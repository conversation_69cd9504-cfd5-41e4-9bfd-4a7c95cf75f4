<template>
    <page-header-wrapper>
        <a-card :bordered="false">
            <a-card :bordered="false">
                <div class="table-page-search-wrapper">
                    <a-form layout="inline">
                        <a-row :gutter="48">
                            <a-col :md="12" :lg="6" :sm="24">
                                <a-form-item label="用户Id">
                                    <a-input v-model="queryParam.userId" style="width: 100%" placeholder="请输入用户Id" />
                                </a-form-item>
                            </a-col>
                            <a-col :md="12" :lg="6" :sm="24">
                                <a-form-item label="真实姓名">
                                    <a-input v-model="queryParam.realName" style="width: 100%" placeholder="请输入真实姓名" />
                                </a-form-item>
                            </a-col>
                            <a-col :md="12" :lg="6" :sm="24">
                                <a-form-item label="手机号">
                                    <a-input v-model="queryParam.phone" style="width: 100%" placeholder="请输入手机号" />
                                </a-form-item>
                            </a-col>
                            <a-col :md="12" :lg="8" :sm="24">
                                <a-form-item>
                                    <span class="table-page-search-submitButtons">
                                        <a-button @click="resetQueryParam" icon="redo">重置</a-button>
                                        <a-button type="primary" icon="search" style="margin-left: 8px"
                                            @click="queryParam.pageNum = 1, getList()">查询</a-button>
                                        <a-button type="primary" style="margin-left: 8px"
                                            @click="openUpdateFundDialog()">修改用户可提现金额</a-button>
                                    </span>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </div>
            </a-card>
            <div style="margin-bottom: 10px;">总资金：{{ withdrawableObj.totalAmount }}，不可提现金额：{{
                withdrawableObj.nonWithdrawableAmount }}，可提现金额：{{ withdrawableObj.withdrawableAmount }}</div>
            <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist"
                rowKey="id">
                <span slot="isUsed" slot-scope="text,record">
                    <template>
                        <a-tag :color="record.isUsed == 0 ? 'red' : 'green'">{{ record.isUsed == 0 ? '未全部使用' : '已全部使用'
                            }}</a-tag>
                    </template>
                </span>
                <span slot="sourceType" slot-scope="text,record">
                    <template>
                        <div v-if="record.sourceType == 'RECHARGE'">充值</div>
                        <div v-if="record.sourceType == 'POSITION_SALE'">卖出股票</div>
                        <div v-if="record.sourceType == 'WITHDRAW_REJECT'">提现驳回返还</div>
                        <div v-if="record.sourceType == 'WITHDRAW_CANCEL'">提现取消返还</div>
                        <div v-if="record.sourceType == 'ADMIN_OPERATION'">管理员操作</div>
                        <div v-if="record.sourceType == 'ADMIN_DEPOSIT'">人工上分</div>
                        <div v-if="record.sourceType == 'ADMIN_WITHDRAW'">人工下分</div>
                    </template>
                </span>
                <template slot="action" slot-scope="text,record">
                    <a href="javascript:;" @click="viewConsumptionRecords(record)">查看</a>
                </template>
            </a-table>

            <a-modal title="修改用户可提现金额" :width="640" :visible="updateFundDialog"
                :confirmLoading="updateFundDialogLoading" :maskClosable="false" @ok="submitUpdateFund"
                @cancel="cancelUpdateFund">
                <a-form :form="updateFundForm" ref="updateFundForm">
                    <a-row :gutter="48">
                        <a-col :md="24" :lg="24" :sm="24">
                            <a-form-item label="用户ID" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
                                <a-input-number placeholder="请输入用户ID" style="width: 100%"
                                    v-decorator="['userId', { rules: [{ required: true, message: '请输入用户ID' }] }]" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="48">
                        <a-col :md="24" :lg="24" :sm="24">
                            <a-form-item label="操作类型" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
                                <a-select placeholder="请选择操作类型"
                                    v-decorator="['operationType', { rules: [{ required: true, message: '请选择操作类型' }] }]">
                                    <a-select-option :value="1">增加可提现金额</a-select-option>
                                    <a-select-option :value="2">减少可提现金额</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="48">
                        <a-col :md="24" :lg="24" :sm="24">
                            <a-form-item label="操作金额" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
                                <a-input-number placeholder="请输入操作金额" style="width: 100%" :min="0.01" :precision="2"
                                    v-decorator="['amount', { rules: [{ required: true, message: '请输入操作金额' }] }]" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="48">
                        <a-col :md="24" :lg="24" :sm="24">
                            <a-form-item label="备注信息" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
                                <a-textarea placeholder="请输入备注信息（可选）" :rows="3" v-decorator="['remark']" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </a-modal>

            <a-modal title="资金消费记录" :width="1200" :visible="consumptionDialog" :maskClosable="false"
                @cancel="closeConsumptionDialog" :footer="null">
                <a-table bordered :loading="consumptionLoading" :columns="consumptionColumns"
                    :data-source="consumptionList" rowKey="id" :pagination="false">
                    <span slot="consumptionType" slot-scope="text,record">
                        <template>
                            <div v-if="record.consumptionType === 'POSITION_BUY'">购买股票</div>
                            <div v-else-if="record.consumptionType === 'WITHDRAW'">提现</div>
                            <div v-else-if="record.consumptionType === 'OTHER'">其他</div>
                            <div v-else>{{ record.consumptionType }}</div>
                        </template>
                    </span>
                    <span slot="sourceType" slot-scope="text,record">
                        <template>
                            <div v-if="record.sourceType == 'RECHARGE'">充值</div>
                            <div v-if="record.sourceType == 'POSITION_SALE'">卖出股票</div>
                            <div v-if="record.sourceType == 'WITHDRAW_REJECT'">提现驳回返还</div>
                            <div v-if="record.sourceType == 'WITHDRAW_CANCEL'">提现取消返还</div>
                            <div v-if="record.sourceType == 'ADMIN_OPERATION'">管理员操作</div>
                            <div v-if="record.sourceType == 'ADMIN_DEPOSIT'">人工上分</div>
                            <div v-if="record.sourceType == 'ADMIN_WITHDRAW'">人工下分</div>
                        </template>
                    </span>
                    <span slot="consumptionAmount" slot-scope="text">
                        <template>
                            <span style="color: #f5222d;">{{ text }}</span>
                        </template>
                    </span>
                </a-table>
            </a-modal>
        </a-card>
    </page-header-wrapper>
</template>
<script>
import { fundList, getWithdrawableAmount, updateUserFund, fundConsumptionBySource } from '@/api/capital'
import moment from 'moment'
export default {
    name: 'FundTransferrecord',
    data() {
        return {
            columns: [
                {
                    title: '用户名称（ID）',
                    dataIndex: 'realName',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return `${row.realName}（${row.userId}）`
                    }
                },
                {
                    title: '资金来源类型',
                    dataIndex: 'sourceType',
                    align: 'center',
                    scopedSlots: { customRender: 'sourceType' }
                },
                {
                    title: '原始金额',
                    dataIndex: 'amount',
                    align: 'center'
                },
                {
                    title: '剩余金额',
                    dataIndex: 'remainingAmount',
                    align: 'center'
                },
                {
                    title: '关联单号',
                    dataIndex: 'relatedOrderSn',
                    align: 'center'
                },
                {
                    title: '全部使用',
                    dataIndex: 'isUsed',
                    align: 'center',
                    scopedSlots: { customRender: 'isUsed' }
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    align: 'center'
                },
                {
                    title: '创建时间',
                    dataIndex: 'createTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'center',
                    width: 100,
                    scopedSlots: { customRender: 'action' }
                }
            ],
            pagination: {
                total: 0,
                pageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize),
                onChange: (page, pageSize) => this.onPageChange(page, pageSize),
                showTotal: (total) => `共有 ${total} 条数据`
            },
            loading: false,
            queryParam: {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                realName: '',
                phone: ''
            },
            datalist: [],
            withdrawableObj: {},
            updateFundDialog: false,
            updateFundDialogLoading: false,
            updateFundForm: this.$form.createForm(this),
            consumptionDialog: false,
            consumptionLoading: false,
            consumptionColumns: [
                {
                    title: '消费类型',
                    dataIndex: 'consumptionType',
                    align: 'center',
                    scopedSlots: { customRender: 'consumptionType' }
                },
                {
                    title: '资金来源类型',
                    dataIndex: 'sourceType',
                    align: 'center',
                    scopedSlots: { customRender: 'sourceType' }
                },
                {
                    title: '消费金额',
                    dataIndex: 'consumptionAmount',
                    align: 'center',
                    scopedSlots: { customRender: 'consumptionAmount' }
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    align: 'center'
                },
                {
                    title: '创建时间',
                    dataIndex: 'createTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                }
            ],
            consumptionList: []
        }
    },
    created() {
        this.getList()
    },
    methods: {
        getWithdrawableAmountData(fn) {
            getWithdrawableAmount(this.queryParam).then((res) => {
                this.withdrawableObj = res.data
                fn()
            })
        },
        resetQueryParam() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                realName: '',
                phone: ''
            }
        },
        getList() {
            var that = this
            this.loading = true
            this.getWithdrawableAmountData(() => {
                fundList(this.queryParam).then((res) => {
                    this.datalist = res.data.list
                    this.pagination.total = res.data.total
                    that.loading = false
                })
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.pagination.pageSize = pageSize
            this.getList()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.pagination.pageSize = pageSize
            this.getList()
        },
        openUpdateFundDialog() {
            this.updateFundDialog = true
        },
        submitUpdateFund(e) {
            e.preventDefault()
            this.updateFundForm.validateFields((err, values) => {
                if (!err) {
                    this.updateFundDialogLoading = true
                    updateUserFund(values).then((res) => {
                        if (res.status === 0) {
                            this.$message.success({ content: res.msg, duration: 2 })
                            this.updateFundDialog = false
                            this.updateFundForm.resetFields()
                            this.getList()
                        } else {
                            this.$message.error({ content: res.msg })
                        }
                        this.updateFundDialogLoading = false
                    }).catch((error) => {
                        this.$message.error({ content: '修改用户可提现金额失败' })
                        this.updateFundDialogLoading = false
                    })
                }
            })
        },
        cancelUpdateFund() {
            this.updateFundDialog = false
            this.updateFundForm.resetFields()
        },
        viewConsumptionRecords(record) {
            this.consumptionDialog = true
            this.consumptionLoading = true

            fundConsumptionBySource({ fundSourceId: record.id }).then((res) => {
                this.consumptionList = res.data || []
                this.consumptionLoading = false
            }).catch((error) => {
                this.$message.error({ content: '获取消费记录失败' })
                this.consumptionLoading = false
            })
        },
        closeConsumptionDialog() {
            this.consumptionDialog = false
            this.consumptionLoading = false
        }
    }
}
</script>
<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
</style>
