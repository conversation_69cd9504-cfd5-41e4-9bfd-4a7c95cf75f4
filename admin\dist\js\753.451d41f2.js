(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[753],{6250:function(e,t,a){"use strict";a.d(t,{B0:function(){return d},Eu:function(){return u},Gd:function(){return l},I1:function(){return y},Jn:function(){return i},O8:function(){return h},U1:function(){return g},Yt:function(){return p},cI:function(){return m},i_:function(){return c},mP:function(){return f},uJ:function(){return v},uo:function(){return w},zu:function(){return b}});var r=a(75769),n=a(55373),s=a.n(n),o={subscribelist:"/admin/subscribe/list.do",subscribeadd:"/admin/subscribe/add.do",subscribeupdate:"/admin/subscribe/update.do",subscribedel:"/admin/subscribe/del.do",getStockSubscribeList:"admin/subscribe/getStockSubscribeList.do",saveStockSubscribe:"/admin/subscribe/saveStockSubscribe.do",delStockSubscribe:"admin/subscribe/delStockSubscribe.do",addUserPosition:"/admin/position/addUserPosition.do",getStockSubscribeQcListByAdmin:"/admin/subscribe/getStockSubscribeQcListByAdmin.do",addStockSubscribeQcByAdmin:"/admin/subscribe/addStockSubscribeQcByAdmin.do",updateStockSubscribeQcByAdmin:"admin/subscribe/updateStockSubscribeQcByAdmin.do",getDzListByAdmin:"/admin/stockDz/getDzListByAdmin.do",addByAdmin:"/admin/stockDz/addByAdmin.do",updateByAdmin:"/admin/stockDz/updateByAdmin.do",deleteByAdmin:"/admin/stockDz/deleteByAdmin.do",virtualOneClickWin:"/admin/subscribe/virtualOneClickWin.do",addForUser:"/admin/subscribe/addForUser.do"};function i(e){return(0,r.Ay)({url:o.subscribelist,method:"post",data:s().stringify(e)})}function l(e){return(0,r.Ay)({url:o.subscribeadd,method:"post",data:s().stringify(e)})}function u(e){return(0,r.Ay)({url:o.subscribeupdate,method:"post",data:s().stringify(e)})}function c(e){return(0,r.Ay)({url:o.subscribedel,method:"post",data:s().stringify(e)})}function d(e){return(0,r.Ay)({url:o.getStockSubscribeList,method:"post",data:s().stringify(e)})}function m(e){return(0,r.Ay)({url:o.saveStockSubscribe,method:"post",data:s().stringify(e)})}function p(e){return(0,r.Ay)({url:o.delStockSubscribe,method:"post",data:s().stringify(e)})}function g(e){return(0,r.Ay)({url:o.addUserPosition,method:"post",data:s().stringify(e)})}function f(){return(0,r.Ay)({url:o.virtualOneClickWin,method:"post"})}function b(e){return(0,r.Ay)({url:o.getDzListByAdmin,method:"post",data:s().stringify(e)})}function h(e){return(0,r.Ay)({url:o.addByAdmin,method:"post",data:s().stringify(e)})}function y(e){return(0,r.Ay)({url:o.updateByAdmin,method:"post",data:s().stringify(e)})}function v(e){return(0,r.Ay)({url:o.deleteByAdmin,method:"post",data:s().stringify(e)})}function w(e){return(0,r.Ay)({url:o.addForUser,method:"post",data:s().stringify(e)})}},40753:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return f}});var r=function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"table-page-search-wrapper"},[t("a-form",{attrs:{layout:"inline"}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户筛选"}},[t("a-select",{attrs:{placeholder:"请选择用户类型","default-value":{key:"0"}},model:{value:e.queryParam.accountType,callback:function(t){e.$set(e.queryParam,"accountType",t)},expression:"queryParam.accountType"}},[t("a-select-option",{attrs:{value:0}},[e._v("真实用户")]),t("a-select-option",{attrs:{value:1}},[e._v("模拟用户")])],1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"状态"}},[t("a-select",{attrs:{placeholder:"请选择状态","default-value":{key:"0"}},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[t("a-select-option",{attrs:{value:1}},[e._v("待中签")]),t("a-select-option",{attrs:{value:2}},[e._v("未中签")]),t("a-select-option",{attrs:{value:3}},[e._v("已中签")]),t("a-select-option",{attrs:{value:4}},[e._v("已缴纳")]),t("a-select-option",{attrs:{value:5}},[e._v("已转持仓")])],1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"真实姓名"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写真实姓名"},model:{value:e.queryParam.realName,callback:function(t){e.$set(e.queryParam,"realName",t)},expression:"queryParam.realName"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户手机"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写用户手机号"},model:{value:e.queryParam.phone,callback:function(t){e.$set(e.queryParam,"phone",t)},expression:"queryParam.phone"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",[t("span",{staticClass:"table-page-search-submitButtons"},[t("a-button",{attrs:{icon:"redo"},on:{click:e.getqueryParam}},[e._v(" 重置")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){e.queryParam.pageNum=1,e.pagination.current=1,e.getlist()}}},[e._v("查询 ")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(t){return e.virtualOneClickWinEvent()}}},[e._v("模拟持仓一键中签")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(t){return e.openNewStockDialog()}}},[e._v("新股申购")])],1)])],1)],1)],1)],1)]),t("a-card",{attrs:{bordered:!1}},[t("a-table",{attrs:{bordered:"",loading:e.loading,pagination:e.pagination,columns:e.columns,"data-source":e.datalist,rowKey:"id"},scopedSlots:e._u([{key:"realName",fn:function(a,r){return t("span",{},[[t("div",[t("span",{staticStyle:{"margin-right":"10px"}},[e._v(e._s(r.realName)+"("+e._s(r.userId)+")")])])]],2)}},{key:"status",fn:function(a,r){return t("span",{},[[t("div",[t("a-tag",{staticStyle:{"margin-right":"10px"},attrs:{color:1==r.status?"pink":2==r.status?"red":3==r.status?"green":4==r.status?"blue":5==r.status?"purple":"orange"}},[e._v(" "+e._s(1==r.status?"待中签":2==r.status?"未中签":3==r.status?"已中签":4==r.status?"已缴纳":5==r.status?"已转持仓":""))])],1)]],2)}},{key:"action",fn:function(a,r){return[1==r.status||2==r.status||3==r.status?t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.geteditStock(r)}},slot:"action"},[e._v(e._s("中签设置"))]):e._e(),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.getdeleteStock(r)}},slot:"action"},[e._v(e._s("删除记录"))]),t("a-divider",{attrs:{type:"vertical"}}),4==r.status?t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.getzhuan(r)}},slot:"action"},[e._v(e._s("转持仓"))]):e._e()]}}])})],1),t("a-modal",{attrs:{title:(e.currentdetail,"中签设置"),width:700,visible:e.addUserdialog,confirmLoading:e.addUserDialogloading},on:{ok:e.OkaddUserdialog,cancel:e.CanceladdUserdialog}},[t("a-form",{ref:"addUserform",attrs:{form:e.addUserform}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"用户手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0,message:"请输入用户手机号"}]}],expression:"['phone', { rules: [{ required: true, message: '请输入用户手机号', }] }]"}],attrs:{disabled:"disabled",placeholder:"请输入用户手机号"}})],1)],1),t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"新股代码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["newCode",{rules:[{required:!0,message:"请输入新股代码"}]}],expression:"['newCode', { rules: [{ required: true, message: '请输入新股代码', }] }]"}],attrs:{disabled:"disabled",placeholder:"请输入新股代码"}})],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"最大买入数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["applyNums",{rules:[{required:!0,message:"请输入买入数量"}]}],expression:"['applyNums', { rules: [{ required: true, message: '请输入买入数量', }] }]"}],attrs:{disabled:"disabled",placeholder:"请输入买入数量"}})],1)],1),t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"中签数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["applyNumber",{}],expression:"['applyNumber', {}]"}],attrs:{placeholder:"请输入中签数量"}})],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{rules:[{required:!0,message:"请选择状态"}]}],expression:"['status', { rules: [{ required: true, message: '请选择状态', }] }]"}],attrs:{placeholder:"请选择状态"}},[t("a-select-option",{attrs:{value:2}},[e._v("未中签")]),t("a-select-option",{attrs:{value:3}},[e._v("已中签")]),t("a-select-option",{attrs:{value:4}},[e._v("已缴纳")]),t("a-select-option",{attrs:{value:5}},[e._v("转持仓")])],1)],1)],1)],1)],1)],1),t("a-modal",{attrs:{title:"新股申购",width:640,visible:e.newStockDialog,confirmLoading:e.newStockDialogLoading,maskClosable:!1},on:{ok:e.submitNewStock,cancel:e.cancelNewStock}},[t("a-form",{ref:"newStockForm",attrs:{form:e.newStockForm}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"用户手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userPhone",{rules:[{required:!0,message:"请输入用户手机号"}]}],expression:"['userPhone', { rules: [{ required: true, message: '请输入用户手机号' }] }]"}],attrs:{placeholder:"请输入用户手机号"}})],1)],1),t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"新股代码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["newCode",{rules:[{required:!0,message:"请输入新股代码"}]}],expression:"['newCode', { rules: [{ required: true, message: '请输入新股代码' }] }]"}],attrs:{placeholder:"请输入新股代码"}})],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"申购数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["applyNums",{rules:[{required:!0,message:"请输入申购数量"}]}],expression:"['applyNums', { rules: [{ required: true, message: '请输入申购数量' }] }]"}],staticStyle:{width:"100%"},attrs:{placeholder:"请输入申购数量"}})],1)],1),t("a-col",{attrs:{md:12,lg:12,sm:12}},[t("a-form-item",{attrs:{label:"申购类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["type",{rules:[{required:!0,message:"请选择申购类型"}]}],expression:"['type', { rules: [{ required: true, message: '请选择申购类型' }] }]"}],attrs:{placeholder:"请选择申购类型"}},[t("a-select-option",{attrs:{value:1}},[e._v("普通申购")]),t("a-select-option",{attrs:{value:2}},[e._v("需要扣款申购")])],1)],1)],1)],1)],1)],1)],1)},n=[],s=a(55464),o=a(56252),i=(a(26099),a(23500),a(6250)),l=a(91863),u=a.n(l),c=a(80157),d={name:"Shares",data:function(){var e=this;return{columns:[{title:"用户名称（ID）",dataIndex:"realName",align:"center",scopedSlots:{customRender:"realName"}},{title:"代理名称",dataIndex:"agentName",align:"center"},{title:"用户手机号",dataIndex:"phone",align:"center"},{title:"新股名称",dataIndex:"newName",align:"center"},{title:"新股代码",dataIndex:"newCode",align:"center"},{title:"买入价格",dataIndex:"buyPrice",align:"center"},{title:"买入数量",dataIndex:"applyNums",align:"center"},{title:"中签数量",dataIndex:"applyNumber",align:"center"},{title:"申购时间",dataIndex:"addTime",align:"center"},{title:"状态",dataIndex:"status",align:"center",scopedSlots:{customRender:"status"}},{title:"操作",key:"action",align:"center",scopedSlots:{customRender:"action"}}],pagination:{total:0,current:1,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,keyword:"",realName:"",accountType:"",status:""},queryParamV2:{agentId:void 0,realName:"",phone:"",pageNum:1,pageSize:10},datalist:[],labelCol:{xs:{span:8},sm:{span:8},md:{span:8}},wrapperCol:{xs:{span:14},sm:{span:14},md:{span:14}},addUserform:this.$form.createForm(this),addUserdialog:!1,addUserDialogloading:!1,fields:["phone","newCode","applyNums","applyNumber","status"],currentdetail:"",currentTime:"",polling:!0,userInfo:{},userParam:{agentId:void 0,realName:"",phone:"",pageNum:1,pageSize:10,allBuyNum:0},newStockDialog:!1,newStockDialogLoading:!1,newStockForm:this.$form.createForm(this)}},created:function(){this.getlist(),this.startPolling()},beforeDestroy:function(){this.stopPolling()},methods:{virtualOneClickWinEvent:function(){var e=this;(0,i.mP)().then((function(t){0==t.status?e.$message.success({content:t.msg,duration:2}):e.$message.error({content:t.msg})}))},getUserInfo:function(e){var t=this;return(0,o.A)((0,s.A)().mark((function a(){var r;return(0,s.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=t,t.userParam.phone=e.phone,a.next=4,(0,c.Cv)(t.userParam).then((function(a){r.userInfo=a.data.list[0],r.userInfo&&(t.allBuyNum=Math.floor(r.userInfo.enableAmt/e.buyPrice),3==e.status||4==e.status||5==e.status?t.addUserform.setFieldsValue({applyNumber:e.applyNumber}):t.addUserform.setFieldsValue({applyNumber:e.applyNums}),t.addUserform.setFieldsValue({applyNums:e.applyNums}),1==e.status||2==e.status||3==e.status?t.addUserform.setFieldsValue({status:3}):t.addUserform.setFieldsValue({status:4}),console.log(t.allBuyNum))}));case 4:case"end":return a.stop()}}),a)})))()},startPolling:function(){this.poll()},poll:function(){this.polling&&(this.getlist(),console.log("执行轮询任务..."),this.currentTime=(new Date).toLocaleTimeString(),setTimeout(this.poll,2e4))},stopPolling:function(){this.polling=!1,console.log("停止轮询")},getzhuan:function(e){var t=this;this.$confirm({title:"提示",content:"确认转入持仓吗？此操作不可恢复！",onOk:function(){var a={id:e.id};(0,i.U1)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getlist()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},getdeleteStock:function(e){var t=this;this.$confirm({title:"提示",content:"确认删除该新股吗？此操作不可恢复！",onOk:function(){var a={id:e.id};(0,i.Yt)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getinit()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},geteditStock:function(e){var t=this;this.getUserInfo(e),console.log("asd",e),this.currentdetail=e,this.addUserdialog=!0,this.fields.forEach((function(e){return t.addUserform.getFieldDecorator(e)})),this.addUserform.setFieldsValue(u()(e,this.fields))},CanceladdUserdialog:function(){this.addUserdialog=!1;var e=this.$refs.addUserform.form;e.resetFields()},OkaddUserdialog:function(){var e=this,t=this.$refs.addUserform.form;t.validateFields((function(a,r){a||(""!=e.currentdetail&&(r.id=e.currentdetail.id),e.addUserDialogloading=!0,(0,i.cI)(r).then((function(a){0==a.status?(e.addUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getlist()):e.$message.error({content:a.msg}),e.addUserDialogloading=!1})))}))},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,keyword:"",realName:"",accountType:"",status:""},this.getlist()},getinit:function(){this.getqueryParam(),this.pagination.current=1,this.getlist()},getlist:function(){var e=this;this.loading=!0,(0,i.B0)(this.queryParam).then((function(t){e.datalist=t.data.list,e.pagination.total=t.data.total,e.loading=!1}))},onPageChange:function(e,t){this.queryParam.pageNum=e,this.pagination.current=e,this.getlist()},onSizeChange:function(e,t){this.queryParam.pageNum=e,this.pagination.current=e,this.queryParam.pageSize=t,this.getlist()},openNewStockDialog:function(){this.newStockDialog=!0},submitNewStock:function(){var e=this,t=this.$refs.newStockForm.form;t.validateFields((function(t,a){t||(e.newStockDialogLoading=!0,(0,i.uo)(a).then((function(t){0===t.status?(e.$message.success({content:t.msg,duration:2}),e.newStockDialog=!1,e.newStockForm.resetFields(),e.getlist()):e.$message.error({content:t.msg}),e.newStockDialogLoading=!1})).catch((function(t){e.$message.error({content:"新股申购失败"}),e.newStockDialogLoading=!1})))}))},cancelNewStock:function(){this.newStockDialog=!1,this.newStockForm.resetFields()}}},m=d,p=a(81656),g=(0,p.A)(m,r,n,!1,null,null,null),f=g.exports},91863:function(e,t,a){var r=1/0,n=9007199254740991,s="[object Arguments]",o="[object Function]",i="[object GeneratorFunction]",l="[object Symbol]",u="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,c="object"==typeof self&&self&&self.Object===Object&&self,d=u||c||Function("return this")();function m(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function p(e,t){var a=-1,r=e?e.length:0,n=Array(r);while(++a<r)n[a]=t(e[a],a,e);return n}function g(e,t){var a=-1,r=t.length,n=e.length;while(++a<r)e[n+a]=t[a];return e}var f=Object.prototype,b=f.hasOwnProperty,h=f.toString,y=d.Symbol,v=f.propertyIsEnumerable,w=y?y.isConcatSpreadable:void 0,S=Math.max;function k(e,t,a,r,n){var s=-1,o=e.length;a||(a=P),n||(n=[]);while(++s<o){var i=e[s];t>0&&a(i)?t>1?k(i,t-1,a,r,n):g(n,i):r||(n[n.length]=i)}return n}function C(e,t){return e=Object(e),N(e,t,(function(t,a){return a in e}))}function N(e,t,a){var r=-1,n=t.length,s={};while(++r<n){var o=t[r],i=e[o];a(i,o)&&(s[o]=i)}return s}function A(e,t){return t=S(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,n=S(a.length-t,0),s=Array(n);while(++r<n)s[r]=a[t+r];r=-1;var o=Array(t+1);while(++r<t)o[r]=a[r];return o[t]=s,m(e,this,o)}}function P(e){return x(e)||_(e)||!!(w&&e&&e[w])}function q(e){if("string"==typeof e||z(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}function _(e){return D(e)&&b.call(e,"callee")&&(!v.call(e,"callee")||h.call(e)==s)}var x=Array.isArray;function U(e){return null!=e&&B(e.length)&&!F(e)}function D(e){return $(e)&&U(e)}function F(e){var t=I(e)?h.call(e):"";return t==o||t==i}function B(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=n}function I(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function $(e){return!!e&&"object"==typeof e}function z(e){return"symbol"==typeof e||$(e)&&h.call(e)==l}var O=A((function(e,t){return null==e?{}:C(e,p(k(t,1),q))}));e.exports=O}}]);