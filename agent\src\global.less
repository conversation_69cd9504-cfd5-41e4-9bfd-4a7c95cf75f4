@import '~ant-design-vue/es/style/themes/default.less';

html,
body,
#app,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout.layout-basic {
  height: 100vh;
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
}

ul,
ol {
  list-style: none;
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}
// 数据列表 操作
.table-operator {
  margin-bottom: 18px;

  button {
    margin-right: 8px;
  }
}
// 数据列表 搜索条件
.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-right: 0;
      margin-bottom: 24px;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        width: auto;
        padding-right: 8px;
        line-height: 32px;
      }

      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}
