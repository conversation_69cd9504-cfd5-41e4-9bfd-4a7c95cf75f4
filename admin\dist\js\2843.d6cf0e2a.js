(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[2843],{5419:function(t){t.exports=function(t,e,a,r){var n="undefined"!==typeof r?[r,t]:[t],i=new Blob(n,{type:a||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(i,e);else{var o=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(i):window.webkitURL.createObjectURL(i),s=document.createElement("a");s.style.display="none",s.href=o,s.setAttribute("download",e),"undefined"===typeof s.download&&s.setAttribute("target","_blank"),document.body.appendChild(s),s.click(),setTimeout((function(){document.body.removeChild(s),window.URL.revokeObjectURL(o)}),200)}}},42843:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return y}});var r=function(){var t=this,e=t._self._c;return e("page-header-wrapper",[e("a-card",{attrs:{bordered:!1}},[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户筛选"}},[e("a-select",{attrs:{placeholder:"请选择用户类型","default-value":{key:"0"}},on:{change:t.accountTypeChangeEvent},model:{value:t.queryParam.accountType,callback:function(e){t.$set(t.queryParam,"accountType",e)},expression:"queryParam.accountType"}},[e("a-select-option",{attrs:{value:0}},[t._v("真实用户")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟用户")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"提现状态"}},[e("a-select",{attrs:{placeholder:"请选择提现状态"},model:{value:t.queryParam.state,callback:function(e){t.$set(t.queryParam,"state",e)},expression:"queryParam.state"}},[e("a-select-option",{attrs:{value:0}},[t._v("审核中")]),e("a-select-option",{attrs:{value:1}},[t._v("出金成功")]),e("a-select-option",{attrs:{value:2}},[t._v("出金失败")]),e("a-select-option",{attrs:{value:3}},[t._v("出金取消")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist,change:t.agentIdChangeEvent},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,r){return e("a-select-option",{key:r,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"手机号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入手机号"},model:{value:t.queryParam.phone,callback:function(e){t.$set(t.queryParam,"phone",e)},expression:"queryParam.phone"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"真实姓名"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入真实姓名"},model:{value:t.queryParam.realName,callback:function(e){t.$set(t.queryParam,"realName",e)},expression:"queryParam.realName"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",{attrs:{label:"出金时间"}},[e("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD HH:mm:ss","show-time":{format:"HH:mm:ss"},ranges:t.dateRanges},on:{change:t.onChangeRangeDate},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1)],1),e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){return t.queryToday()}}},[t._v("查询今日")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){return t.queryYesterday()}}},[t._v("查询昨日")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"vertical-align-bottom"},on:{click:t.getexport}},[t._v("导出搜索数据 ")])],1)])],1)],1)],1)],1)]),e("div",[t._v(t._s(1==t.queryParam.accountType?"模拟用户":"真实用户")+" - 今日提现："+t._s(t.totalResult.todayWithdrawAmount)+"，"+t._s(1==t.queryParam.accountType?"模拟用户":"真实用户")+" - 总提现："+t._s(t.totalResult.totalWithdrawAmount))]),e("a-table",{staticStyle:{"margin-top":"10px"},attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2500}},scopedSlots:t._u([{key:"withStatus",fn:function(a,r){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:0==r.withStatus?"blue":1==r.withStatus?"green":2==r.withStatus?"red":"orange"}},[t._v(" "+t._s(0==r.withStatus?"待打款":1==r.withStatus?"提现成功":2==r.withStatus?"提现失败":"提现取消")+" ")])],1)]],2)}},{key:"action",fn:function(a,r){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getEditorder(r.id)}},slot:"action"},[t._v("修改提现记录")])]}}])})],1),e("a-modal",{attrs:{title:"修改订单状态",width:500,visible:t.editOrderdialog,confirmLoading:t.editOrderDialogloading},on:{ok:t.OkeditOrderdialog,cancel:t.CanceleditOrderdialog}},[e("a-form",{ref:"editOrderform",attrs:{form:t.editOrderform}},[e("a-form-item",{attrs:{label:"订单ID"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withId",{rules:[{type:"number",required:!0,message:"请输入锁仓原因！"}]}],expression:"['withId', { rules: [{ type: 'number', required: true, message: '请输入锁仓原因！', }] }]"}],attrs:{disabled:""}})],1),e("a-form-item",{attrs:{label:"提现状态"}},[e("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["state",{rules:[{required:!0,message:"请选择提现状态"}]}],expression:"['state', { rules: [{ required: true, message: '请选择提现状态', }] }]"}],attrs:{placeholder:"请选择提现状态"}},[e("a-select-option",{attrs:{value:"1"}},[t._v("提现成功")]),e("a-select-option",{attrs:{value:"2"}},[t._v("提现失败")])],1)],1),2==t.editOrderform.getFieldValue("state")?e("a-form-item",{attrs:{label:"失败原因"}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["authMsg",{rules:[{required:!0,message:"请输入提现失败原因！"}]}],expression:"['authMsg', { rules: [{ required: true, message: '请输入提现失败原因！', }] }]"}]})],1):t._e()],1)],1)],1)},n=[],i=(a(28706),a(26099),a(23500),a(60804)),o=a(80157),s=a(91863),l=a.n(s),u=a(5419),d=a.n(u),c=a(95093),m=a.n(c),g={name:"Withdrawallist",data:function(){var t=this;return{dateRanges:{"今天":[m()().startOf("day"),m()().endOf("day")],"昨天":[m()().subtract(1,"days").startOf("day"),m()().subtract(1,"days").endOf("day")],"本周":[m()().startOf("week"),m()().endOf("week")],"本月":[m()().startOf("month"),m()().endOf("month")]},columns:[{title:"订单ID",dataIndex:"id",align:"center",width:80},{title:"手机号",dataIndex:"phone",align:"center",width:150,customRender:function(t,e,a){if(!t)return"";var r=String(t);return r.substr(0,3)+"****"+r.substr(7)}},{title:"用户名称（ID）",dataIndex:"nickName",align:"center",key:"receiverName",width:180,customRender:function(t,e,a){return"".concat(e.nickName,"（").concat(e.userId,"）")}},{title:"代理用户",dataIndex:"agentName",align:"center",width:100},{title:"出金金额",dataIndex:"withAmt",align:"center",width:120},{title:"状态",dataIndex:"withStatus",align:"center",width:120,scopedSlots:{customRender:"withStatus"}},{title:"原因",dataIndex:"withMsg",align:"center",width:200},{title:"收款人姓名",dataIndex:"nickName",align:"center",width:120},{title:"提现银行",dataIndex:"bankName",align:"center",width:180},{title:"银行号码",dataIndex:"bankNo",align:"center",width:180},{title:"代理ID",dataIndex:"agentId",align:"center",width:80},{title:"应转金额",dataIndex:"payChannel",align:"center",width:120,customRender:function(t,e,a){return e.withAmt-e.withFee}},{title:"手续费",dataIndex:"withFee",align:"center",width:80},{title:"提现支行",dataIndex:"bankAddress",align:"center",width:200},{title:"申请时间",dataIndex:"applyTime",align:"center",width:180,customRender:function(t,e,a){return t?m()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"出金时间",dataIndex:"transTime",align:"center",width:180,customRender:function(t,e,a){return t?m()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作者",dataIndex:"operator",align:"center",width:120},{title:"操作",key:"action",align:"center",width:180,fixed:"right",scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,agentId:void 0,state:void 0,userId:"",realName:"",beginTime:"",endTime:"",phone:"",accountType:void 0},datalist:[],agentlist:[],agentloading:!1,times:[],editOrderform:this.$form.createForm(this),editOrderdialog:!1,editOrderDialogloading:!1,fields:["withId","state"],agentqueryParam:{pageNum:1,pageSize:100},totalResult:{}}},created:function(){this.getlist(),this.withdrawCountRechargeAmountEvent()},methods:{queryToday:function(){this.times=[m()().startOf("day"),m()().endOf("day")],this.queryParam.beginTime=m()(this.times[0]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.endTime=m()(this.times[1]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.pageNum=1,this.getlist()},queryYesterday:function(){var t=m()().subtract(1,"days").format("YYYY-MM-DD");this.times=[m()(t+" 00:00:00","YYYY-MM-DD HH:mm:ss"),m()(t+" 23:59:59","YYYY-MM-DD HH:mm:ss")],this.queryParam.beginTime=m()(this.times[0]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.endTime=m()(this.times[1]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.pageNum=1,this.getlist()},withdrawCountRechargeAmountEvent:function(){var t=this;(0,i.ZB)({accountType:this.queryParam.accountType,agentId:this.queryParam.agentId}).then((function(e){t.totalResult=e.data}))},accountTypeChangeEvent:function(){this.withdrawCountRechargeAmountEvent()},agentIdChangeEvent:function(t){this.withdrawCountRechargeAmountEvent()},getexport:function(){(0,i.VW)(this.queryParam).then((function(t){d()(t,"提现列表.xls")}))},getEditorder:function(t){var e=this;this.editOrderdialog=!0,this.fields.forEach((function(t){return e.editOrderform.getFieldDecorator(t)})),this.editOrderform.setFieldsValue(l()({withId:t},this.fields))},OkeditOrderdialog:function(){var t=this,e=this.$refs.editOrderform.form;e.validateFields((function(a,r){a||(t.editOrderDialogloading=!0,(0,i.$Q)(r).then((function(a){0==a.status?(t.$message.success({content:a.msg,duration:2}),e.resetFields(),t.getlist()):t.$message.error({content:a.msg}),t.editOrderdialog=!1,t.editOrderDialogloading=!1})).catch((function(t){reject(t)})))}))},CanceleditOrderdialog:function(){this.editOrderdialog=!1;var t=this.$refs.editOrderform.form;t.resetFields()},onChangeRangeDate:function(t,e){if(t&&2===t.length&&t[0]&&t[1]){var a=t[0].clone().startOf("day"),r=t[1].clone().endOf("day");this.times=[a,r],this.queryParam.beginTime=a.format("YYYY-MM-DD HH:mm:ss"),this.queryParam.endTime=r.format("YYYY-MM-DD HH:mm:ss"),console.log("设置的时间范围：",this.queryParam.beginTime,this.queryParam.endTime)}else this.queryParam.beginTime="",this.queryParam.endTime="",this.times=[]},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,agentId:void 0,state:void 0,userId:"",realName:"",beginTime:"",endTime:""},this.times=[]},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,o.vP)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this,e=this;this.loading=!0,(0,i.dh)(this.queryParam).then((function(a){t.datalist=a.data.list,t.pagination.total=a.data.total,setTimeout((function(){e.loading=!1}),500)}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},h=g,f=a(81656),p=(0,f.A)(h,r,n,!1,null,"dba6fd0c",null),y=p.exports},91863:function(t,e,a){var r=1/0,n=9007199254740991,i="[object Arguments]",o="[object Function]",s="[object GeneratorFunction]",l="[object Symbol]",u="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,d="object"==typeof self&&self&&self.Object===Object&&self,c=u||d||Function("return this")();function m(t,e,a){switch(a.length){case 0:return t.call(e);case 1:return t.call(e,a[0]);case 2:return t.call(e,a[0],a[1]);case 3:return t.call(e,a[0],a[1],a[2])}return t.apply(e,a)}function g(t,e){var a=-1,r=t?t.length:0,n=Array(r);while(++a<r)n[a]=e(t[a],a,t);return n}function h(t,e){var a=-1,r=e.length,n=t.length;while(++a<r)t[n+a]=e[a];return t}var f=Object.prototype,p=f.hasOwnProperty,y=f.toString,v=c.Symbol,w=f.propertyIsEnumerable,b=v?v.isConcatSpreadable:void 0,q=Math.max;function P(t,e,a,r,n){var i=-1,o=t.length;a||(a=x),n||(n=[]);while(++i<o){var s=t[i];e>0&&a(s)?e>1?P(s,e-1,a,r,n):h(n,s):r||(n[n.length]=s)}return n}function Y(t,e){return t=Object(t),O(t,e,(function(e,a){return a in t}))}function O(t,e,a){var r=-1,n=e.length,i={};while(++r<n){var o=e[r],s=t[o];a(s,o)&&(i[o]=s)}return i}function I(t,e){return e=q(void 0===e?t.length-1:e,0),function(){var a=arguments,r=-1,n=q(a.length-e,0),i=Array(n);while(++r<n)i[r]=a[e+r];r=-1;var o=Array(e+1);while(++r<e)o[r]=a[r];return o[e]=i,m(t,this,o)}}function x(t){return D(t)||S(t)||!!(b&&t&&t[b])}function k(t){if("string"==typeof t||N(t))return t;var e=t+"";return"0"==e&&1/t==-r?"-0":e}function S(t){return T(t)&&p.call(t,"callee")&&(!w.call(t,"callee")||y.call(t)==i)}var D=Array.isArray;function _(t){return null!=t&&R(t.length)&&!M(t)}function T(t){return H(t)&&_(t)}function M(t){var e=C(t)?y.call(t):"";return e==o||e==s}function R(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=n}function C(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function H(t){return!!t&&"object"==typeof t}function N(t){return"symbol"==typeof t||H(t)&&y.call(t)==l}var j=I((function(t,e){return null==t?{}:Y(t,g(P(e,1),k))}));t.exports=j}}]);