"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[644],{48188:function(e,t,a){a.r(t);var u=a(76338),l=a(95692),n=a(52648),o=a.n(n),d=a(39668),s=a(14868),f=a(91363),A=a(11016),c=a(91771),m=a(89065),r=a(11250),i=a(77844),k={antLocale:l.A,momentName:"zh-cn",momentLocale:o()};t["default"]=(0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)({message:"-","layouts.usermenu.dialog.title":"信息","layouts.usermenu.dialog.content":"您确定要注销吗？","layouts.userLayout.title":"Stock Agent 是西湖区最具影响力的 Web 设计规范"},k),d["default"]),s["default"]),f["default"]),A["default"]),c["default"]),m["default"]),r["default"]),i["default"])}}]);