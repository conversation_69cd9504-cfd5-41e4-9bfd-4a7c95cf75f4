(function(){var e={98490:function(e,t,a){var n=a(71332)["default"],i=a(88498)["default"],s=["class","staticClass","style","staticStyle","attrs"];a(28706),e.exports={functional:!0,render:function(e,t){var a=t._c,o=(t._v,t.data),r=t.children,c=void 0===r?[]:r,l=o.class,d=o.staticClass,u=o.style,m=o.staticStyle,f=o.attrs,p=void 0===f?{}:f,h=i(o,s);return a("svg",n({class:["bx-analyse_svg__icon",l,d],style:[u,m],attrs:Object.assign({viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg",width:"200",height:"200"},p)},h),c.concat([a("defs"),a("path",{attrs:{d:"M85.333 512h85.334a340.736 340.736 0 0199.712-241.621 337.493 337.493 0 01108.458-72.96 346.453 346.453 0 01261.547-1.75 106.155 106.155 0 00106.283 102.998c59.136 0 106.666-47.531 106.666-106.667S805.803 85.333 746.667 85.333c-29.398 0-55.979 11.776-75.222 30.934-103.722-41.515-222.848-40.875-325.76 2.517a423.595 423.595 0 00-135.68 91.264A423.253 423.253 0 00118.7 345.685 426.88 426.88 0 0085.333 512zm741.248 133.205c-17.109 40.619-41.685 77.142-72.96 108.416s-67.797 55.851-108.458 72.96a346.453 346.453 0 01-261.547 1.75 106.155 106.155 0 00-106.283-102.998c-59.136 0-106.666 47.531-106.666 106.667s47.53 106.667 106.666 106.667c29.398 0 55.979-11.776 75.222-30.934A425.173 425.173 0 00512 938.667a425.941 425.941 0 00393.259-260.352A426.325 426.325 0 00938.667 512h-85.334a341.035 341.035 0 01-26.752 133.205z"}}),a("path",{attrs:{d:"M512 318.379c-106.752 0-193.621 86.869-193.621 193.621S405.248 705.621 512 705.621 705.621 618.752 705.621 512 618.752 318.379 512 318.379zm0 301.909c-59.69 0-108.288-48.597-108.288-108.288S452.309 403.712 512 403.712 620.288 452.309 620.288 512 571.691 620.288 512 620.288z"}})]))}}},35358:function(e,t,a){var n={"./af":25177,"./af.js":25177,"./ar":61509,"./ar-dz":41488,"./ar-dz.js":41488,"./ar-kw":58676,"./ar-kw.js":58676,"./ar-ly":42353,"./ar-ly.js":42353,"./ar-ma":24496,"./ar-ma.js":24496,"./ar-ps":6947,"./ar-ps.js":6947,"./ar-sa":82682,"./ar-sa.js":82682,"./ar-tn":89756,"./ar-tn.js":89756,"./ar.js":61509,"./az":95533,"./az.js":95533,"./be":28959,"./be.js":28959,"./bg":47777,"./bg.js":47777,"./bm":54903,"./bm.js":54903,"./bn":61290,"./bn-bd":17357,"./bn-bd.js":17357,"./bn.js":61290,"./bo":31545,"./bo.js":31545,"./br":11470,"./br.js":11470,"./bs":44429,"./bs.js":44429,"./ca":7306,"./ca.js":7306,"./cs":56464,"./cs.js":56464,"./cv":73635,"./cv.js":73635,"./cy":64226,"./cy.js":64226,"./da":93601,"./da.js":93601,"./de":77853,"./de-at":26111,"./de-at.js":26111,"./de-ch":54697,"./de-ch.js":54697,"./de.js":77853,"./dv":60708,"./dv.js":60708,"./el":54691,"./el.js":54691,"./en-au":53872,"./en-au.js":53872,"./en-ca":28298,"./en-ca.js":28298,"./en-gb":56195,"./en-gb.js":56195,"./en-ie":66584,"./en-ie.js":66584,"./en-il":65543,"./en-il.js":65543,"./en-in":9033,"./en-in.js":9033,"./en-nz":79402,"./en-nz.js":79402,"./en-sg":43004,"./en-sg.js":43004,"./eo":32934,"./eo.js":32934,"./es":97650,"./es-do":20838,"./es-do.js":20838,"./es-mx":17730,"./es-mx.js":17730,"./es-us":56575,"./es-us.js":56575,"./es.js":97650,"./et":3035,"./et.js":3035,"./eu":3508,"./eu.js":3508,"./fa":119,"./fa.js":119,"./fi":90527,"./fi.js":90527,"./fil":95995,"./fil.js":95995,"./fo":52477,"./fo.js":52477,"./fr":85498,"./fr-ca":26435,"./fr-ca.js":26435,"./fr-ch":37892,"./fr-ch.js":37892,"./fr.js":85498,"./fy":37071,"./fy.js":37071,"./ga":41734,"./ga.js":41734,"./gd":70217,"./gd.js":70217,"./gl":77329,"./gl.js":77329,"./gom-deva":32124,"./gom-deva.js":32124,"./gom-latn":93383,"./gom-latn.js":93383,"./gu":95050,"./gu.js":95050,"./he":11713,"./he.js":11713,"./hi":43861,"./hi.js":43861,"./hr":26308,"./hr.js":26308,"./hu":90609,"./hu.js":90609,"./hy-am":17160,"./hy-am.js":17160,"./id":74063,"./id.js":74063,"./is":89374,"./is.js":89374,"./it":88383,"./it-ch":21827,"./it-ch.js":21827,"./it.js":88383,"./ja":23827,"./ja.js":23827,"./jv":89722,"./jv.js":89722,"./ka":41794,"./ka.js":41794,"./kk":27088,"./kk.js":27088,"./km":96870,"./km.js":96870,"./kn":84451,"./kn.js":84451,"./ko":63164,"./ko.js":63164,"./ku":98174,"./ku-kmr":6181,"./ku-kmr.js":6181,"./ku.js":98174,"./ky":78474,"./ky.js":78474,"./lb":79680,"./lb.js":79680,"./lo":15867,"./lo.js":15867,"./lt":45766,"./lt.js":45766,"./lv":69532,"./lv.js":69532,"./me":58076,"./me.js":58076,"./mi":41848,"./mi.js":41848,"./mk":30306,"./mk.js":30306,"./ml":73739,"./ml.js":73739,"./mn":99053,"./mn.js":99053,"./mr":86169,"./mr.js":86169,"./ms":73386,"./ms-my":92297,"./ms-my.js":92297,"./ms.js":73386,"./mt":77075,"./mt.js":77075,"./my":72264,"./my.js":72264,"./nb":22274,"./nb.js":22274,"./ne":8235,"./ne.js":8235,"./nl":92572,"./nl-be":43784,"./nl-be.js":43784,"./nl.js":92572,"./nn":54566,"./nn.js":54566,"./oc-lnc":69330,"./oc-lnc.js":69330,"./pa-in":29849,"./pa-in.js":29849,"./pl":94418,"./pl.js":94418,"./pt":79834,"./pt-br":48303,"./pt-br.js":48303,"./pt.js":79834,"./ro":24457,"./ro.js":24457,"./ru":82271,"./ru.js":82271,"./sd":1221,"./sd.js":1221,"./se":33478,"./se.js":33478,"./si":17538,"./si.js":17538,"./sk":5784,"./sk.js":5784,"./sl":46637,"./sl.js":46637,"./sq":86794,"./sq.js":86794,"./sr":45719,"./sr-cyrl":3322,"./sr-cyrl.js":3322,"./sr.js":45719,"./ss":56e3,"./ss.js":56e3,"./sv":41011,"./sv.js":41011,"./sw":40748,"./sw.js":40748,"./ta":11025,"./ta.js":11025,"./te":11885,"./te.js":11885,"./tet":28861,"./tet.js":28861,"./tg":86571,"./tg.js":86571,"./th":55802,"./th.js":55802,"./tk":59527,"./tk.js":59527,"./tl-ph":29231,"./tl-ph.js":29231,"./tlh":31052,"./tlh.js":31052,"./tr":85096,"./tr.js":85096,"./tzl":79846,"./tzl.js":79846,"./tzm":81765,"./tzm-latn":97711,"./tzm-latn.js":97711,"./tzm.js":81765,"./ug-cn":48414,"./ug-cn.js":48414,"./uk":16618,"./uk.js":16618,"./ur":57777,"./ur.js":57777,"./uz":57609,"./uz-latn":72475,"./uz-latn.js":72475,"./uz.js":57609,"./vi":21135,"./vi.js":21135,"./x-pseudo":64051,"./x-pseudo.js":64051,"./yo":82218,"./yo.js":82218,"./zh-cn":52648,"./zh-cn.js":52648,"./zh-hk":1632,"./zh-hk.js":1632,"./zh-mo":31541,"./zh-mo.js":31541,"./zh-tw":50304,"./zh-tw.js":50304};function i(e){var t=s(e);return a(t)}function s(e){if(!a.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}i.keys=function(){return Object.keys(n)},i.resolve=s,e.exports=i,i.id=35358},80157:function(e,t,a){"use strict";a.d(t,{$W:function(){return r},Cv:function(){return u},EV:function(){return d},LY:function(){return p},Rr:function(){return l},hS:function(){return c},iE:function(){return m},lu:function(){return f},nY:function(){return g},wh:function(){return h},yl:function(){return b}});var n=a(75769),i=a(55373),s=a.n(i),o={usermanag:"/admin/user/list.do",agentuserlist:"/agent/user/list.do",agentgetSecondAgent:"/agent/getSecondAgent.do",agentaddSimulatedAccount:"/agent/addSimulatedAccount.do",agentaddAgent:"/agent/addAgent.do",agentgetAgentInfo:"/agent/getAgentInfo.do",agentgetAgentAgencyFeeList:"/agent/getAgentAgencyFeeList.do",agentupdatePwd:"/agent/updatePwd.do",userauthByAdmin:"/admin/user/authByAdmin.do",userupdate:"/admin/user/update.do",usergetBank:"/admin/user/getBank.do",userupdateBank:"admin/user/updateBank.do",userupdateAmt:"/admin/user/updateAmt.do",deleteSignature:"/admin/user/deleteSignature.do"};function r(e){return(0,n.Ay)({url:o.userupdateAmt,method:"post",data:s().stringify(e)})}function c(e){return(0,n.Ay)({url:o.deleteSignature,method:"post",data:s().stringify(e)})}function l(e){return(0,n.Ay)({url:o.userupdate,method:"post",data:s().stringify(e)})}function d(e){return(0,n.Ay)({url:o.usergetBank,method:"post",data:s().stringify(e)})}function u(e){return(0,n.Ay)({url:o.usermanag,method:"post",data:s().stringify(e)})}function m(e){return(0,n.Ay)({url:o.userauthByAdmin,method:"post",data:s().stringify(e)})}function f(e){return(0,n.Ay)({url:o.userupdateBank,method:"post",data:s().stringify(e)})}function p(e){return(0,n.Ay)({url:o.agentgetSecondAgent,method:"post",data:s().stringify(e)})}function h(e){return(0,n.Ay)({url:o.agentaddSimulatedAccount,method:"post",data:s().stringify(e)})}function g(e){return(0,n.Ay)({url:o.agentgetAgentInfo,method:"post",data:s().stringify(e)})}function b(e){return(0,n.Ay)({url:o.agentupdatePwd,method:"post",data:s().stringify(e)})}},505:function(e,t,a){"use strict";a.d(t,{N:function(){return u},Vp:function(){return l},iD:function(){return r},mN:function(){return c},ri:function(){return d}});var n=a(75769),i=a(55373),s=a.n(i),o={Login:"/api/agent/login.do",Logout:"/api/agent/logout.do",ForgePassword:"/auth/forge-password",Register:"/auth/register",twoStepCode:"/auth/2step-code",SendSms:"/account/sms",SendSmsErr:"/account/sms_err",UserInfo:"/api/user/info",UserMenu:"/user/nav"};function r(e){return(0,n.Ay)({url:o.Login,method:"post",data:s().stringify(e)})}function c(e){return(0,n.Ay)({url:o.SendSms,method:"post",data:e})}function l(){return(0,n.Ay)({url:o.UserInfo,method:"get",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(){return(0,n.Ay)({url:o.Logout,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(e){return(0,n.Ay)({url:o.twoStepCode,method:"post",data:e})}},66117:function(e,t,a){"use strict";var n=a(76338),i=(a(26099),a(43898));t.A=function(e){function t(t,a,s){var o=this;if(s=s||{},o&&o._isVue){var r=document.querySelector("body>div[type=dialog]");r||(r=document.createElement("div"),r.setAttribute("type","dialog"),document.body.appendChild(r));var c=function(e,t){if(e instanceof Function){var a=e();a instanceof Promise?a.then((function(e){e&&t()})):a&&t()}else e||t()},l=new e({data:function(){return{visible:!0}},router:o.$router,store:o.$store,mounted:function(){var e=this;this.$on("close",(function(t){e.handleClose()}))},methods:{handleClose:function(){var e=this;c(this.$refs._component.onCancel,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("cancel"),l.$destroy()}))},handleOk:function(){var e=this;c(this.$refs._component.onOK||this.$refs._component.onOk,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("ok"),l.$destroy()}))}},render:function(e){var o=this,r=s&&s.model;r&&delete s.model;var c=Object.assign({},r&&{model:r}||{},{attrs:Object.assign({},(0,n.A)({},s.attrs||s),{visible:this.visible}),on:Object.assign({},(0,n.A)({},s.on||s),{ok:function(){o.handleOk()},cancel:function(){o.handleClose()}})}),l=a&&a.model;l&&delete a.model;var d=Object.assign({},l&&{model:l}||{},{ref:"_component",attrs:Object.assign({},(0,n.A)({},a&&a.attrs||a)),on:Object.assign({},(0,n.A)({},a&&a.on||a))});return e(i.A,c,[e(t,d)])}}).$mount(r)}}Object.defineProperty(e.prototype,"$dialog",{get:function(){return function(){t.apply(this,arguments)}}})}},8815:function(e,t,a){"use strict";a.d(t,{A:function(){return p}});var n,i,s=a(85471),o=new s.Ay,r=a(76338),c=(a(2008),a(50113),a(74423),a(62062),a(62010),a(26099),a(21699),a(23500),{name:"MultiTab",data:function(){return{fullPathList:[],pages:[],activeKey:"",newTabIndex:0}},created:function(){var e=this;o.$on("open",(function(t){if(!t)throw new Error("multi-tab: open tab ".concat(t," err"));e.activeKey=t})).$on("close",(function(t){t?e.closeThat(t):e.closeThat(e.activeKey)})).$on("rename",(function(t){var a=t.key,n=t.name;console.log("rename",a,n);try{var i=e.pages.find((function(e){return e.path===a}));i.meta.customTitle=n,e.$forceUpdate()}catch(s){}})),this.pages.push(this.$route),this.fullPathList.push(this.$route.fullPath),this.selectedLastPath()},methods:{onEdit:function(e,t){this[t](e)},remove:function(e){this.pages=this.pages.filter((function(t){return t.fullPath!==e})),this.fullPathList=this.fullPathList.filter((function(t){return t!==e})),this.fullPathList.includes(this.activeKey)||this.selectedLastPath()},selectedLastPath:function(){this.activeKey=this.fullPathList[this.fullPathList.length-1]},closeThat:function(e){this.fullPathList.length>1?this.remove(e):this.$message.info("这是最后一个标签了, 无法被关闭")},closeLeft:function(e){var t=this,a=this.fullPathList.indexOf(e);a>0?this.fullPathList.forEach((function(e,n){n<a&&t.remove(e)})):this.$message.info("左侧没有标签")},closeRight:function(e){var t=this,a=this.fullPathList.indexOf(e);a<this.fullPathList.length-1?this.fullPathList.forEach((function(e,n){n>a&&t.remove(e)})):this.$message.info("右侧没有标签")},closeAll:function(e){var t=this,a=this.fullPathList.indexOf(e);this.fullPathList.forEach((function(e,n){n!==a&&t.remove(e)}))},closeMenuClick:function(e,t){this[e](t)},renderTabPaneMenu:function(e){var t=this,a=this.$createElement;return a("a-menu",{on:(0,r.A)({},{click:function(a){var n=a.key;a.item,a.domEvent;t.closeMenuClick(n,e)}})},[a("a-menu-item",{key:"closeThat"},["关闭当前标签"]),a("a-menu-item",{key:"closeRight"},["关闭右侧"]),a("a-menu-item",{key:"closeLeft"},["关闭左侧"]),a("a-menu-item",{key:"closeAll"},["关闭全部"])])},renderTabPane:function(e,t){var a=this.$createElement,n=this.renderTabPaneMenu(t);return a("a-dropdown",{attrs:{overlay:n,trigger:["contextmenu"]}},[a("span",{style:{userSelect:"none"}},[e])])}},watch:{$route:function(e){this.activeKey=e.fullPath,this.fullPathList.indexOf(e.fullPath)<0&&(this.fullPathList.push(e.fullPath),this.pages.push(e))},activeKey:function(e){this.$router.push({path:e})}},render:function(){var e=this,t=arguments[0],a=this.onEdit,n=this.$data.pages,i=n.map((function(a){return t("a-tab-pane",{style:{height:0},attrs:{tab:e.renderTabPane(a.meta.customTitle||a.meta.title,a.fullPath),closable:n.length>1},key:a.fullPath})}));return t("div",{class:"ant-pro-multi-tab"},[t("div",{class:"ant-pro-multi-tab-wrapper"},[t("a-tabs",{attrs:{hideAdd:!0,type:"editable-card",tabBarStyle:{background:"#FFF",margin:0,paddingLeft:"16px",paddingTop:"1px"}},on:(0,r.A)({},{edit:a}),model:{value:e.activeKey,callback:function(t){e.activeKey=t}}},[i])])])}}),l=c,d=a(81656),u=(0,d.A)(l,n,i,!1,null,null,null),m=u.exports,f={open:function(e){o.$emit("open",e)},rename:function(e,t){o.$emit("rename",{key:e,name:t})},closeCurrentPage:function(){this.close()},close:function(e){o.$emit("close",e)}};m.install=function(e){e.prototype.$multiTab||(f.instance=o,e.prototype.$multiTab=f,e.component("multi-tab",m))};var p=m},60366:function(e,t){"use strict";t.A={navTheme:"dark",primaryColor:"#1890ff",layout:"sidemenu",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!1,colorWeak:!1,menu:{locale:!0},title:"Stock-Agent",pwa:!1,iconfontUrl:"",production:!1}},83521:function(e,t,a){"use strict";a.d(t,{y:function(){return Se},f:function(){return Le}});a(26099),a(47764),a(62953);var n,i,s,o,r=function(){var e=this,t=e._self._c;return t("div",{class:["user-layout-wrapper",e.isMobile&&"mobile"],attrs:{id:"userLayout"}},[t("div",{staticClass:"container"},[t("div",{staticClass:"user-layout-content"},[e._m(0),t("router-view")],1)])])},c=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"top"},[t("div",{staticClass:"header"},[t("a",{attrs:{href:"/"}},[t("img",{staticClass:"logo",attrs:{src:a(33153),alt:"logo"}}),t("span",{staticClass:"title"},[e._v("Stock-Agent")])])])])}],l=a(99547),d=(a(96205),a(77197)),u=(a(50769),a(40255)),m=(a(17735),a(67602)),f=(a(62062),a(11363)),p=a(76338),h=a(95353),g={computed:(0,p.A)({},(0,h.aH)({currentLang:function(e){return e.app.lang}})),methods:{setLang:function(e){this.$store.dispatch("setLang",e)}}},b=g,y=["zh-CN","en-US"],k={"zh-CN":"简体中文","en-US":"English"},v={"zh-CN":"🇨🇳","en-US":"🇺🇸"},C={props:{prefixCls:{type:String,default:"ant-pro-drop-down"}},name:"SelectLang",mixins:[b],render:function(){var e=this,t=arguments[0],a=this.prefixCls,n=function(t){var a=t.key;e.setLang(a)},i=t(m.Ay,{class:["menu","ant-pro-header-menu"],attrs:{selectedKeys:[this.currentLang]},on:{click:n}},[y.map((function(e){return t(m.Ay.Item,{key:e},[t("span",{attrs:{role:"img","aria-label":k[e]}},[v[e]])," ",k[e]])}))]);return t(d.Ay,{attrs:{overlay:i,placement:"bottomRight"}},[t("span",{class:a},[t(u.A,{attrs:{type:"global",title:(0,f.vb)("navBar.lang")}})])])}},A=C,w={name:"UserLayout",components:{SelectLang:A},mixins:[l.w],mounted:function(){document.body.classList.add("userLayout")},beforeDestroy:function(){document.body.classList.remove("userLayout")}},j=w,S=a(81656),L=(0,S.A)(j,r,c,!1,null,"4ed8ddcc",null),I=L.exports,x=function(){var e=this,t=e._self._c;return t("div",[t("router-view")],1)},N=[],T={name:"BlankLayout"},E=T,z=(0,S.A)(E,x,N,!1,null,"7f25f9eb",null),U=(z.exports,function(){var e=this,t=e._self._c;return t("pro-layout",e._b({attrs:{menus:e.menus,collapsed:e.collapsed,mediaQuery:e.query,isMobile:e.isMobile,handleMediaQuery:e.handleMediaQuery,handleCollapse:e.handleCollapse,i18nRender:e.i18nRender},scopedSlots:e._u([{key:"menuHeaderRender",fn:function(){return[t("div",[t("img",{attrs:{src:a(33153)}}),t("h1",[e._v(e._s(e.title))])])]},proxy:!0},{key:"headerContentRender",fn:function(){return[t("div",[t("a-tooltip",{attrs:{title:"刷新页面"}},[t("a-icon",{staticStyle:{"font-size":"18px",cursor:"pointer"},attrs:{type:"reload"},on:{click:function(t){return e.getreload()}}})],1)],1)]},proxy:!0},{key:"rightContentRender",fn:function(){return[t("right-content",{attrs:{"top-menu":"topmenu"===e.settings.layout,"is-mobile":e.isMobile,theme:e.settings.theme}})]},proxy:!0},{key:"footerRender",fn:function(){return[t("global-footer")]},proxy:!0}])},"pro-layout",e.settings,!1),[t("router-view")],1)}),P=[],q=(a(50113),a(20931)),F=a(75314),M=a(60366),_=function(){var e=this,t=e._self._c;return t("div",{class:e.wrpCls},[t("avatar-dropdown",{class:e.prefixCls,attrs:{menu:e.showMenu,"current-user":e.currentUser}})],1)},D=[],$=a(26297),O=(a(62010),function(){var e=this,t=e._self._c;return t("div",[e.currentUser&&e.currentUser.name?t("a-dropdown",{attrs:{placement:"bottomRight"},scopedSlots:e._u([{key:"overlay",fn:function(){return[t("a-menu",{staticClass:"ant-pro-drop-down menu",attrs:{"selected-keys":[]}},[e.menu?t("a-menu-item",{key:"settings",on:{click:e.handleToSettings}},[t("a-icon",{attrs:{type:"setting"}}),e._v(" 修改密码 ")],1):e._e(),e.menu?t("a-menu-divider"):e._e(),t("a-menu-item",{key:"logout",on:{click:e.handleLogout}},[t("a-icon",{attrs:{type:"logout"}}),e._v(" "+e._s(e.$t("menu.account.logout"))+" ")],1)],1)]},proxy:!0}],null,!1,**********)},[t("span",{staticClass:"ant-pro-account-avatar"},[t("a-avatar",{staticClass:"antd-pro-global-header-index-avatar",attrs:{size:"small",src:"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png"}}),t("span",[e._v(e._s(e.currentUser.name))])],1)]):t("span",[t("a-spin",{style:{marginLeft:8,marginRight:8},attrs:{size:"small"}})],1),t("span",[t("a-modal",{attrs:{title:"修改密码",width:500,visible:e.addUserdialog,confirmLoading:e.addUserDialogloading},on:{ok:e.OkaddUserdialog,cancel:e.CanceladdUserdialog}},[t("a-form",{ref:"addUserform",attrs:{form:e.addUserform}},[t("a-form-item",{attrs:{label:"旧密码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["oldPwd",{rules:[{required:!0,message:"请输入旧密码"}]}],expression:"['oldPwd', { rules: [{ required: true, message: '请输入旧密码', }] }]"}],attrs:{placeholder:"请输入旧密码"}})],1),t("a-form-item",{attrs:{label:"新密码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["newPwd",{rules:[{required:!0,message:"请输入新密码"}]}],expression:"['newPwd', { rules: [{ required: true, message: '请输入新密码', }] }]"}],attrs:{placeholder:"请输入新密码"}})],1)],1)],1)],1)],1)}),R=[],B=(a(96305),a(43898)),V=a(80157),H={name:"AvatarDropdown",props:{currentUser:{type:Object,default:function(){return null}},menu:{type:Boolean,default:!0}},data:function(){return{addUserform:this.$form.createForm(this),addUserdialog:!1,addUserDialogloading:!1}},methods:{CanceladdUserdialog:function(){this.addUserdialog=!1;var e=this.$refs.addUserform.form;e.resetFields()},OkaddUserdialog:function(){var e=this,t=this.$refs.addUserform.form;t.validateFields((function(a,n){a||(e.addUserDialogloading=!0,(0,V.yl)(n).then((function(a){0==a.status?(e.addUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields()):e.$message.error({content:a.msg}),e.addUserDialogloading=!1})))}))},handleToCenter:function(){this.$router.push({path:"/account/center"})},handleToSettings:function(){this.addUserdialog=!0},handleLogout:function(e){var t=this;B.A.confirm({title:this.$t("layouts.usermenu.dialog.title"),content:this.$t("layouts.usermenu.dialog.content"),onOk:function(){return t.$store.dispatch("Logout").then((function(){t.$router.push({name:"login"})}))},onCancel:function(){}})}}},W=H,G=(0,S.A)(W,O,R,!1,null,"9ef1321c",null),K=G.exports,X={name:"RightContent",components:{AvatarDropdown:K,SelectLang:A},props:{prefixCls:{type:String,default:"ant-pro-global-header-index-action"},isMobile:{type:Boolean,default:function(){return!1}},topMenu:{type:Boolean,required:!0},theme:{type:String,required:!0}},data:function(){return{showMenu:!0,currentUser:{}}},computed:{wrpCls:function(){return(0,$.A)({"ant-pro-global-header-index-right":!0},"ant-pro-global-header-index-".concat(this.isMobile||!this.topMenu?"light":this.theme),!0)}},mounted:function(){this.getAgentInfo()},methods:{getAgentInfo:function(){var e=this;(0,V.nY)().then((function(t){0==t.status?setTimeout((function(){e.currentUser={name:t.data.agentName}}),1500):setTimeout((function(){e.currentUser={name:"代理"}}),1500)}))}}},J=X,Y=(0,S.A)(J,_,D,!1,null,null,null),Z=Y.exports,Q=function(){var e=this,t=e._self._c;return t("global-footer",{staticClass:"footer custom-render",scopedSlots:e._u([{key:"links",fn:function(){},proxy:!0},{key:"copyright",fn:function(){},proxy:!0}])})},ee=[],te={name:"ProGlobalFooter",components:{GlobalFooter:q.Tn}},ae=te,ne=(0,S.A)(ae,Q,ee,!1,null,null,null),ie=ne.exports,se="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",oe={props:{isMobile:Boolean},mounted:function(){},methods:{load:function(){if(se){var e=document.createElement("script");e.id="_adsbygoogle_js",e.src=se,this.$el.appendChild(e),setTimeout((function(){(window.adsbygoogle||[]).push({})}),2e3)}}},render:function(){}},re=oe,ce=(0,S.A)(re,n,i,!1,null,"a032cdc2",null),le=ce.exports,de={name:"BasicLayout",components:{SettingDrawer:q.G5,RightContent:Z,GlobalFooter:ie,Ads:le},data:function(){return{isProPreviewSite:!0,isDev:!0,menus:[],collapsed:!1,title:M.A.title,settings:{layout:M.A.layout,contentWidth:"sidemenu"===M.A.layout?F.OT.Fluid:M.A.contentWidth,theme:M.A.navTheme,primaryColor:M.A.primaryColor,fixedHeader:M.A.fixedHeader,fixSiderbar:M.A.fixSiderbar,colorWeak:M.A.colorWeak,hideHintAlert:!0,hideCopyButton:!1},query:{},isMobile:!1}},computed:(0,p.A)({},(0,h.aH)({mainMenu:function(e){return e.permission.addRouters}})),created:function(){var e=this,t=this.mainMenu.find((function(e){return"/"===e.path}));this.menus=t&&t.children||[],this.$watch("collapsed",(function(){e.$store.commit(F.cf,e.collapsed)})),this.$watch("isMobile",(function(){e.$store.commit(F.nd,e.isMobile)}))},mounted:function(){var e=this,t=navigator.userAgent;t.indexOf("Edge")>-1&&this.$nextTick((function(){e.collapsed=!e.collapsed,setTimeout((function(){e.collapsed=!e.collapsed}),16)})),console.log("production"),(0,q.V_)(this.settings.primaryColor)},methods:{getreload:function(){this.$router.go(0)},i18nRender:f.vb,handleMediaQuery:function(e){this.query=e,!this.isMobile||e["screen-xs"]?!this.isMobile&&e["screen-xs"]&&(this.isMobile=!0,this.collapsed=!1,this.settings.contentWidth=F.OT.Fluid):this.isMobile=!1},handleCollapse:function(e){this.collapsed=e},handleSettingChange:function(e){var t=e.type,a=e.value;switch(console.log("type",t,a),t&&(this.settings[t]=a),t){case"contentWidth":this.settings[t]=a;break;case"layout":"sidemenu"===a?this.settings.contentWidth=F.OT.Fluid:(this.settings.fixSiderbar=!1,this.settings.contentWidth=F.OT.Fixed);break}}}},ue=de,me=(0,S.A)(ue,U,P,!1,null,null,null),fe=me.exports,pe={name:"RouteView",props:{keepAlive:{type:Boolean,default:!0}},data:function(){return{}},render:function(){var e=arguments[0],t=this.$route.meta,a=this.$store.getters,n=e("keep-alive",[e("router-view")]),i=e("router-view");return(a.multiTab||t.keepAlive)&&(this.keepAlive||a.multiTab||t.keepAlive)?n:i}},he=pe,ge=(0,S.A)(he,s,o,!1,null,null,null),be=(ge.exports,function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("router-view")],1)}),ye=[],ke={name:"PageView"},ve=ke,Ce=(0,S.A)(ve,be,ye,!1,null,null,null),Ae=(Ce.exports,a(98490)),we=a.n(Ae),je={name:"RouteView",render:function(e){return e("router-view")}},Se=[{path:"/",name:"index",component:fe,meta:{title:"menu.home"},redirect:"/dashboard/workplace",children:[{path:"/dashboard",name:"dashboard",redirect:"/dashboard/workplace",component:je,meta:{title:"menu.dashboard",keepAlive:!0,icon:we(),permission:["dashboard"]},children:[{path:"/dashboard/workplace",name:"Workplace",component:function(){return a.e(39).then(a.bind(a,95039))},meta:{title:"menu.dashboard.workplace",keepAlive:!0,permission:["dashboard"]}}]},{path:"/userlist",redirect:"/userlist/index",component:je,meta:{title:"用户管理",icon:"user",permission:["userlist"]},children:[{path:"/userlist/index",name:"Userlist",component:function(){return a.e(221).then(a.bind(a,81221))},meta:{title:"用户列表",keepAlive:!0,permission:["userlist"]}}]},{path:"/position",redirect:"/position/financing",component:je,meta:{title:"持仓管理",icon:"money-collect",permission:["financing"]},children:[{path:"/position/financing",name:"financing",component:function(){return a.e(142).then(a.bind(a,30142))},meta:{title:"持仓管理",keepAlive:!0,permission:["financing"]}}]},{path:"/newshares",redirect:"/newshares/newshareslist",component:je,meta:{title:"新股管理",icon:"sliders",permission:["newshareslist"]},children:[{path:"/newshares/newsharesrecord",name:"newsharesrecord",component:function(){return a.e(655).then(a.bind(a,69655))},meta:{title:"新股申购记录",keepAlive:!0,permission:["newsharesrecord"]}}]},{path:"/depositrecord",redirect:"/depositrecord/depositlist",component:je,meta:{title:"入金记录",icon:"import",permission:["depositlist"]},children:[{path:"/depositrecord/depositlist",name:"depositlist",component:function(){return a.e(164).then(a.bind(a,52164))},meta:{title:"入金列表",keepAlive:!0,permission:["depositlist"]}}]},{path:"/cashingrecord",redirect:"/cashingrecord/cashinglist",component:je,meta:{title:"出金记录",icon:"export",permission:["cashinglist"]},children:[{path:"/cashingrecord/cashinglist",name:"cashinglist",component:function(){return a.e(350).then(a.bind(a,99350))},meta:{title:"出金列表",keepAlive:!0,permission:["cashinglist"]}}]}]},{path:"*",redirect:"/404",hidden:!0}],Le=[{path:"/user",component:I,redirect:"/user/login",hidden:!0,children:[{path:"login",name:"login",component:function(){return a.e(806).then(a.bind(a,65797))}},{path:"register",name:"register",component:function(){return a.e(806).then(a.bind(a,71194))}},{path:"register-result",name:"registerResult",component:function(){return a.e(806).then(a.bind(a,2275))}},{path:"recover",name:"recover",component:void 0}]},{path:"/404",component:function(){return a.e(143).then(a.bind(a,32319))}}]},11363:function(e,t,a){"use strict";a.d(t,{J4:function(){return g},vb:function(){return b}});var n=a(76338),i=(a(74423),a(26099),a(47764),a(62953),a(85471)),s=a(64765),o=a(74053),r=a.n(o),c=a(95093),l=a.n(c),d=a(45958);i.Ay.use(s.A);var u="en-US",m={"en-US":(0,n.A)({},d["default"])},f=new s.A({silentTranslationWarn:!0,locale:u,fallbackLocale:u,messages:m}),p=[u];function h(e){return f.locale=e,document.querySelector("html").setAttribute("lang",e),e}function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u;return new Promise((function(t){return r().set("lang",e),f.locale!==e?p.includes(e)?t(h(e)):a(5839)("./".concat(e)).then((function(t){var a=t.default;return f.setLocaleMessage(e,a),p.push(e),l().updateLocale(a.momentName,a.momentLocale),h(e)})):t(e)}))}function b(e){return f.t("".concat(e))}t.Ay=f},45958:function(e,t,a){"use strict";a.r(t);var n=a(76338),i=a(95692),s=a(52648),o=a.n(s),r=a(39668),c=a(14868),l=a(91363),d=a(11016),u=a(91771),m=a(89065),f=a(11250),p=a(77844),h={antLocale:i.A,momentName:"zh-cn",momentLocale:o()};t["default"]=(0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)({message:"-","layouts.usermenu.dialog.title":"信息","layouts.usermenu.dialog.content":"您确定要注销吗？","layouts.userLayout.title":"Stock Agent 是西湖区最具影响力的 Web 设计规范"},h),r["default"]),c["default"]),l["default"]),d["default"]),u["default"]),m["default"]),f["default"]),p["default"])},77844:function(e,t,a){"use strict";a.r(t);var n=a(76338),i=a(54142);t["default"]=(0,n.A)({},i["default"])},54142:function(e,t,a){"use strict";a.r(t),t["default"]={"account.settings.menuMap.basic":"基本设置","account.settings.menuMap.security":"安全设置","account.settings.menuMap.custom":"个性化","account.settings.menuMap.binding":"账号绑定","account.settings.menuMap.notification":"新消息通知","account.settings.basic.avatar":"头像","account.settings.basic.change-avatar":"更换头像","account.settings.basic.email":"邮箱","account.settings.basic.email-message":"请输入您的邮箱!","account.settings.basic.nickname":"昵称","account.settings.basic.nickname-message":"请输入您的昵称!","account.settings.basic.profile":"个人简介","account.settings.basic.profile-message":"请输入个人简介!","account.settings.basic.profile-placeholder":"个人简介","account.settings.basic.country":"国家/地区","account.settings.basic.country-message":"请输入您的国家或地区!","account.settings.basic.geographic":"所在省市","account.settings.basic.geographic-message":"请输入您的所在省市!","account.settings.basic.address":"街道地址","account.settings.basic.address-message":"请输入您的街道地址!","account.settings.basic.phone":"联系电话","account.settings.basic.phone-message":"请输入您的联系电话!","account.settings.basic.update":"更新基本信息","account.settings.basic.update.success":"更新基本信息成功","account.settings.security.strong":"强","account.settings.security.medium":"中","account.settings.security.weak":"弱","account.settings.security.password":"账户密码","account.settings.security.password-description":"当前密码强度：","account.settings.security.phone":"密保手机","account.settings.security.phone-description":"已绑定手机：","account.settings.security.question":"密保问题","account.settings.security.question-description":"未设置密保问题，密保问题可有效保护账户安全","account.settings.security.email":"备用邮箱","account.settings.security.email-description":"已绑定邮箱：","account.settings.security.mfa":"MFA 设备","account.settings.security.mfa-description":"未绑定 MFA 设备，绑定后，可以进行二次确认","account.settings.security.modify":"修改","account.settings.security.set":"设置","account.settings.security.bind":"绑定","account.settings.binding.taobao":"绑定淘宝","account.settings.binding.taobao-description":"当前未绑定淘宝账号","account.settings.binding.alipay":"绑定支付宝","account.settings.binding.alipay-description":"当前未绑定支付宝账号","account.settings.binding.dingding":"绑定钉钉","account.settings.binding.dingding-description":"当前未绑定钉钉账号","account.settings.binding.bind":"绑定","account.settings.notification.password":"账户密码","account.settings.notification.password-description":"其他用户的消息将以站内信的形式通知","account.settings.notification.messages":"系统消息","account.settings.notification.messages-description":"系统消息将以站内信的形式通知","account.settings.notification.todo":"待办任务","account.settings.notification.todo-description":"待办任务将以站内信的形式通知","account.settings.settings.open":"开","account.settings.settings.close":"关"}},91771:function(e,t,a){"use strict";a.r(t);var n=a(76338),i=a(22492);t["default"]=(0,n.A)({},i["default"])},22492:function(e,t,a){"use strict";a.r(t),t["default"]={"dashboard.analysis.test":"工专路 {no} 号店","dashboard.analysis.introduce":"指标说明","dashboard.analysis.total-sales":"总销售额","dashboard.analysis.day-sales":"日均销售额￥","dashboard.analysis.visits":"访问量","dashboard.analysis.visits-trend":"访问量趋势","dashboard.analysis.visits-ranking":"门店访问量排名","dashboard.analysis.day-visits":"日访问量","dashboard.analysis.week":"周同比","dashboard.analysis.day":"日同比","dashboard.analysis.payments":"支付笔数","dashboard.analysis.conversion-rate":"转化率","dashboard.analysis.operational-effect":"运营活动效果","dashboard.analysis.sales-trend":"销售趋势","dashboard.analysis.sales-ranking":"门店销售额排名","dashboard.analysis.all-year":"全年","dashboard.analysis.all-month":"本月","dashboard.analysis.all-week":"本周","dashboard.analysis.all-day":"今日","dashboard.analysis.search-users":"搜索用户数","dashboard.analysis.per-capita-search":"人均搜索次数","dashboard.analysis.online-top-search":"线上热门搜索","dashboard.analysis.the-proportion-of-sales":"销售额类别占比","dashboard.analysis.dropdown-option-one":"操作一","dashboard.analysis.dropdown-option-two":"操作二","dashboard.analysis.channel.all":"全部渠道","dashboard.analysis.channel.online":"线上","dashboard.analysis.channel.stores":"门店","dashboard.analysis.sales":"销售额","dashboard.analysis.traffic":"客流量","dashboard.analysis.table.rank":"排名","dashboard.analysis.table.search-keyword":"搜索关键词","dashboard.analysis.table.users":"用户数","dashboard.analysis.table.weekly-range":"周涨幅"}},89065:function(e,t,a){"use strict";a.r(t);var n=a(76338),i=a(97178);t["default"]=(0,n.A)({},i["default"])},97178:function(e,t,a){"use strict";a.r(t),t["default"]={"form.basic-form.basic.title":"基础表单","form.basic-form.basic.description":"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。","form.basic-form.title.label":"标题","form.basic-form.title.placeholder":"给目标起个名字","form.basic-form.title.required":"请输入标题","form.basic-form.date.label":"起止日期","form.basic-form.placeholder.start":"开始日期","form.basic-form.placeholder.end":"结束日期","form.basic-form.date.required":"请选择起止日期","form.basic-form.goal.label":"目标描述","form.basic-form.goal.placeholder":"请输入你的阶段性工作目标","form.basic-form.goal.required":"请输入目标描述","form.basic-form.standard.label":"衡量标准","form.basic-form.standard.placeholder":"请输入衡量标准","form.basic-form.standard.required":"请输入衡量标准","form.basic-form.client.label":"客户","form.basic-form.client.required":"请描述你服务的客户","form.basic-form.label.tooltip":"目标的服务对象","form.basic-form.client.placeholder":"请描述你服务的客户，内部客户直接 @姓名／工号","form.basic-form.invites.label":"邀评人","form.basic-form.invites.placeholder":"请直接 @姓名／工号，最多可邀请 5 人","form.basic-form.weight.label":"权重","form.basic-form.weight.placeholder":"请输入","form.basic-form.public.label":"目标公开","form.basic-form.label.help":"客户、邀评人默认被分享","form.basic-form.radio.public":"公开","form.basic-form.radio.partially-public":"部分公开","form.basic-form.radio.private":"不公开","form.basic-form.publicUsers.placeholder":"公开给","form.basic-form.option.A":"同事一","form.basic-form.option.B":"同事二","form.basic-form.option.C":"同事三","form.basic-form.email.required":"请输入邮箱地址！","form.basic-form.email.wrong-format":"邮箱地址格式错误！","form.basic-form.userName.required":"请输入用户名!","form.basic-form.password.required":"请输入密码！","form.basic-form.password.twice":"两次输入的密码不匹配!","form.basic-form.strength.msg":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","form.basic-form.strength.strong":"强度：强","form.basic-form.strength.medium":"强度：中","form.basic-form.strength.short":"强度：太短","form.basic-form.confirm-password.required":"请确认密码！","form.basic-form.phone-number.required":"请输入手机号！","form.basic-form.phone-number.wrong-format":"手机号格式错误！","form.basic-form.verification-code.required":"请输入验证码！","form.basic-form.form.get-captcha":"获取验证码","form.basic-form.captcha.second":"秒","form.basic-form.form.optional":"（选填）","form.basic-form.form.submit":"提交","form.basic-form.form.save":"保存","form.basic-form.email.placeholder":"邮箱","form.basic-form.password.placeholder":"至少6位密码，区分大小写","form.basic-form.confirm-password.placeholder":"确认密码","form.basic-form.phone-number.placeholder":"手机号","form.basic-form.verification-code.placeholder":"验证码"}},39668:function(e,t,a){"use strict";a.r(t),t["default"]={submit:"提交",save:"保存","submit.ok":"提交成功","save.ok":"保存成功"}},14868:function(e,t,a){"use strict";a.r(t),t["default"]={"menu.welcome":"欢迎","menu.home":"主页","menu.dashboard":"仪表盘","menu.dashboard.analysis":"分析页","menu.dashboard.monitor":"监控页","menu.dashboard.workplace":"工作台","menu.form":"表单页","menu.form.basic-form":"基础表单","menu.form.step-form":"分步表单","menu.form.step-form.info":"分步表单（填写转账信息）","menu.form.step-form.confirm":"分步表单（确认转账信息）","menu.form.step-form.result":"分步表单（完成）","menu.form.advanced-form":"高级表单","menu.list":"列表页","menu.list.table-list":"查询表格","menu.list.basic-list":"标准列表","menu.list.card-list":"卡片列表","menu.list.search-list":"搜索列表","menu.list.search-list.articles":"搜索列表（文章）","menu.list.search-list.projects":"搜索列表（项目）","menu.list.search-list.applications":"搜索列表（应用）","menu.profile":"详情页","menu.profile.basic":"基础详情页","menu.profile.advanced":"高级详情页","menu.result":"结果页","menu.result.success":"成功页","menu.result.fail":"失败页","menu.exception":"异常页","menu.exception.not-permission":"403","menu.exception.not-find":"404","menu.exception.server-error":"500","menu.exception.trigger":"触发错误","menu.account":"个人页","menu.account.center":"个人中心","menu.account.settings":"个人设置","menu.account.trigger":"触发报错","menu.account.logout":"退出登录"}},11250:function(e,t,a){"use strict";a.r(t);var n=a(76338),i=a(64158),s=a(27669);t["default"]=(0,n.A)((0,n.A)({},i["default"]),s["default"])},27669:function(e,t,a){"use strict";a.r(t),t["default"]={"result.fail.error.title":"提交失败","result.fail.error.description":"请核对并修改以下信息后，再重新提交。","result.fail.error.hint-title":"您提交的内容有如下错误：","result.fail.error.hint-text1":"您的账户已被冻结","result.fail.error.hint-btn1":"立即解冻","result.fail.error.hint-text2":"您的账户还不具备申请资格","result.fail.error.hint-btn2":"立即升级","result.fail.error.btn-text":"返回修改"}},64158:function(e,t,a){"use strict";a.r(t),t["default"]={"result.success.title":"提交成功","result.success.description":"提交结果页用于反馈一系列操作任务的处理结果， 如果仅是简单操作，使用 Message 全局提示反馈即可。 本文字区域可以展示简单的补充说明，如果有类似展示 “单据”的需求，下面这个灰色区域可以呈现比较复杂的内容。","result.success.operate-title":"项目名称","result.success.operate-id":"项目 ID","result.success.principal":"负责人","result.success.operate-time":"生效时间","result.success.step1-title":"创建项目","result.success.step1-operator":"曲丽丽","result.success.step2-title":"部门初审","result.success.step2-operator":"周毛毛","result.success.step2-extra":"催一下","result.success.step3-title":"财务复核","result.success.step4-title":"完成","result.success.btn-return":"返回列表","result.success.btn-project":"查看项目","result.success.btn-print":"打印"}},91363:function(e,t,a){"use strict";a.r(t),t["default"]={"app.setting.pagestyle":"整体风格设置","app.setting.pagestyle.light":"亮色菜单风格","app.setting.pagestyle.dark":"暗色菜单风格","app.setting.pagestyle.realdark":"暗黑模式","app.setting.themecolor":"主题色","app.setting.navigationmode":"导航模式","app.setting.content-width":"内容区域宽度","app.setting.fixedheader":"固定 Header","app.setting.fixedsidebar":"固定侧边栏","app.setting.sidemenu":"侧边菜单布局","app.setting.topmenu":"顶部菜单布局","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.othersettings":"其他设置","app.setting.weakmode":"色弱模式","app.setting.copy":"拷贝设置","app.setting.loading":"加载主题中","app.setting.copyinfo":"拷贝设置成功 src/config/defaultSettings.js","app.setting.production.hint":"","app.setting.themecolor.daybreak":"拂晓蓝","app.setting.themecolor.dust":"薄暮","app.setting.themecolor.volcano":"火山","app.setting.themecolor.sunset":"日暮","app.setting.themecolor.cyan":"明青","app.setting.themecolor.green":"极光绿","app.setting.themecolor.geekblue":"极客蓝","app.setting.themecolor.purple":"酱紫"}},11016:function(e,t,a){"use strict";a.r(t),t["default"]={"user.login.userName":"用户名","user.login.password":"密码","user.login.username.placeholder":"请输入账户","user.login.password.placeholder":"请输入密码","user.login.message-invalid-credentials":"账户或密码错误","user.login.message-invalid-verification-code":"验证码错误","user.login.tab-login-credentials":"账户密码登录","user.login.tab-login-mobile":"手机号登录","user.login.mobile.placeholder":"手机号","user.login.mobile.verification-code.placeholder":"验证码","user.login.remember-me":"自动登录","user.login.forgot-password":"忘记密码","user.login.sign-in-with":"其他登录方式","user.login.signup":"注册账户","user.login.login":"登录","user.register.register":"注册","user.register.email.placeholder":"邮箱","user.register.password.placeholder":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.password.popover-message":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.confirm-password.placeholder":"确认密码","user.register.get-verification-code":"获取验证码","user.register.sign-in":"使用已有账户登录","user.register-result.msg":"你的账户：{email} 注册成功","user.register-result.activation-email":"激活邮件已发送到你的邮箱中，邮件有效期为24小时。请及时登录邮箱，点击邮件中的链接激活帐户。","user.register-result.back-home":"返回首页","user.register-result.view-mailbox":"查看邮箱","user.email.required":"请输入邮箱地址！","user.email.wrong-format":"邮箱地址格式错误！","user.userName.required":"请输入帐户名或邮箱地址","user.password.required":"请输入密码！","user.password.twice.msg":"两次输入的密码不匹配!","user.password.strength.msg":"密码强度不够 ","user.password.strength.strong":"强度：强","user.password.strength.medium":"强度：中","user.password.strength.low":"强度：低","user.password.strength.short":"强度：太短","user.confirm-password.required":"请确认密码！","user.phone-number.required":"请输入正确的手机号","user.phone-number.wrong-format":"手机号格式错误！","user.verification-code.required":"请输入验证码！"}},44300:function(e,t,a){"use strict";a(23792),a(3362),a(69085),a(9391),a(52675),a(89463),a(66412),a(60193),a(92168),a(2259),a(86964),a(83237),a(61833),a(67947),a(31073),a(45700),a(78125),a(20326),a(28706),a(26835),a(33771),a(2008),a(50113),a(48980),a(46449),a(78350),a(23418),a(74423),a(48598),a(62062),a(31051),a(34782),a(26910),a(87478),a(54554),a(93514),a(30237),a(54743),a(46761),a(11745),a(89572),a(48957),a(62010),a(4731),a(36033),a(93153),a(82326),a(36389),a(64444),a(8085),a(77762),a(65070),a(60605),a(39469),a(72152),a(75376),a(56624),a(11367),a(5914),a(78553),a(98690),a(60479),a(70761),a(2892),a(45374),a(25428),a(32637),a(40150),a(59149),a(64601),a(44435),a(87220),a(25843),a(9868),a(17427),a(87607),a(5506),a(52811),a(53921),a(83851),a(81278),a(1480),a(40875),a(29908),a(94052),a(94003),a(221),a(79432),a(9220),a(7904),a(93967),a(93941),a(10287),a(26099),a(16034),a(39796),a(60825),a(87411),a(21211),a(40888),a(9065),a(86565),a(32812),a(84634),a(71137),a(30985),a(34268),a(34873),a(84864),a(27495),a(69479),a(38781),a(31415),a(23860),a(99449),a(27337),a(21699),a(47764),a(71761),a(35701),a(68156),a(85906),a(42781),a(25440),a(5746),a(90744),a(11392),a(42762),a(39202),a(43359),a(89907),a(11898),a(35490),a(5745),a(94298),a(60268),a(69546),a(20781),a(50778),a(89195),a(46276),a(48718),a(16308),a(34594),a(29833),a(46594),a(72107),a(95477),a(21489),a(22134),a(3690),a(61740),a(81630),a(72170),a(75044),a(69539),a(31694),a(89955),a(33206),a(48345),a(44496),a(66651),a(12887),a(19369),a(66812),a(8995),a(52568),a(31575),a(36072),a(88747),a(28845),a(29423),a(57301),a(373),a(86614),a(41405),a(33684),a(73772),a(30958),a(23500),a(62953),a(59848),a(122),a(3296),a(27208),a(48408),a(7452);var n=a(67569);(0,n.lT)()&&console.error("[antd-pro] ERROR: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV."),console.log("[antd-pro] mock mounting");var i=a(9661);a(72021),a(76052),a(89776),a(54577),a(5126),a(90313),i.setup({timeout:800}),console.log("[antd-pro] mock mounted");var s=a(85471),o=function(){var e=this,t=e._self._c;return t("a-config-provider",{attrs:{locale:e.locale}},[t("div",{attrs:{id:"app"}},[t("router-view")],1)])},r=[],c=a(60366),l=function(e){document.title=e;var t=navigator.userAgent,a=/\bMicroMessenger\/([\d\.]+)/;if(a.test(t)&&/ip(hone|od|ad)/i.test(t)){var n=document.createElement("iframe");n.src="/favicon.ico",n.style.display="none",n.onload=function(){setTimeout((function(){n.remove()}),9)},document.body.appendChild(n)}},d=c.A.title,u=a(11363),m={data:function(){return{}},computed:{locale:function(){var e=this.$route.meta.title;return e&&l("".concat((0,u.vb)(e)," - ").concat(d)),this.$i18n.getLocaleMessage(this.$store.getters.lang).antLocale}},created:function(){},methods:{}},f=m,p=a(81656),h=(0,p.A)(f,o,r,!1,null,null,null),g=h.exports,b=a(40173),y=a(83521),k=b.Ay.prototype.push;b.Ay.prototype.push=function(e,t,a){return t||a?k.call(this,e,t,a):k.call(this,e).catch((function(e){return e}))},s.Ay.use(b.Ay);var v=function(){return new b.Ay({mode:"hash",routes:y.f})},C=v();function A(){var e=v();C.matcher=e.matcher}var w=C,j=a(55499),S=a(75769),L=a(20931),I={theme:[{key:"dark",fileName:"dark.css",theme:"dark"},{key:"#F5222D",fileName:"#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",fileName:"#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",fileName:"#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",fileName:"#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",fileName:"#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",fileName:"#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",fileName:"#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}},{key:"#F5222D",theme:"dark",fileName:"dark-#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",theme:"dark",fileName:"dark-#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",theme:"dark",fileName:"dark-#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",theme:"dark",fileName:"dark-#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",theme:"dark",fileName:"dark-#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",theme:"dark",fileName:"dark-#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",theme:"dark",fileName:"dark-#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}}]},x=a(74053),N=a.n(x),T=a(75314),E=function(){console.log("[antd pro] created()");"\n █████╗ ███╗   ██╗████████╗██████╗     ██████╗ ██████╗  ██████╗ \n██╔══██╗████╗  ██║╚══██╔══╝██╔══██╗    ██╔══██╗██╔══██╗██╔═══██╗\n███████║██╔██╗ ██║   ██║   ██║  ██║    ██████╔╝██████╔╝██║   ██║\n██╔══██║██║╚██╗██║   ██║   ██║  ██║    ██╔═══╝ ██╔══██╗██║   ██║\n██║  ██║██║ ╚████║   ██║   ██████╔╝    ██║     ██║  ██║╚██████╔╝\n╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═════╝     ╚═╝     ╚═╝  ╚═╝ ╚═════╝ \n\t\t\t\t\tPublished ".concat("3.0.4","-").concat("36e57144"," @ antdv.com\n\t\t\t\t\tBuild date: ").concat("2025/5/27 下午12:27:15")};function z(){E(),j.A.commit(T.yG,N().get(T.yG,c.A.layout)),j.A.commit(T.MV,N().get(T.MV,c.A.fixedHeader)),j.A.commit(T.Fb,N().get(T.Fb,c.A.fixSiderbar)),j.A.commit(T.sl,N().get(T.sl,c.A.contentWidth)),j.A.commit(T.Wb,N().get(T.Wb,c.A.autoHideHeader)),j.A.commit(T.RM,N().get(T.RM,c.A.navTheme)),j.A.commit(T.o6,N().get(T.o6,c.A.colorWeak)),j.A.commit(T.Db,N().get(T.Db,c.A.primaryColor)),j.A.commit(T.jc,N().get(T.jc,c.A.multiTab)),j.A.commit("SET_TOKEN",N().get(T.Xh)),j.A.dispatch("setLang",N().get(T.$C,"en-US"))}a(13559);var U=a(56427),P=(a(89999),a(18787)),q=(a(89996),a(8442)),F=(a(61443),a(9426)),M=(a(88320),a(60304)),_=(a(78377),a(12393)),D=(a(64291),a(90895)),$=(a(94891),a(50257)),O=(a(98215),a(68263)),R=(a(92283),a(93167)),B=(a(37921),a(27448)),V=(a(5228),a(83766)),H=(a(7225),a(60031)),W=(a(25257),a(97345)),G=(a(24870),a(87298)),K=(a(93316),a(64274)),X=(a(94955),a(90500)),J=(a(78221),a(41446)),Y=(a(17735),a(67602)),Z=(a(45870),a(82840)),Q=(a(44043),a(39962)),ee=(a(36417),a(65847)),te=(a(53033),a(64719)),ae=(a(85494),a(47132)),ne=(a(96205),a(77197)),ie=(a(69941),a(94261)),se=(a(6875),a(98169)),oe=(a(50769),a(40255)),re=(a(47482),a(14248)),ce=(a(42290),a(22020)),le=(a(96305),a(43898)),de=(a(35184),a(29966)),ue=(a(12854),a(812)),me=(a(8740),a(71791)),fe=(a(1852),a(73856)),pe=(a(40662),a(2546)),he=(a(74721),a(75842)),ge=(a(82187),a(63301)),be=(a(56042),a(91997)),ye=(a(77050),a(49084)),ke=(a(70208),a(66066)),ve=(a(92786),a(57155)),Ce=(a(74332),a(37896)),Ae=(a(16878),a(39161)),we=a(26128),je=a(17756),Se=a.n(je),Le=a(66117),Ie=a(8815),xe=a(76338),Ne={name:"PageLoading",props:{tip:{type:String,default:"Loading.."},size:{type:String,default:"large"}},render:function(){var e=arguments[0],t={textAlign:"center",background:"rgba(0,0,0,0.6)",position:"fixed",top:0,bottom:0,left:0,right:0,zIndex:1100},a={position:"absolute",left:"50%",top:"40%",transform:"translate(-50%, -50%)"};return e("div",{style:t},[e(Z.A,{attrs:{size:this.size,tip:this.tip},style:a})])}},Te="0.0.1",Ee={newInstance:function(e,t){var a=document.querySelector("body>div[type=loading]");a||(a=document.createElement("div"),a.setAttribute("type","loading"),a.setAttribute("class","ant-loading-wrapper"),document.body.appendChild(a));var n=Object.assign({visible:!1,size:"large",tip:"Loading..."},t),i=new e({data:function(){return(0,xe.A)({},n)},render:function(){var e=arguments[0],t=this.tip,a={};return this.tip&&(a.tip=t),this.visible?e(Ne,{props:(0,xe.A)({},a)}):null}}).$mount(a);function s(e){var t=(0,xe.A)((0,xe.A)({},n),e),a=t.visible,s=t.size,o=t.tip;i.$set(i,"visible",a),o&&i.$set(i,"tip",o),s&&i.$set(i,"size",s)}return{instance:i,update:s}}},ze={show:function(e){this.instance.update((0,xe.A)((0,xe.A)({},e),{},{visible:!0}))},hide:function(){this.instance.update({visible:!1})}},Ue=function(e,t){e.prototype.$loading||(ze.instance=Ee.newInstance(e,t),e.prototype.$loading=ze)},Pe={version:Te,install:Ue},qe=a(27066),Fe={add:{key:"add",label:"新增"},delete:{key:"delete",label:"删除"},edit:{key:"edit",label:"修改"},query:{key:"query",label:"查询"},get:{key:"get",label:"详情"},enable:{key:"enable",label:"启用"},disable:{key:"disable",label:"禁用"},import:{key:"import",label:"导入"},export:{key:"export",label:"导出"}};function Me(e){Me.installed||(!e.prototype.$auth&&Object.defineProperties(e.prototype,{$auth:{get:function(){var e=this;return function(t){var a=t.split("."),n=(0,qe.A)(a,2),i=n[0],s=n[1],o=e.$store.getters.roles.permissions;return o.find((function(e){return e.permissionId===i})).actionList.findIndex((function(e){return e===s}))>-1}}}}),!e.prototype.$enum&&Object.defineProperties(e.prototype,{$enum:{get:function(){return function(e){var t=Fe;return e&&e.split(".").forEach((function(e){t=t&&t[e]||null})),t}}}}))}var _e=Me;s.Ay.directive("action",{inserted:function(e,t,a){var n=t.arg,i=j.A.getters.roles,s=a.context.$route.meta.permission,o="[object String]"===Object.prototype.toString.call(s)&&[s]||s;i.permissions.forEach((function(t){o.includes(t.permissionId)&&t.actionList&&!t.actionList.includes(n)&&(e.parentNode&&e.parentNode.removeChild(e)||(e.style.display="none"))}))}});s.Ay.use(Ae.A),s.Ay.use(Ce.A),s.Ay.use(ve.A),s.Ay.use(ke.A),s.Ay.use(ye.A),s.Ay.use(be.A),s.Ay.use(ge.Ay),s.Ay.use(he.A),s.Ay.use(pe.Ay),s.Ay.use(fe.A),s.Ay.use(me.Ay),s.Ay.use(ue.A),s.Ay.use(de.A),s.Ay.use(le.A),s.Ay.use(ce.A),s.Ay.use(re.Ay),s.Ay.use(oe.A),s.Ay.use(se.A),s.Ay.use(ie.A),s.Ay.use(ne.Ay),s.Ay.use(ae.Ay),s.Ay.use(te.A),s.Ay.use(ee.A),s.Ay.use(Q.A),s.Ay.use(Z.A),s.Ay.use(Y.Ay),s.Ay.use(J.A),s.Ay.use(X.A),s.Ay.use(K.A),s.Ay.use(G.A),s.Ay.use(W.A),s.Ay.use(H.A),s.Ay.use(V.Ay),s.Ay.use(B.Ay),s.Ay.use(R.A),s.Ay.use(O.A),s.Ay.use($.A),s.Ay.use(D.A),s.Ay.use(_.Ay),s.Ay.use(M.A),s.Ay.use(F.Ay),s.Ay.use(q.Ay),s.Ay.prototype.$confirm=le.A.confirm,s.Ay.prototype.$message=P.A,s.Ay.prototype.$notification=U.A,s.Ay.prototype.$info=le.A.info,s.Ay.prototype.$success=le.A.success,s.Ay.prototype.$error=le.A.error,s.Ay.prototype.$warning=le.A.warning,s.Ay.use(we.Ay),s.Ay.use(Le.A),s.Ay.use(Ie.A),s.Ay.use(Pe),s.Ay.use(_e),s.Ay.use(Se());var De=a(5947),$e=a.n(De);$e().configure({showSpinner:!1});var Oe=["login","register","registerResult"],Re="/user/login",Be="/user/userlist";w.beforeEach((function(e,t,a){$e().start(),e.meta&&"undefined"!==typeof e.meta.title&&l("".concat((0,u.vb)(e.meta.title)," - ").concat(d));var n=N().get(T.Xh);n?e.path===Re?(a({path:Be}),$e().done()):0===j.A.getters.roles.length?j.A.dispatch("GetInfo").then((function(i){console.log("res",i),j.A.dispatch("GenerateRoutes",(0,xe.A)({token:n},i)).then((function(){A(),j.A.getters.addRouters.forEach((function(e){w.addRoute(e)}));var n=decodeURIComponent(t.query.redirect||e.path);e.path===n?a((0,xe.A)((0,xe.A)({},e),{},{replace:!0})):a({path:n})}))})).catch((function(){U.A.error({message:"错误",description:"请求用户信息失败，请重试"}),j.A.dispatch("Logout").then((function(){a({path:Re,query:{redirect:e.fullPath}})}))})):a():Oe.includes(e.name)?a():(a({path:Re,query:{redirect:e.fullPath}}),$e().done())})),w.afterEach((function(){$e().done()}));var Ve=a(95093),He=a.n(Ve);a(52648);He().locale("zh-cn"),s.Ay.filter("NumberFormat",(function(e){if(!e)return"0";var t=e.toString().replace(/(\d)(?=(?:\d{3})+$)/g,"$1,");return t})),s.Ay.filter("dayjs",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return He()(e).format(t)})),s.Ay.filter("moment",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return He()(e).format(t)})),window["console"]["log"]=function(){},s.Ay.config.productionTip=!1,s.Ay.prototype.$host="/",s.Ay.use(S.He),s.Ay.component("pro-layout",L.Ay),s.Ay.component("page-container",L.sm),s.Ay.component("page-header-wrapper",L.sm),window.umi_plugin_ant_themeVar=I.theme,new s.Ay({router:w,store:j.A,i18n:u.Ay,created:z,render:function(e){return e(g)}}).$mount("#app")},90313:function(e,t,a){"use strict";a.r(t);var n=a(9661),i=a.n(n),s=a(65728),o=["Alipay","Angular","Stock Agent","Stock Agent","Bootstrap","React","Vue","Webpack"],r=["https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png","https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png","https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png","https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png","https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png"],c=["https://gw.alipayobjects.com/zos/rmsportal/uMfMFlvUuceEyPpotzlq.png","https://gw.alipayobjects.com/zos/rmsportal/iZBVOIhGJiAnhplqjvZW.png","https://gw.alipayobjects.com/zos/rmsportal/iXjVmWVHbCJAyqvDxdtx.png","https://gw.alipayobjects.com/zos/rmsportal/gLaIAoVWTtLbBWZNYEMg.png"],l=["付小小","吴加好","周星星","林东东","曲丽丽"],d="段落示意：蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。",u="在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。",m="https://ant.design",f=function(e){var t=(0,s.LT)(e);console.log("queryParameters",t),t&&!t.count&&(t.count=5);for(var a=[],n=0;n<t.count;n++){var f=n+1,p=parseInt(5*Math.random(),10);a.push({id:f,avatar:r[p],owner:l[p],content:d,star:i().mock("@integer(1, 999)"),percent:i().mock("@integer(1, 999)"),like:i().mock("@integer(1, 999)"),message:i().mock("@integer(1, 999)"),description:u,href:m,title:o[n%8],updatedAt:i().mock("@datetime"),members:[{avatar:"https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png",name:"曲丽丽",id:"member1"},{avatar:"https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png",name:"王昭君",id:"member2"},{avatar:"https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png",name:"董娜娜",id:"member3"}],activeUser:Math.ceil(1e5*Math.random())+1e5,newUser:Math.ceil(1e3*Math.random())+1e3,cover:parseInt(n/4,10)%2===0?c[n%4]:c[3-n%4]})}return(0,s.cL)(a)};i().mock(/\/list\/article/,"get",f)},72021:function(e,t,a){"use strict";a.r(t);a(74423);var n=a(9661),i=a.n(n),s=a(65728),o=["admin","super"],r=["8914de686ab28dc22f30d3d8e107ff6c","21232f297a57a5a743894a0e4a801fc3"],c=function(e){var t=(0,s.lZ)(e);return console.log("mock: body",t),o.includes(t.username)&&r.includes(t.password)?(0,s.cL)({id:i().mock("@guid"),name:i().mock("@name"),username:"admin",password:"",avatar:"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png",status:1,telephone:"",lastLoginIp:"*************",lastLoginTime:1534837621348,creatorId:"admin",createTime:*************,deleted:0,roleId:"admin",lang:"zh-CN",token:"4291d7da9005377ec9aec4a71ea837f"},"",200,{"Custom-Header":i().mock("@guid")}):(0,s.cL)({isLogin:!0},"账户或密码错误",401)},l=function(){return(0,s.cL)({},"[测试接口] 注销成功")},d=function(){return(0,s.cL)({captcha:i().mock("@integer(10000, 99999)")})},u=function(){return(0,s.cL)({stepCode:i().mock("@integer(0, 1)")})};i().mock(/\/auth\/login/,"post",c),i().mock(/\/auth\/logout/,"post",l),i().mock(/\/account\/sms/,"post",d),i().mock(/\/auth\/2step-code/,"post",u)},89776:function(e,t,a){"use strict";a.r(t);var n=a(9661),i=a.n(n),s=a(65728),o=5701,r=function(e){for(var t=(0,s.LT)(e),a=[],n=parseInt(t.pageNo),r=parseInt(t.pageSize),c=Math.ceil(o/r),l=(n-1)*r,d=(n>=c?o%r:r)+1,u=1;u<d;u++){var m=l+u;a.push({key:m,id:m,no:"No "+m,description:"这是一段描述",callNo:i().mock("@integer(1, 999)"),status:i().mock("@integer(0, 3)"),updatedAt:i().mock("@datetime"),editable:!1})}return(0,s.cL)({pageSize:r,pageNo:n,totalCount:o,totalPage:c,data:a})},c=function(){return(0,s.cL)({data:[{id:1,cover:"https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png",title:"Alipay",description:"那是一种内在的东西， 他们到达不了，也无法触及的",status:1,updatedAt:"2018-07-26 00:00:00"},{id:2,cover:"https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png",title:"Angular",description:"希望是一个好东西，也许是最好的，好东西是不会消亡的",status:1,updatedAt:"2018-07-26 00:00:00"},{id:3,cover:"https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png",title:"Stock Agent",description:"城镇中有那么多的酒馆，她却偏偏走进了我的酒馆",status:1,updatedAt:"2018-07-26 00:00:00"},{id:4,cover:"https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png",title:"Stock Agent",description:"那时候我只会想自己想要什么，从不想自己拥有什么",status:1,updatedAt:"2018-07-26 00:00:00"},{id:5,cover:"https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png",title:"Bootstrap",description:"凛冬将至",status:1,updatedAt:"2018-07-26 00:00:00"},{id:6,cover:"https://gw.alipayobjects.com/zos/rmsportal/ComBAopevLwENQdKWiIn.png",title:"Vue",description:"生命就像一盒巧克力，结果往往出人意料",status:1,updatedAt:"2018-07-26 00:00:00"}],pageSize:10,pageNo:0,totalPage:6,totalCount:57})},l=function(){return(0,s.cL)([{id:1,user:{nickname:"@name",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},project:{name:"白鹭酱油开发组",action:"更新",event:"番组计划"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"蓝莓酱",avatar:"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png"},project:{name:"白鹭酱油开发组",action:"更新",event:"番组计划"},time:"2018-08-23 09:35:37"},{id:1,user:{nickname:"@name",avatar:"@image(64x64)"},project:{name:"白鹭酱油开发组",action:"创建",event:"番组计划"},time:"2017-05-27 00:00:00"},{id:1,user:{nickname:"曲丽丽",avatar:"@image(64x64)"},project:{name:"高逼格设计天团",action:"更新",event:"六月迭代"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"@name",avatar:"@image(64x64)"},project:{name:"高逼格设计天团",action:"created",event:"六月迭代"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"曲丽丽",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},project:{name:"高逼格设计天团",action:"created",event:"六月迭代"},time:"2018-08-23 14:47:00"}])},d=function(){return(0,s.cL)([{id:1,name:"科学搬砖组",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},{id:2,name:"程序员日常",avatar:"https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png"},{id:1,name:"设计天团",avatar:"https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png"},{id:1,name:"中二少女团",avatar:"https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png"},{id:1,name:"骗你学计算机",avatar:"https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png"}])},u=function(){return(0,s.cL)([{item:"引用","个人":70,"团队":30,"部门":40},{item:"口碑","个人":60,"团队":70,"部门":40},{item:"产量","个人":50,"团队":60,"部门":40},{item:"贡献","个人":40,"团队":50,"部门":40},{item:"热度","个人":60,"团队":70,"部门":40},{item:"引用","个人":70,"团队":50,"部门":40}])};i().mock(/\/service/,"get",r),i().mock(/\/list\/search\/projects/,"get",c),i().mock(/\/workplace\/activity/,"get",l),i().mock(/\/workplace\/teams/,"get",d),i().mock(/\/workplace\/radar/,"get",u)},54577:function(e,t,a){"use strict";a.r(t);var n=a(9661),i=a.n(n),s=a(65728),o=function(){return(0,s.cL)([{key:"key-01",title:"研发中心",icon:"mail",children:[{key:"key-01-01",title:"后端组",icon:null,group:!0,children:[{key:"key-01-01-01",title:"JAVA",icon:null},{key:"key-01-01-02",title:"PHP",icon:null},{key:"key-01-01-03",title:"Golang",icon:null}]},{key:"key-01-02",title:"前端组",icon:null,group:!0,children:[{key:"key-01-02-01",title:"React",icon:null},{key:"key-01-02-02",title:"Vue",icon:null},{key:"key-01-02-03",title:"Angular",icon:null}]}]},{key:"key-02",title:"财务部",icon:"dollar",children:[{key:"key-02-01",title:"会计核算",icon:null},{key:"key-02-02",title:"成本控制",icon:null},{key:"key-02-03",title:"内部控制",icon:null,children:[{key:"key-02-03-01",title:"财务制度建设",icon:null},{key:"key-02-03-02",title:"会计核算",icon:null}]}]}])},r=function(){return(0,s.cL)({data:[{id:"admin",name:"管理员",describe:"拥有所有权限",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"admin",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["delete","edit"],dataAccess:null},{roleId:"admin",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["query","get","edit","delete"],dataAccess:null},{roleId:"admin",permissionId:"menu",permissionName:"菜单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","import"],dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["query","add","get"],dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["add","get","edit","delete"],dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:["add","get"],dataAccess:null}]},{id:"svip",name:"SVIP",describe:"超级会员",status:1,creatorId:"system",createTime:1532417744846,deleted:0,permissions:[{roleId:"admin",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["add","get","delete"],dataAccess:null},{roleId:"admin",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["add","query","get"],dataAccess:null},{roleId:"admin",permissionId:"menu",permissionName:"菜单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["add","get"],dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","query"],dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","get","edit"],dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:["add","edit"],dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add"],dataAccess:null}]},{id:"user",name:"普通会员",describe:"普通用户，只能查询",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"user",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["query"],dataAccess:null},{roleId:"user",permissionId:"marketing",permissionName:"营销管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"user",permissionId:"menu",permissionName:"菜单管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"user",permissionId:"permission",permissionName:"权限管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"role",permissionName:"角色管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"user",permissionName:"用户管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null}]}],pageSize:10,pageNo:0,totalPage:1,totalCount:5})},c=function(){return(0,s.cL)([{id:"marketing",name:"营销管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:null,parents:null,type:null,deleted:0,actions:["add","query","get","edit","delete"]},{id:"member",name:"会员管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"menu",name:"菜单管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","import","get","edit"]},{id:"order",name:"订单管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"permission",name:"权限管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"role",name:"角色管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"test",name:"测试权限",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]},{id:"user",name:"用户管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"export","defaultCheck":false,"describe":"导出"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]}])},l=function(){return(0,s.cL)({data:[{id:"marketing",name:"营销管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:null,parents:null,type:null,deleted:0,actions:["add","query","get","edit","delete"]},{id:"member",name:"会员管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"menu",name:"菜单管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","import","get","edit"]},{id:"order",name:"订单管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"permission",name:"权限管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"role",name:"角色管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"test",name:"测试权限",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]},{id:"user",name:"用户管理",describe:null,status:1,actionData:'[{"action":"add","describe":"新增","defaultCheck":false},{"action":"get","describe":"查询","defaultCheck":false}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]}],pageSize:10,pageNo:0,totalPage:1,totalCount:5})};i().mock(/\/org\/tree/,"get",o),i().mock(/\/role/,"get",r),i().mock(/\/permission\/no-pager/,"get",c),i().mock(/\/permission/,"get",l)},5126:function(e,t,a){"use strict";a.r(t);var n=a(9661),i=a.n(n),s=a(65728),o=function(){return(0,s.cL)([{value:9,name:"AntV"},{value:8,name:"F2"},{value:8,name:"G2"},{value:8,name:"G6"},{value:8,name:"DataSet"},{value:8,name:"墨者学院"},{value:6,name:"Analysis"},{value:6,name:"Data Mining"},{value:6,name:"Data Vis"},{value:6,name:"Design"},{value:6,name:"Grammar"},{value:6,name:"Graphics"},{value:6,name:"Graph"},{value:6,name:"Hierarchy"},{value:6,name:"Labeling"},{value:6,name:"Layout"},{value:6,name:"Quantitative"},{value:6,name:"Relation"},{value:6,name:"Statistics"},{value:6,name:"可视化"},{value:6,name:"数据"},{value:6,name:"数据可视化"},{value:4,name:"Arc Diagram"},{value:4,name:"Bar Chart"},{value:4,name:"Canvas"},{value:4,name:"Chart"},{value:4,name:"DAG"},{value:4,name:"DG"},{value:4,name:"Facet"},{value:4,name:"Geo"},{value:4,name:"Line"},{value:4,name:"MindMap"},{value:4,name:"Pie"},{value:4,name:"Pizza Chart"},{value:4,name:"Punch Card"},{value:4,name:"SVG"},{value:4,name:"Sunburst"},{value:4,name:"Tree"},{value:4,name:"UML"},{value:3,name:"Chart"},{value:3,name:"View"},{value:3,name:"Geom"},{value:3,name:"Shape"},{value:3,name:"Scale"},{value:3,name:"Animate"},{value:3,name:"Global"},{value:3,name:"Slider"},{value:3,name:"Connector"},{value:3,name:"Transform"},{value:3,name:"Util"},{value:3,name:"DomUtil"},{value:3,name:"MatrixUtil"},{value:3,name:"PathUtil"},{value:3,name:"G"},{value:3,name:"2D"},{value:3,name:"3D"},{value:3,name:"Line"},{value:3,name:"Area"},{value:3,name:"Interval"},{value:3,name:"Schema"},{value:3,name:"Edge"},{value:3,name:"Polygon"},{value:3,name:"Heatmap"},{value:3,name:"Render"},{value:3,name:"Tooltip"},{value:3,name:"Axis"},{value:3,name:"Guide"},{value:3,name:"Coord"},{value:3,name:"Legend"},{value:3,name:"Path"},{value:3,name:"Helix"},{value:3,name:"Theta"},{value:3,name:"Rect"},{value:3,name:"Polar"},{value:3,name:"Dsv"},{value:3,name:"Csv"},{value:3,name:"Tsv"},{value:3,name:"GeoJSON"},{value:3,name:"TopoJSON"},{value:3,name:"Filter"},{value:3,name:"Map"},{value:3,name:"Pick"},{value:3,name:"Rename"},{value:3,name:"Filter"},{value:3,name:"Map"},{value:3,name:"Pick"},{value:3,name:"Rename"},{value:3,name:"Reverse"},{value:3,name:"sort"},{value:3,name:"Subset"},{value:3,name:"Partition"},{value:3,name:"Imputation"},{value:3,name:"Fold"},{value:3,name:"Aggregate"},{value:3,name:"Proportion"},{value:3,name:"Histogram"},{value:3,name:"Quantile"},{value:3,name:"Treemap"},{value:3,name:"Hexagon"},{value:3,name:"Binning"},{value:3,name:"kernel"},{value:3,name:"Regression"},{value:3,name:"Density"},{value:3,name:"Sankey"},{value:3,name:"Voronoi"},{value:3,name:"Projection"},{value:3,name:"Centroid"},{value:3,name:"H5"},{value:3,name:"Mobile"},{value:3,name:"K线图"},{value:3,name:"关系图"},{value:3,name:"烛形图"},{value:3,name:"股票图"},{value:3,name:"直方图"},{value:3,name:"金字塔图"},{value:3,name:"分面"},{value:3,name:"南丁格尔玫瑰图"},{value:3,name:"饼图"},{value:3,name:"线图"},{value:3,name:"点图"},{value:3,name:"散点图"},{value:3,name:"子弹图"},{value:3,name:"柱状图"},{value:3,name:"仪表盘"},{value:3,name:"气泡图"},{value:3,name:"漏斗图"},{value:3,name:"热力图"},{value:3,name:"玉玦图"},{value:3,name:"直方图"},{value:3,name:"矩形树图"},{value:3,name:"箱形图"},{value:3,name:"色块图"},{value:3,name:"螺旋图"},{value:3,name:"词云"},{value:3,name:"词云图"},{value:3,name:"雷达图"},{value:3,name:"面积图"},{value:3,name:"马赛克图"},{value:3,name:"盒须图"},{value:3,name:"坐标轴"},{value:3,name:""},{value:3,name:"Jacques Bertin"},{value:3,name:"Leland Wilkinson"},{value:3,name:"William Playfair"},{value:3,name:"关联"},{value:3,name:"分布"},{value:3,name:"区间"},{value:3,name:"占比"},{value:3,name:"地图"},{value:3,name:"时间"},{value:3,name:"比较"},{value:3,name:"流程"},{value:3,name:"趋势"},{value:2,name:"亦叶"},{value:2,name:"再飞"},{value:2,name:"完白"},{value:2,name:"巴思"},{value:2,name:"张初尘"},{value:2,name:"御术"},{value:2,name:"有田"},{value:2,name:"沉鱼"},{value:2,name:"玉伯"},{value:2,name:"画康"},{value:2,name:"祯逸"},{value:2,name:"绝云"},{value:2,name:"罗宪"},{value:2,name:"萧庆"},{value:2,name:"董珊珊"},{value:2,name:"陆沉"},{value:2,name:"顾倾"},{value:2,name:"Domo"},{value:2,name:"GPL"},{value:2,name:"PAI"},{value:2,name:"SPSS"},{value:2,name:"SYSTAT"},{value:2,name:"Tableau"},{value:2,name:"D3"},{value:2,name:"Vega"},{value:2,name:"统计图表"}])};i().mock(/\/data\/antv\/tag-cloud/,"get",o)},76052:function(e,t,a){"use strict";a.r(t);var n=a(9661),i=a.n(n),s=a(65728),o=function(e){console.log("options",e);var t={id:"4291d7da9005377ec9aec4a71ea837f",name:"天野远子",username:"admin",password:"",avatar:"/avatar2.jpg",status:1,telephone:"",lastLoginIp:"*************",lastLoginTime:1534837621348,creatorId:"admin",createTime:*************,merchantCode:"TLif2btpzg079h15bk",deleted:0,roleId:"admin",role:{}},a={id:"admin",name:"管理员",describe:"拥有所有权限",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"admin",permissionId:"dashboard",permissionName:"仪表盘",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"userlist",permissionName:"仪表盘",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"exception",permissionName:"异常页面权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"result",permissionName:"结果权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"profile",permissionName:"详细页权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"table",permissionName:"表格权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"form",permissionName:"表单权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"table",permissionName:"桌子管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:null,dataAccess:null}]};return a.permissions.push({roleId:"admin",permissionId:"support",permissionName:"超级模块",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:null,dataAccess:null}),t.role=a,(0,s.cL)(t)},r=function(e){var t=[{name:"dashboard",parentId:0,id:1,meta:{title:"menu.dashboard",icon:"dashboard",show:!0},component:"RouteView",redirect:"/dashboard/workplace"},{name:"workplace",parentId:1,id:7,meta:{title:"menu.dashboard.monitor",show:!0},component:"Workplace"},{name:"monitor",path:"https://www.baidu.com/",parentId:1,id:3,meta:{title:"menu.dashboard.workplace",target:"_blank",show:!0}},{name:"Analysis",parentId:1,id:2,meta:{title:"menu.dashboard.analysis",show:!0},component:"Analysis",path:"/dashboard/analysis"},{name:"Userlist",parentId:1,id:2,meta:{title:"用户管理",show:!0},component:"userlist",path:"/userlist/index"},{name:"form",parentId:0,id:10,meta:{icon:"form",title:"menu.form"},redirect:"/form/base-form",component:"RouteView"},{name:"basic-form",parentId:10,id:6,meta:{title:"menu.form.basic-form"},component:"BasicForm"},{name:"step-form",parentId:10,id:5,meta:{title:"menu.form.step-form"},component:"StepForm"},{name:"advanced-form",parentId:10,id:4,meta:{title:"menu.form.advanced-form"},component:"AdvanceForm"},{name:"list",parentId:0,id:10010,meta:{icon:"table",title:"menu.list",show:!0},redirect:"/list/table-list",component:"RouteView"},{name:"table-list",parentId:10010,id:10011,path:"/list/table-list/:pageNo([1-9]\\d*)?",meta:{title:"menu.list.table-list",show:!0},component:"TableList"},{name:"basic-list",parentId:10010,id:10012,meta:{title:"menu.list.basic-list",show:!0},component:"StandardList"},{name:"card",parentId:10010,id:10013,meta:{title:"menu.list.card-list",show:!0},component:"CardList"},{name:"search",parentId:10010,id:10014,meta:{title:"menu.list.search-list",show:!0},redirect:"/list/search/article",component:"SearchLayout"},{name:"article",parentId:10014,id:10015,meta:{title:"menu.list.search-list.articles",show:!0},component:"SearchArticles"},{name:"project",parentId:10014,id:10016,meta:{title:"menu.list.search-list.projects",show:!0},component:"SearchProjects"},{name:"application",parentId:10014,id:10017,meta:{title:"menu.list.search-list.applications",show:!0},component:"SearchApplications"},{name:"profile",parentId:0,id:10018,meta:{title:"menu.profile",icon:"profile",show:!0},redirect:"/profile/basic",component:"RouteView"},{name:"basic",parentId:10018,id:10019,meta:{title:"menu.profile.basic",show:!0},component:"ProfileBasic"},{name:"advanced",parentId:10018,id:10020,meta:{title:"menu.profile.advanced",show:!0},component:"ProfileAdvanced"},{name:"result",parentId:0,id:10021,meta:{title:"menu.result",icon:"check-circle-o",show:!0},redirect:"/result/success",component:"PageView"},{name:"success",parentId:10021,id:10022,meta:{title:"menu.result.success",hiddenHeaderContent:!0,show:!0},component:"ResultSuccess"},{name:"fail",parentId:10021,id:10023,meta:{title:"menu.result.fail",hiddenHeaderContent:!0,show:!0},component:"ResultFail"},{name:"exception",parentId:0,id:10024,meta:{title:"menu.exception",icon:"warning",show:!0},redirect:"/exception/403",component:"RouteView"},{name:"403",parentId:10024,id:10025,meta:{title:"menu.exception.not-permission",show:!0},component:"Exception403"},{name:"404",parentId:10024,id:10026,meta:{title:"menu.exception.not-find",show:!0},component:"Exception404"},{name:"500",parentId:10024,id:10027,meta:{title:"menu.exception.server-error",show:!0},component:"Exception500"},{name:"account",parentId:0,id:10028,meta:{title:"menu.account",icon:"user",show:!0},redirect:"/account/center",component:"RouteView"},{name:"center",parentId:10028,id:10029,meta:{title:"menu.account.center",show:!0},component:"AccountCenter"},{name:"settings",parentId:10028,id:10030,meta:{title:"menu.account.settings",hideHeader:!0,hideChildren:!0,show:!0},redirect:"/account/settings/basic",component:"AccountSettings"},{name:"BasicSettings",path:"/account/settings/basic",parentId:10030,id:10031,meta:{title:"account.settings.menuMap.basic",show:!1},component:"BasicSetting"},{name:"SecuritySettings",path:"/account/settings/security",parentId:10030,id:10032,meta:{title:"account.settings.menuMap.security",show:!1},component:"SecuritySettings"},{name:"CustomSettings",path:"/account/settings/custom",parentId:10030,id:10033,meta:{title:"account.settings.menuMap.custom",show:!1},component:"CustomSettings"},{name:"BindingSettings",path:"/account/settings/binding",parentId:10030,id:10034,meta:{title:"account.settings.menuMap.binding",show:!1},component:"BindingSettings"},{name:"NotificationSettings",path:"/account/settings/notification",parentId:10030,id:10034,meta:{title:"account.settings.menuMap.notification",show:!1},component:"NotificationSettings"}],a=(0,s.cL)(t);return console.log("json",a),a};i().mock(/\/api\/user\/info/,"get",o),i().mock(/\/api\/user\/nav/,"get",r)},65728:function(e,t,a){"use strict";a.d(t,{LT:function(){return o},cL:function(){return s},lZ:function(){return r}});var n=a(44735),i=(a(79432),a(27495),a(25440),{message:"",timestamp:0,result:null,code:0}),s=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return i.result=e,void 0!==t&&null!==t&&(i.message=t),void 0!==a&&0!==a&&(i.code=a,i._status=a),null!==s&&"object"===(0,n.A)(s)&&Object.keys(s).length>0&&(i._headers=s),i.timestamp=(new Date).getTime(),i},o=function(e){var t=e.url,a=t.split("?")[1];return a?JSON.parse('{"'+decodeURIComponent(a).replace(/"/g,'\\"').replace(/&/g,'","').replace(/=/g,'":"')+'"}'):{}},r=function(e){return e.body&&JSON.parse(e.body)}},99547:function(e,t,a){"use strict";a.d(t,{w:function(){return s}});var n=a(76338),i=a(95353),s={computed:(0,n.A)({},(0,i.aH)({isMobile:function(e){return e.app.isMobile}}))}},55499:function(e,t,a){"use strict";a.d(t,{A:function(){return N}});var n,i=a(85471),s=a(95353),o=a(26297),r=(a(26099),a(74053)),c=a.n(r),l=a(75314),d=a(11363),u={state:{sideCollapsed:!1,isMobile:!1,theme:"dark",layout:"",contentWidth:"",fixedHeader:!1,fixedSidebar:!1,autoHideHeader:!1,color:"",weak:!1,multiTab:!0,lang:"zh-CN",_antLocale:{}},mutations:(n={},(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)(n,l.cf,(function(e,t){e.sideCollapsed=t,c().set(l.cf,t)})),l.nd,(function(e,t){e.isMobile=t})),l.RM,(function(e,t){e.theme=t,c().set(l.RM,t)})),l.yG,(function(e,t){e.layout=t,c().set(l.yG,t)})),l.MV,(function(e,t){e.fixedHeader=t,c().set(l.MV,t)})),l.Fb,(function(e,t){e.fixedSidebar=t,c().set(l.Fb,t)})),l.sl,(function(e,t){e.contentWidth=t,c().set(l.sl,t)})),l.Wb,(function(e,t){e.autoHideHeader=t,c().set(l.Wb,t)})),l.Db,(function(e,t){e.color=t,c().set(l.Db,t)})),l.o6,(function(e,t){e.weak=t,c().set(l.o6,t)})),(0,o.A)((0,o.A)(n,l.$C,(function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e.lang=t,e._antLocale=a,c().set(l.$C,t)})),l.jc,(function(e,t){c().set(l.jc,t),e.multiTab=t}))),actions:{setLang:function(e,t){var a=e.commit;return new Promise((function(e,n){a(l.$C,t),(0,d.J4)(t).then((function(){e()})).catch((function(e){n(e)}))}))}}},m=u,f=a(76338),p=(a(62062),a(62010),a(26398)),h=a.n(p),g=a(505),b=a(67569);c().addPlugin(h());var y={state:{token:"",name:"",welcome:"",avatar:"",roles:[],info:{}},mutations:{SET_TOKEN:function(e,t){e.token=t},SET_NAME:function(e,t){var a=t.name,n=t.welcome;e.name=a,e.welcome=n},SET_AVATAR:function(e,t){e.avatar=t},SET_ROLES:function(e,t){e.roles=t},SET_INFO:function(e,t){e.info=t}},actions:{Login:function(e,t){var a=e.commit;return new Promise((function(e,n){(0,g.iD)(t).then((function(t){var i=t;0==i.status?(c().set(l.Xh,i.data.token,i.data.expireTime),a("SET_TOKEN",i.data.token),e()):n(i.msg)})).catch((function(e){n(e)}))}))},GetInfo:function(e){var t=e.commit;return new Promise((function(e,a){(0,g.Vp)().then((function(n){var i=n.result;if(i.role&&i.role.permissions.length>0){var s=(0,f.A)({},i.role);s.permissions=i.role.permissions.map((function(e){var t=(0,f.A)((0,f.A)({},e),{},{actionList:(e.actionEntitySet||{}).map((function(e){return e.action}))});return t})),s.permissionList=s.permissions.map((function(e){return e.permissionId})),i.role=s,t("SET_ROLES",s),t("SET_INFO",i),t("SET_NAME",{name:i.name,welcome:(0,b.dH)()}),t("SET_AVATAR",i.avatar),e(i)}else a(new Error("getInfo: roles must be a non-null array !"))})).catch((function(e){a(e)}))}))},Logout:function(e){var t=e.commit,a=e.state;return new Promise((function(e){(0,g.ri)(a.token).then((function(){t("SET_TOKEN",""),t("SET_ROLES",[]),c().remove(l.Xh),e()})).catch((function(e){console.log("logout fail:",e)})).finally((function(){}))}))}}},k=y,v=(a(28706),a(2008),a(74423),a(21699),a(83521)),C=a(67193),A=a.n(C);function w(e,t){return!0}function j(e,t){var a=e.filter((function(e){return!!w(t.permissionList,e)&&(e.children&&e.children.length&&(e.children=j(e.children,t)),!0)}));return a}var S={state:{routers:v.f,addRouters:[]},mutations:{SET_ROUTERS:function(e,t){e.addRouters=t,e.routers=v.f.concat(t)}},actions:{GenerateRoutes:function(e,t){var a=e.commit;return new Promise((function(e){var n=t.role,i=A()(v.y),s=j(i,n);a("SET_ROUTERS",s),e()}))}}},L=S,I={isMobile:function(e){return e.app.isMobile},lang:function(e){return e.app.lang},theme:function(e){return e.app.theme},color:function(e){return e.app.color},token:function(e){return e.user.token},avatar:function(e){return e.user.avatar},nickname:function(e){return e.user.name},welcome:function(e){return e.user.welcome},roles:function(e){return e.user.roles},userInfo:function(e){return e.user.info},addRouters:function(e){return e.permission.addRouters},multiTab:function(e){return e.app.multiTab}},x=I;i.Ay.use(s.Ay);var N=new s.Ay.Store({modules:{app:m,user:k,permission:L},state:{},mutations:{},actions:{},getters:x})},75314:function(e,t,a){"use strict";a.d(t,{$C:function(){return h},Db:function(){return m},Fb:function(){return l},MV:function(){return c},OT:function(){return g},RM:function(){return o},Wb:function(){return u},Xh:function(){return n},cf:function(){return i},jc:function(){return p},nd:function(){return s},o6:function(){return f},sl:function(){return d},yG:function(){return r}});var n="Access-Token",i="sidebar_type",s="is_mobile",o="nav_theme",r="layout",c="fixed_header",l="fixed_sidebar",d="content_width",u="auto_hide_header",m="color",f="weak",p="multi_tab",h="app_language",g={Fluid:"Fluid",Fixed:"Fixed"}},75769:function(e,t,a){"use strict";a.d(t,{He:function(){return h},Ay:function(){return g}});var n=a(55464),i=a(56252),s=(a(26099),a(72505)),o=a.n(s),r=a(55499),c=a(74053),l=a.n(c),d=a(56427),u={vm:{},install:function(e,t){this.installed||(this.installed=!0,t?(e.axios=t,Object.defineProperties(e.prototype,{axios:{get:function(){return t}},$http:{get:function(){return t}}})):console.error("You have to install axios"))}},m=a(75314),f=o().create({baseURL:"/",headers:{"Content-Type":"application/x-www-form-urlencoded"},timeout:6e3}),p=function(e){if(e.response){var t=e.response.data,a=l().get(m.Xh);403===e.response.status&&d.A.error({message:"Forbidden",description:t.msg}),401!==e.response.status||t.result&&t.result.isLogin||(d.A.error({message:"Unauthorized",description:"Authorization verification failed"}),a&&r.A.dispatch("Logout").then((function(){setTimeout((function(){window.location.reload()}),1500)})))}return Promise.reject(e)};f.interceptors.request.use((function(e){var t=l().get(m.Xh);return t&&(e.headers["agenttoken"]=t),e}),p),f.interceptors.response.use(function(){var e=(0,i.A)((0,n.A)().mark((function e(t){var a,i,s;return(0,n.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:return a=e.sent,i=a.data,i.code,s=i.msg,"請先登錄，無權限訪問agent"==s||"請先登錄"==s?(d.A.error({message:"重新登陆",description:"未登录或登录过期，请重新登录"}),r.A.dispatch("Logout").then((function(){setTimeout((function(){window.localStorage.clear(),window.location.reload()}),1500)}))):a.data||d.A.error({message:"网络错误",description:"网络错误，请稍后刷新页面重试！"}),e.abrupt("return",a.data);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());var h={vm:{},install:function(e){e.use(u,f)}},g=f},67569:function(e,t,a){"use strict";a.d(t,{Av:function(){return o},Z$:function(){return n},dH:function(){return i},lT:function(){return s}});a(27495);function n(){var e=new Date,t=e.getHours();return t<9?"早上好":t<=11?"上午好":t<=13?"中午好":t<20?"下午好":"晚上好"}function i(){var e=["休息一会儿吧","准备吃什么呢?","要不要打一把 DOTA","我猜你可能累了"],t=Math.floor(Math.random()*e.length);return e[t]}function s(){var e=window.navigator.userAgent,t=function(t){return e.indexOf(t)>=0},a=function(){return"ActiveXObject"in window}();return t("MSIE")||a}function o(e){var t=0;if(!e)return t;for(var a={},n=0;n<e.length;n++)a[e[n]]=(a[e[n]]||0)+1,t+=5/a[e[n]];var i={digits:/\d/.test(e),lower:/[a-z]/.test(e),upper:/[A-Z]/.test(e),nonWords:/\W/.test(e)},s=0;for(var o in i)s+=!0===i[o]?1:0;return t+=10*(s-1),parseInt(t)}},5839:function(e,t,a){var n={"./en-US":[45958],"./en-US.js":[45958],"./en-US/account":[21970,980],"./en-US/account.js":[21970,980],"./en-US/account/settings":[36748,98],"./en-US/account/settings.js":[36748,98],"./en-US/dashboard":[12089,345],"./en-US/dashboard.js":[12089,345],"./en-US/dashboard/analysis":[77062,376],"./en-US/dashboard/analysis.js":[77062,376],"./en-US/form":[98031,533],"./en-US/form.js":[98031,533],"./en-US/form/basicForm":[14932,438],"./en-US/form/basicForm.js":[14932,438],"./en-US/global":[12610,418],"./en-US/global.js":[12610,418],"./en-US/menu":[53050,254],"./en-US/menu.js":[53050,254],"./en-US/result":[29780,924],"./en-US/result.js":[29780,924],"./en-US/result/fail":[78915,77],"./en-US/result/fail.js":[78915,77],"./en-US/result/success":[10088,802],"./en-US/result/success.js":[10088,802],"./en-US/setting":[18749,729],"./en-US/setting.js":[18749,729],"./en-US/user":[28666,606],"./en-US/user.js":[28666,606],"./zh-CN":[48188,644],"./zh-CN.js":[48188,644],"./zh-CN/account":[77844],"./zh-CN/account.js":[77844],"./zh-CN/account/settings":[54142],"./zh-CN/account/settings.js":[54142],"./zh-CN/dashboard":[91771],"./zh-CN/dashboard.js":[91771],"./zh-CN/dashboard/analysis":[22492],"./zh-CN/dashboard/analysis.js":[22492],"./zh-CN/form":[89065],"./zh-CN/form.js":[89065],"./zh-CN/form/basicForm":[97178],"./zh-CN/form/basicForm.js":[97178],"./zh-CN/global":[39668],"./zh-CN/global.js":[39668],"./zh-CN/menu":[14868],"./zh-CN/menu.js":[14868],"./zh-CN/result":[11250],"./zh-CN/result.js":[11250],"./zh-CN/result/fail":[27669],"./zh-CN/result/fail.js":[27669],"./zh-CN/result/success":[64158],"./zh-CN/result/success.js":[64158],"./zh-CN/setting":[91363],"./zh-CN/setting.js":[91363],"./zh-CN/user":[11016],"./zh-CN/user.js":[11016]};function i(e){if(!a.o(n,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=n[e],i=t[0];return Promise.all(t.slice(1).map(a.e)).then((function(){return a(i)}))}i.keys=function(){return Object.keys(n)},i.id=5839,e.exports=i},33153:function(e,t,a){"use strict";e.exports=a.p+"img/logo.c47eccef.png"},42634:function(){}},t={};function a(n){var i=t[n];if(void 0!==i)return i.exports;var s=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(s.exports,s,s.exports,a),s.loaded=!0,s.exports}a.m=e,function(){var e=[];a.O=function(t,n,i,s){if(!n){var o=1/0;for(d=0;d<e.length;d++){n=e[d][0],i=e[d][1],s=e[d][2];for(var r=!0,c=0;c<n.length;c++)(!1&s||o>=s)&&Object.keys(a.O).every((function(e){return a.O[e](n[c])}))?n.splice(c--,1):(r=!1,s<o&&(o=s));if(r){e.splice(d--,1);var l=i();void 0!==l&&(t=l)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[n,i,s]}}(),function(){a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,{a:t}),t}}(),function(){a.d=function(e,t){for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}}(),function(){a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce((function(t,n){return a.f[n](e,t),t}),[]))}}(),function(){a.u=function(e){return"js/"+({77:"lang-en-US-result-fail",98:"lang-en-US-account-settings",143:"fail",254:"lang-en-US-menu",345:"lang-en-US-dashboard",376:"lang-en-US-dashboard-analysis",418:"lang-en-US-global",438:"lang-en-US-form-basicForm",533:"lang-en-US-form",606:"lang-en-US-user",644:"lang-zh-CN",729:"lang-en-US-setting",802:"lang-en-US-result-success",806:"user",924:"lang-en-US-result",980:"lang-en-US-account"}[e]||e)+"."+{39:"73a71327",77:"097ce5ae",98:"ae1684c4",142:"bb1e6484",143:"1b828c2d",164:"50f8345b",221:"0891e7ba",254:"17d2485c",345:"d2b1cc4d",350:"2e4d378a",376:"b231fa77",418:"64c37786",438:"8bef5a03",533:"ac27fb11",606:"8bfcebbf",644:"5dfc3da6",655:"0728262e",729:"6fdf8e95",802:"2d1bcf83",806:"ed8d7020",924:"8fc9ab22",980:"4ea2b150"}[e]+".js"}}(),function(){a.miniCssF=function(e){return"css/"+(806===e?"user":e)+"."+{39:"c3b52819",142:"2b01eaba",164:"608f47c3",350:"e2f2bf79",806:"5f41fe1f"}[e]+".css"}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="vue-antd-pro:";a.l=function(n,i,s,o){if(e[n])e[n].push(i);else{var r,c;if(void 0!==s)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var u=l[d];if(u.getAttribute("src")==n||u.getAttribute("data-webpack")==t+s){r=u;break}}r||(c=!0,r=document.createElement("script"),r.charset="utf-8",r.timeout=120,a.nc&&r.setAttribute("nonce",a.nc),r.setAttribute("data-webpack",t+s),r.src=n),e[n]=[i];var m=function(t,a){r.onerror=r.onload=null,clearTimeout(f);var i=e[n];if(delete e[n],r.parentNode&&r.parentNode.removeChild(r),i&&i.forEach((function(e){return e(a)})),t)return t(a)},f=setTimeout(m.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=m.bind(null,r.onerror),r.onload=m.bind(null,r.onload),c&&document.head.appendChild(r)}}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){a.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){a.p=""}(),function(){if("undefined"!==typeof document){var e=function(e,t,n,i,s){var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",a.nc&&(o.nonce=a.nc);var r=function(a){if(o.onerror=o.onload=null,"load"===a.type)i();else{var n=a&&a.type,r=a&&a.target&&a.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+n+": "+r+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=n,c.request=r,o.parentNode&&o.parentNode.removeChild(o),s(c)}};return o.onerror=o.onload=r,o.href=t,n?n.parentNode.insertBefore(o,n.nextSibling):document.head.appendChild(o),o},t=function(e,t){for(var a=document.getElementsByTagName("link"),n=0;n<a.length;n++){var i=a[n],s=i.getAttribute("data-href")||i.getAttribute("href");if("stylesheet"===i.rel&&(s===e||s===t))return i}var o=document.getElementsByTagName("style");for(n=0;n<o.length;n++){i=o[n],s=i.getAttribute("data-href");if(s===e||s===t)return i}},n=function(n){return new Promise((function(i,s){var o=a.miniCssF(n),r=a.p+o;if(t(o,r))return i();e(n,r,null,i,s)}))},i={524:0};a.f.miniCss=function(e,t){var a={39:1,142:1,164:1,350:1,806:1};i[e]?t.push(i[e]):0!==i[e]&&a[e]&&t.push(i[e]=n(e).then((function(){i[e]=0}),(function(t){throw delete i[e],t})))}}}(),function(){var e={524:0};a.f.j=function(t,n){var i=a.o(e,t)?e[t]:void 0;if(0!==i)if(i)n.push(i[2]);else{var s=new Promise((function(a,n){i=e[t]=[a,n]}));n.push(i[2]=s);var o=a.p+a.u(t),r=new Error,c=function(n){if(a.o(e,t)&&(i=e[t],0!==i&&(e[t]=void 0),i)){var s=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;r.message="Loading chunk "+t+" failed.\n("+s+": "+o+")",r.name="ChunkLoadError",r.type=s,r.request=o,i[1](r)}};a.l(o,c,"chunk-"+t,t)}},a.O.j=function(t){return 0===e[t]};var t=function(t,n){var i,s,o=n[0],r=n[1],c=n[2],l=0;if(o.some((function(t){return 0!==e[t]}))){for(i in r)a.o(r,i)&&(a.m[i]=r[i]);if(c)var d=c(a)}for(t&&t(n);l<o.length;l++)s=o[l],a.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return a.O(d)},n=self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))}();var n=a.O(void 0,[504],(function(){return a(44300)}));n=a.O(n)})();