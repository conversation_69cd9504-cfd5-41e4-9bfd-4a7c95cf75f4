<template>
    <div>
        <a-modal title="IP" :width="500" :visible="show" :confirmLoading="loading" @ok="okHandle" @cancel="cancelHandle">
            <a-form :form="form" ref="form">
                <a-form-item label="IP" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入IP" v-decorator="['ipAddr', { rules: [{ required: true, message: '请输入IP', }] }]" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script>
import { whitelistList, whitelistAdd, whitelistDel } from '@/api/whitelist'
export default {
    components: {},
    props: {
        init: {
            type: Function,
            default: function () {},
        },
    },
    data() {
        return {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 7 },
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 13 },
            },
            form: this.$form.createForm(this),
            show: false,
            loading: false,
        }
    },
    methods: {
        cancelHandle() {
            this.show = false
            const form = this.$refs.form.form
            form.resetFields()
        },
        okHandle() {
            const form = this.$refs.form.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    this.loading = true
                    whitelistAdd(values).then((res) => {
                        if (res.status == 0) {
                            this.show = false
                            this.$message.success({ content: res.msg, duration: 2 })
                            form.resetFields()
                            this.init()
                        } else {
                            this.$message.error({ content: res.msg })
                        }
                        this.loading = false
                    })
                }
            })
        },
    },
}
</script>
