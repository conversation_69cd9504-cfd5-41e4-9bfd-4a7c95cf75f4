"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[1581],{81581:function(t,e,n){n.r(e),n.d(e,{default:function(){return k}});var a=function(){var t=this,e=t._self._c;return e("page-header-wrapper",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"plus"},on:{click:function(e){t.$refs.addwhitelistdialog.show=!0}}},[t._v("添加白名单")])],1)])],1)],1)],1)],1)]),e("a-card",{attrs:{bordered:!1}},[e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.dataList,rowKey:"phone"},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"agentName",fn:function(n,a){return e("span",{},[[e("div",[e("span",[t._v(t._s(a.agentName)+"（"+t._s(a.agentId)+"）")])])]],2)}},{key:"isLock",fn:function(n,a){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:0==a.isLock?"green":"red"}},[t._v(t._s(0==a.isLock?"可交易":"不可交易"))])],1)]],2)}},{key:"isLogin",fn:function(n,a){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:0==a.isLogin?"green":"red"}},[t._v(t._s(0==a.isLogin?"可登陆":"不可登陆"))])],1)]],2)}},{key:"action",fn:function(n,a){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getDeluser(a)}},slot:"action"},[t._v("删除")])]}}])})],1),e("addwhitelistdialog",{ref:"addwhitelistdialog",attrs:{init:t.init}})],1)},i=[],s=n(75769),o=n(55373),r=n.n(o),l={whitelistList:"/admin/whitelist/list.do",whitelistAdd:"/admin/whitelist/add.do",whitelistDel:"/admin/whitelist/del.do"};function u(t){return console.log(t),(0,s.Ay)({url:l.whitelistList,method:"get",params:t})}function c(t){return(0,s.Ay)({url:l.whitelistAdd,method:"post",data:r().stringify(t)})}function d(t){return(0,s.Ay)({url:l.whitelistDel,method:"post",data:r().stringify(t)})}var g=function(){var t=this,e=t._self._c;return e("div",[e("a-modal",{attrs:{title:"IP",width:500,visible:t.show,confirmLoading:t.loading},on:{ok:t.okHandle,cancel:t.cancelHandle}},[e("a-form",{ref:"form",attrs:{form:t.form}},[e("a-form-item",{attrs:{label:"IP",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["ipAddr",{rules:[{required:!0,message:"请输入IP"}]}],expression:"['ipAddr', { rules: [{ required: true, message: '请输入IP', }] }]"}],attrs:{placeholder:"请输入IP"}})],1)],1)],1)],1)},h=[],p={components:{},props:{init:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},form:this.$form.createForm(this),show:!1,loading:!1}},methods:{cancelHandle:function(){this.show=!1;var t=this.$refs.form.form;t.resetFields()},okHandle:function(){var t=this,e=this.$refs.form.form;e.validateFields((function(n,a){n||(t.loading=!0,c(a).then((function(n){0==n.status?(t.show=!1,t.$message.success({content:n.msg,duration:2}),e.resetFields(),t.init()):t.$message.error({content:n.msg}),t.loading=!1})))}))}}},m=p,f=n(81656),w=(0,f.A)(m,g,h,!1,null,null,null),v=w.exports,y=(n(95093),{name:"Agentlist",components:{addwhitelistdialog:v},data:function(){var t=this;return{queryParam:{pageNum:1,pageSize:10},labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},columns:[{title:"ID",dataIndex:"id",align:"center"},{title:"IP",dataIndex:"ipAddr",align:"center"},{title:"操作",key:"action",align:"center",scopedSlots:{customRender:"action"}}],dataList:[],pagination:{total:0,pageSize:10,current:1,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,n){return t.onSizeChange(e,n)},onChange:function(e,n){return t.onPageChange(e,n)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,agentqueryParam:{pageNum:1,pageSize:100},currentDetails:{}}},created:function(){this.getWhiteList()},methods:{getDeluser:function(t){var e=this;this.$confirm({title:"提示",content:"确认删除IP！",onOk:function(){var n={id:t.id};d(n).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.init()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},init:function(){this.queryParam={pageNum:1,pageSize:10},this.pagination.current=1,this.getWhiteList()},editInit:function(){this.getWhiteList()},getWhiteList:function(){var t=this,e=this;this.loading=!0,u(this.queryParam).then((function(n){t.dataList=n.data.list,t.pagination.total=n.data.total,setTimeout((function(){e.loading=!1}),500)}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.pagination.current=t,this.getWhiteList()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.pagination.current=t,this.queryParam.pageSize=e,this.getWhiteList()},handleTableChange:function(){}}}),C=y,b=(0,f.A)(C,a,i,!1,null,null,null),k=b.exports}}]);