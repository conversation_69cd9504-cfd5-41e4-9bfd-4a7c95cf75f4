(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[221],{81221:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ee}});var r=function(){var e=this,t=this,a=t._self._c;return a("page-header-wrapper",[a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"用户筛选"}},[a("a-select",{attrs:{placeholder:"请选择用户类型","default-value":{key:"0"}},model:{value:t.queryParam.accountType,callback:function(e){t.$set(t.queryParam,"accountType",e)},expression:"queryParam.accountType"}},[a("a-select-option",{attrs:{value:0}},[t._v("真实用户")]),a("a-select-option",{attrs:{value:1}},[t._v("模拟用户")])],1)],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"下级代理"}},[a("a-select",{attrs:{placeholder:"请选择下级代理"},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(e,r){return a("a-select-option",{key:r,attrs:{value:e.id}},[t._v(t._s(e.agentName)+" ")])})),1)],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"真实姓名"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写真实姓名"},model:{value:t.queryParam.realName,callback:function(e){t.$set(t.queryParam,"realName",e)},expression:"queryParam.realName"}})],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"用户手机"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写用户手机号"},model:{value:t.queryParam.phone,callback:function(e){t.$set(t.queryParam,"phone",e)},expression:"queryParam.phone"}})],1)],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{icon:"redo"},on:{click:function(){return e.queryParam={}}}},[t._v("重置")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getuserList()}}},[t._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"plus"},on:{click:function(e){t.$refs.adduserdialog.addUserdialog=!0}}},[t._v("添加账户")])],1)])],1)],1)],1)],1)]),a("a-card",{attrs:{bordered:!1}},[a("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.dataList,rowKey:"phone"},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"agentName",fn:function(e,r){return a("span",{},[[a("div",[a("span",[t._v(t._s(r.agentName)+"（"+t._s(r.agentId)+"）")])])]],2)}},{key:"isLock",fn:function(e,r){return a("span",{},[[a("div",[a("a-tag",{attrs:{color:0==r.isLock?"green":"red"}},[t._v(t._s(0==r.isLock?"可交易":"不可交易"))])],1)]],2)}},{key:"isLogin",fn:function(e,r){return a("span",{},[[a("div",[a("a-tag",{attrs:{color:0==r.isLogin?"green":"red"}},[t._v(t._s(0==r.isLogin?"可登陆":"不可登陆"))])],1)]],2)}},{key:"action",fn:function(e,r){return[a("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){t.currentDetails=r,t.$refs.detailuserdialog.userDialog=!0}},slot:"action"},[t._v("用户详情")])]}}])})],1),a("adduserdialog",{ref:"adduserdialog",attrs:{getinit:t.getinit,agentlist:t.agentlist}}),a("detailuserdialog",{ref:"detailuserdialog",attrs:{currentDetails:t.currentDetails}}),a("EditUserinfodialog",{ref:"EditUserinfodialog",attrs:{getinit:t.geteditinit,agentlist:t.agentlist}}),a("editUserbankdialog",{ref:"editUserbankdialog",attrs:{getinit:t.geteditinit}}),a("editCapitaluserdialog",{ref:"editCapitaluserdialog",attrs:{getinit:t.geteditinit}}),a("audituserdialog",{ref:"audituserdialog",attrs:{currentDetails:t.currentDetails,getinit:t.geteditinit}}),a("rejectdialoglog",{ref:"rejectdialog",attrs:{currentDetails:t.currentDetails,getinit:t.geteditinit}})],1)},i=[],s=(a(9868),a(80157)),n=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"添加用户(添加的金额默认为融资资金)",width:500,visible:e.addUserdialog,confirmLoading:e.addUserDialogloading},on:{ok:e.OkaddUserdialog,cancel:e.CanceladdUserdialog}},[t("a-form",{ref:"addUserform",attrs:{form:e.addUserform}},[t("a-form-item",{attrs:{label:"账号类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["accountType",{rules:[{required:!0,message:"请选择账号类型"}]}],expression:"['accountType', { rules: [{ required: true, message: '请选择账号类型', }] }]"}],attrs:{placeholder:"请选择账号类型"}},[t("a-select-option",{attrs:{value:"0"}},[e._v("正式")]),t("a-select-option",{attrs:{value:"1"}},[e._v("模拟")])],1)],1),t("a-form-item",{attrs:{label:"手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0,message:"请输入手机号"}]}],expression:"['phone', { rules: [{ required: true, message: '请输入手机号', }] }]"}],attrs:{placeholder:"请输入手机号"}})],1),t("a-form-item",{attrs:{label:"密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["pwd",{rules:[{required:!0,message:"请输入密码"}]}],expression:"['pwd', { rules: [{ required: true, message: '请输入密码', }] }]"}],attrs:{placeholder:"请输入密码"}})],1),t("a-form-item",{attrs:{label:"金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["amt",{rules:[{required:!0,message:"请输入金额"}]}],expression:"['amt', { rules: [{ required: true, message: '请输入金额', }] }]"}],attrs:{placeholder:"请输入金额"}})],1)],1)],1)],1)},o=[],l={components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},addUserform:this.$form.createForm(this),addUserdialog:!1,addUserDialogloading:!1}},methods:{CanceladdUserdialog:function(){this.addUserdialog=!1;var e=this.$refs.addUserform.form;e.resetFields()},OkaddUserdialog:function(){var e=this,t=this.$refs.addUserform.form;t.validateFields((function(a,r){a||(e.addUserDialogloading=!0,(0,s.wh)(r).then((function(a){0==a.status?(e.addUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.addUserDialogloading=!1})))}))}}},c=l,d=a(81656),u=(0,d.A)(c,n,o,!1,null,null,null),g=u.exports,m=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"用户详情",width:1e3,visible:e.userDialog,footer:!1},on:{cancel:function(t){e.userDialog=!1}}},[t("a-descriptions",{attrs:{bordered:"",title:e.currentDetails.realName?e.currentDetails.realName:"未认证",column:{xxl:3,xl:3,lg:3,md:3,sm:2,xs:1}}},[t("a-descriptions-item",{attrs:{label:"用户ID"}},[e._v(" "+e._s(e.currentDetails.id?e.currentDetails.id:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"手机号码"}},[e._v(" "+e._s(e.currentDetails.phone?e.currentDetails.phone:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"登录状态"}},[t("a-tag",{attrs:{color:1==e.currentDetails.positionType?"red":"green"}},[e._v(" "+e._s(1==e.currentDetails.isLogin?"不可登录":"正常")+" ")])],1),t("a-descriptions-item",{attrs:{label:"账号类型"}},[t("a-tag",{attrs:{color:1==e.currentDetails.accountType?"blue":"green"}},[e._v(" "+e._s(1==e.currentDetails.accountType?"模拟用户":"实盘用户")+" ")])],1),t("a-descriptions-item",{attrs:{label:"交易状态"}},[t("a-tag",{attrs:{color:1==e.currentDetails.isLock?"red":"green"}},[e._v(" "+e._s(1==e.currentDetails.isLock?"不可交易":"正常")+" ")])],1),t("a-descriptions-item",{attrs:{label:"所属代理"}},[e._v(" "+e._s(e.currentDetails.agentName?e.currentDetails.agentName:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"身份证号码"}},[e._v(" "+e._s(e.currentDetails.idCard?e.currentDetails.idCard:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"银行名称"}},[e._v(" "+e._s(e.bankInfo.bankName?e.bankInfo.bankName:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"支行名称"}},[e._v(" "+e._s(e.bankInfo.bankAddress?e.bankInfo.bankAddress:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"银行卡号"}},[e._v(" "+e._s(e.bankInfo.bankNo?e.bankInfo.bankNo:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"账户总资金"}},[e._v(" "+e._s((e.currentDetails.userAmt+e.currentDetails.userIndexAmt).toFixed(2))+" ")]),t("a-descriptions-item",{attrs:{label:"融资总资金"}},[e._v(" "+e._s(e.currentDetails.userAmt?e.currentDetails.userAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"融资可用资金"}},[e._v(" "+e._s(e.currentDetails.enableAmt?e.currentDetails.enableAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"融资冻结保证金"}},[e._v(" "+e._s(e.currentDetails.allFreezAmt?e.currentDetails.allFreezAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"融资平仓线"}},[e._v(" "+e._s(e.currentDetails.forceLine?e.currentDetails.forceLine:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"指数总资金"}},[e._v(" "+e._s(e.currentDetails.userIndexAmt?e.currentDetails.userIndexAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"指数可用资金"}},[e._v(" "+e._s(e.currentDetails.enableIndexAmt?e.currentDetails.enableIndexAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"指数冻结保证金"}},[e._v(" "+e._s(e.currentDetails.allIndexFreezAmt?e.currentDetails.allIndexFreezAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"指数平仓线"}},[e._v(" "+e._s(e.currentDetails.indexForceLine?e.currentDetails.indexForceLine:"0")+" ")])],1)],1)],1)},p=[],f={components:{},props:{currentDetails:{type:Object}},data:function(){return{userDialog:!1,bankInfo:{}}},watch:{userDialog:function(e){e&&this.currentDetails&&this.currentDetails.id&&this.getBankInfo()}},methods:{showDialog:function(e){this.userDialog=!0,e&&e.id&&this.getBankInfo(e.id)},getBankInfo:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,a=t||this.currentDetails&&this.currentDetails.id;if(a){var r={userId:a};(0,s.EV)(r).then((function(t){0===t.status?e.bankInfo=t.data||{}:(e.bankInfo={},console.error("获取银行卡信息失败:",t.msg))})).catch((function(t){e.bankInfo={},console.error("获取银行卡信息接口错误:",t)}))}}}},h=f,v=(0,d.A)(h,m,p,!1,null,null,null),b=v.exports,D=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"修改用户信息",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"所属代理",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentId",{}],expression:"['agentId', {}]"}],attrs:{placeholder:"请选择所属代理"}},e._l(e.agentlist,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.id}},[e._v(e._s(a.agentName)+" ")])})),1)],1),t("a-form-item",{attrs:{label:"手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{}],expression:"['phone', {}]"}],attrs:{placeholder:"请输入手机号"}})],1),t("a-form-item",{attrs:{label:"用户名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["realName",{}],expression:"['realName', {}]"}],attrs:{placeholder:"请输入用户名"}})],1),t("a-form-item",{attrs:{label:"密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["pwd",{}],expression:"['pwd', {}]"}],attrs:{placeholder:"请输入密码"}})],1),t("a-form-item",{attrs:{label:"身份证号码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["idCard",{}],expression:"['idCard', {}]"}],attrs:{placeholder:"请输入身份证号码"}})],1),t("a-form-item",{attrs:{label:"登录状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["isLogin",{}],expression:"['isLogin', {}]"}],attrs:{placeholder:"请选择登录状态"}},[t("a-select-option",{attrs:{value:0}},[e._v("可登录")]),t("a-select-option",{attrs:{value:1}},[e._v("不可交易")])],1)],1),t("a-form-item",{attrs:{label:"交易状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["isLock",{}],expression:"['isLock', {}]"}],attrs:{placeholder:"请选择交易状态"}},[t("a-select-option",{attrs:{value:0}},[e._v("可交易")]),t("a-select-option",{attrs:{value:1}},[e._v("不可交易")])],1)],1)],1)],1)],1)},w=[],C=(a(26099),a(23500),a(91863)),_=a.n(C),y={components:{},props:{getinit:{type:Function,default:function(){}},agentlist:{type:Array}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["agentId","phone","realName","pwd","idCard","isLogin","isLock"],currentDetails:{}}},methods:{getEditorder:function(e){var t=this;this.currentDetails=e,this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(_()(e,this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(r.id=e.currentDetails.id,e.editUserDialogloading=!0,(0,s.Rr)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},x=y,k=(0,d.A)(x,D,w,!1,null,null,null),U=k.exports,I=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"修改银行卡信息",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"银行名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["bankName",{}],expression:"['bankName', {}]"}],attrs:{placeholder:"请输入银行名称"}})],1),t("a-form-item",{attrs:{label:"银行卡",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["bankNo",{}],expression:"['bankNo', {}]"}],attrs:{placeholder:"请输入银行卡"}})],1),t("a-form-item",{attrs:{label:"支行地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["bankAddress",{}],expression:"['bankAddress', {}]"}],attrs:{placeholder:"请输入支行地址"}})],1)],1)],1)],1)},N=[],F={components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["bankName","bankAddress","bankNo"],currentDetails:{}}},methods:{getbankinfo:function(e){var t=this;this.currentDetails=e;var a={userId:e.id};(0,s.EV)(a).then((function(e){0==e.status&&t.getEditorder(e.data)}))},getEditorder:function(e){var t=this;this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(_()(e,this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(r.id=e.currentDetails.id,e.editUserDialogloading=!0,(0,s.lu)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},A=F,$=(0,d.A)(A,I,N,!1,null,null,null),L=$.exports,q=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"账户扣入款",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"用户id",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId",{}],expression:"['userId', {}]"}],attrs:{placeholder:"请输入用户id",disabled:""}})],1),t("a-form-item",{attrs:{label:"金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["amt",{rules:[{required:!0,message:"请输入金额"}]}],expression:"['amt', {rules: [{ required: true, message: '请输入金额', }] }]"}],attrs:{placeholder:"请输入金额"}})],1),t("a-form-item",{attrs:{label:"扣入款",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["direction",{rules:[{required:!0,message:"请选择扣入款"}]}],expression:"['direction', { rules: [{ required: true, message: '请选择扣入款', }] }]"}],attrs:{placeholder:"请选择扣入款"}},[t("a-select-option",{attrs:{value:"1"}},[e._v("扣款")]),t("a-select-option",{attrs:{value:"0"}},[e._v("入款")])],1)],1)],1)],1)],1)},S=[],P={components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["amt","direction","userId"],currentDetails:{}}},methods:{getEditorder:function(e){var t=this;this.currentDetails=e,this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(_()(e,this.fields)),this.editUserform.setFieldsValue(_()({userId:e.id},this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(e.editUserDialogloading=!0,(0,s.$W)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},j=P,O=(0,d.A)(j,q,S,!1,null,null,null),E=O.exports,T=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"实名认证审核",width:1e3,visible:e.userDialog,footer:!1},on:{cancel:function(t){e.userDialog=!1}}},[t("a-descriptions",{attrs:{bordered:"",title:e.currentDetails.realName?e.currentDetails.realName:"未认证",column:{xxl:3,xl:3,lg:3,md:3,sm:2,xs:1}}},[t("a-descriptions-item",{attrs:{label:"真实姓名"}},[e._v(" "+e._s(e.currentDetails.realName?e.currentDetails.realName:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"认证状态"}},[t("a-tag",{attrs:{color:0==e.currentDetails.isActive||1==e.currentDetails.isActive?"blue":2==e.currentDetails.isActive?"green":"red"}},[e._v(" "+e._s(0==e.currentDetails.isActive?"待认证":1==e.currentDetails.isActive?"待审核":2==e.currentDetails.isActive?"认证成功":"驳回")+" ")])],1),3==e.currentDetails.isActive?t("a-descriptions-item",{attrs:{label:"驳回原因"}},[e._v(" "+e._s(e.currentDetails.authMsg?e.currentDetails.authMsg:"--")+" ")]):e._e(),t("a-descriptions-item",{attrs:{label:"身份证号码"}},[e._v(" "+e._s(e.currentDetails.idCard?e.currentDetails.idCard:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册ip"}},[e._v(" "+e._s(e.currentDetails.regIp?e.currentDetails.regIp:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册地址"}},[e._v(" "+e._s(e.currentDetails.regAddress?e.currentDetails.regAddress:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册时间"}},[e._v(" "+e._s(e._f("moment")(e.currentDetails.regTime))+" ")]),t("a-descriptions-item",{attrs:{label:"身份证正面"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img2Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img2Key)}}})]),t("a-descriptions-item",{attrs:{label:"身份证背面"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img1Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img1Key)}}})]),t("a-descriptions-item",{attrs:{label:"手持身份证"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img3Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img3Key)}}})])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.currentDetails.isActive||1==e.currentDetails.isActive,expression:"currentDetails.isActive == 0 || currentDetails.isActive == 1"}],staticStyle:{"margin-top":"20px",display:"flex","justify-content":"center"}},[t("a-button",{attrs:{type:"danger"},on:{click:function(t){e.userDialog=!1,e.bohuidialog=!0}}},[e._v(" 驳回 ")]),t("a-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.gettongguo(2)}}},[e._v(" 通过 ")])],1)],1),t("a-modal",{attrs:{title:"驳回原因",width:500,visible:e.bohuidialog,confirmLoading:e.bohuidialogloading},on:{ok:e.Okbohuidialog,cancel:e.Cancelbohuidialog}},[t("a-form",{ref:"bohuiform",attrs:{form:e.bohuiform}},[t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["authMsg",{rules:[{required:!0,message:"请输入驳回原因"}]}],expression:"['authMsg', { rules: [{ required: true, message: '请输入驳回原因', }] }]"}],attrs:{placeholder:"请输入驳回原因"}})],1)],1)],1),t("a-modal",{attrs:{visible:e.previewVisible,footer:null},on:{cancel:function(t){e.previewVisible=!1}}},[t("img",{staticStyle:{width:"100%"},attrs:{alt:"example",src:e.previewImage}})])],1)},z=[],V={components:{},props:{currentDetails:{type:Object},getinit:{type:Function,default:function(){}}},data:function(){return{userDialog:!1,bohuidialog:!1,bohuidialogloading:!1,bohuiform:this.$form.createForm(this),previewVisible:!1,previewImage:""}},methods:{previewImageFun:function(e){e&&(this.previewImage=e,this.previewVisible=!0)},Okbohuidialog:function(){var e=this,t=this.$refs.bohuiform.form;t.validateFields((function(a,r){a||(r.userId=e.currentDetails.id,r.state=3,e.bohuidialogloading=!0,(0,s.iE)(r).then((function(a){0==a.status?(e.bohuidialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.bohuidialogloading=!1})))}))},Cancelbohuidialog:function(){this.bohuidialog=!1;var e=this.$refs.bohuiform.form;e.resetFields()},gettongguo:function(e){var t=this,a={userId:this.currentDetails.id,state:e};(0,s.iE)(a).then((function(e){0==e.status?(t.userDialog=!1,t.getinit()):t.$message.error({content:e.msg}),t.userDialog=!1}))}}},M=V,R=(0,d.A)(M,T,z,!1,null,null,null),K=R.exports,B=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"合同审核",width:1e3,visible:e.userDialog,footer:!1},on:{cancel:function(t){e.userDialog=!1}}},[t("a-descriptions",{attrs:{bordered:"",title:e.currentDetails.signatureMsg?"已签合同":"未签合同",column:{xxl:3,xl:3,lg:3,md:3,sm:2,xs:1}}},[t("a-descriptions-item",{attrs:{label:"签名图片"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.signatureMsg,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.signatureMsg)}}})])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:null!=e.currentDetails.signatureMsg&&e.currentDetails.signatureMsg.length>2,expression:"currentDetails.signatureMsg != null && currentDetails.signatureMsg.length > 2"}],staticStyle:{"margin-top":"20px",display:"flex","justify-content":"center"}})],1),t("a-modal",{attrs:{title:"确认驳回",width:500,visible:e.bohuidialog,confirmLoading:e.bohuidialogloading},on:{ok:e.Okbohuidialog,cancel:e.Cancelbohuidialog}}),t("a-modal",{attrs:{visible:e.previewVisible,footer:null},on:{cancel:function(t){e.previewVisible=!1}}},[t("img",{staticStyle:{width:"100%"},attrs:{alt:"example",src:e.previewImage}})])],1)},G=[],W={components:{},props:{currentDetails:{type:Object},getinit:{type:Function,default:function(){}}},data:function(){return{userDialog:!1,bohuidialog:!1,bohuidialogloading:!1,bohuiform:this.$form.createForm(this),previewVisible:!1,previewImage:""}},methods:{previewImageFun:function(e){e&&(this.previewImage=e,this.previewVisible=!0)},postRejectSignature:function(e){var t=this;this.userDialog=!1,this.$confirm({title:"驳回开户合同",content:"确认驳回此用户的开户合同?",onOk:function(){var a={userId:e.id};(0,s.hS)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getinit()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},Okbohuidialog:function(){var e=this,t=this.$refs.bohuiform.form;t.validateFields((function(a,r){a||(r.userId=e.currentDetails.id,r.state=3,e.bohuidialogloading=!0,(0,s.iE)(r).then((function(a){0==a.status?(e.bohuidialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.bohuidialogloading=!1})))}))},Cancelbohuidialog:function(){this.bohuidialog=!1;var e=this.$refs.bohuiform.form;e.resetFields()},gettongguo:function(e){var t=this,a={userId:this.currentDetails.id,state:e};(0,s.iE)(a).then((function(e){0==e.status?(t.userDialog=!1,t.getinit()):t.$message.error({content:e.msg}),t.userDialog=!1}))}}},Y=W,H=(0,d.A)(Y,B,G,!1,null,null,null),J=H.exports,Q={name:"Agentlist",components:{adduserdialog:g,detailuserdialog:b,EditUserinfodialog:U,editUserbankdialog:L,editCapitaluserdialog:E,audituserdialog:K,rejectdialoglog:J},data:function(){var e=this;return{queryParam:{agentId:void 0,realName:"",phone:"",pageNum:1,pageSize:10,accountType:0},labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},columns:[{title:"用户ID",dataIndex:"id",align:"center"},{title:"所属代理（Id）",scopedSlots:{customRender:"agentName"},align:"center"},{title:"手机号",dataIndex:"phone",align:"center"},{title:"真实姓名",dataIndex:"realName",align:"center"},{title:"总资金",dataIndex:"userAmt",align:"center",customRender:function(e,t,a){return e.toFixed(2)}},{title:"认证信息",dataIndex:"isActive",align:"center",customRender:function(e,t,a){return 0==e?"待认证":1==e?"待审核":2==e?"认证成功":3==e?"驳回":""}},{title:"交易状态",dataIndex:"isLock",align:"center",scopedSlots:{customRender:"isLock"}},{title:"登录状态",dataIndex:"isLogin",align:"center",scopedSlots:{customRender:"isLogin"}},{title:"操作",key:"action",align:"center",scopedSlots:{customRender:"action"}}],dataList:[],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,agentqueryParam:{pageNum:1,pageSize:100},agentlist:[],currentDetails:{}}},created:function(){this.getuserList(),this.getagentlist()},methods:{geteditinit:function(){this.getuserList()},getinit:function(){this.queryParam={agentId:void 0,realName:"",phone:"",pageNum:1,pageSize:10,accountType:0},this.getuserList()},getuserList:function(){var e=this,t=this;this.loading=!0,(0,s.Cv)(this.queryParam).then((function(a){e.dataList=a.data.list,e.pagination.total=a.data.total,setTimeout((function(){t.loading=!1}),500)}))},getagentlist:function(){var e=this;(0,s.LY)(this.agentqueryParam).then((function(t){e.agentlist=t.data.list}))},onPageChange:function(e,t){this.queryParam.pageNum=e,this.getuserList()},onSizeChange:function(e,t){this.queryParam.pageNum=e,this.queryParam.pageSize=t,this.getuserList()},handleTableChange:function(){}}},X=Q,Z=(0,d.A)(X,r,i,!1,null,null,null),ee=Z.exports},91863:function(e,t,a){var r=1/0,i=****************,s="[object Arguments]",n="[object Function]",o="[object GeneratorFunction]",l="[object Symbol]",c="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,d="object"==typeof self&&self&&self.Object===Object&&self,u=c||d||Function("return this")();function g(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function m(e,t){var a=-1,r=e?e.length:0,i=Array(r);while(++a<r)i[a]=t(e[a],a,e);return i}function p(e,t){var a=-1,r=t.length,i=e.length;while(++a<r)e[i+a]=t[a];return e}var f=Object.prototype,h=f.hasOwnProperty,v=f.toString,b=u.Symbol,D=f.propertyIsEnumerable,w=b?b.isConcatSpreadable:void 0,C=Math.max;function _(e,t,a,r,i){var s=-1,n=e.length;a||(a=U),i||(i=[]);while(++s<n){var o=e[s];t>0&&a(o)?t>1?_(o,t-1,a,r,i):p(i,o):r||(i[i.length]=o)}return i}function y(e,t){return e=Object(e),x(e,t,(function(t,a){return a in e}))}function x(e,t,a){var r=-1,i=t.length,s={};while(++r<i){var n=t[r],o=e[n];a(o,n)&&(s[n]=o)}return s}function k(e,t){return t=C(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,i=C(a.length-t,0),s=Array(i);while(++r<i)s[r]=a[t+r];r=-1;var n=Array(t+1);while(++r<t)n[r]=a[r];return n[t]=s,g(e,this,n)}}function U(e){return F(e)||N(e)||!!(w&&e&&e[w])}function I(e){if("string"==typeof e||j(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}function N(e){return $(e)&&h.call(e,"callee")&&(!D.call(e,"callee")||v.call(e)==s)}var F=Array.isArray;function A(e){return null!=e&&q(e.length)&&!L(e)}function $(e){return P(e)&&A(e)}function L(e){var t=S(e)?v.call(e):"";return t==n||t==o}function q(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=i}function S(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function P(e){return!!e&&"object"==typeof e}function j(e){return"symbol"==typeof e||P(e)&&v.call(e)==l}var O=k((function(e,t){return null==e?{}:y(e,m(_(t,1),I))}));e.exports=O}}]);