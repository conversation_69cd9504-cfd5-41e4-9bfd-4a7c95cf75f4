"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[164],{52164:function(t,a,e){e.r(a),e.d(a,{default:function(){return m}});var n=function(){var t=this,a=t._self._c;return a("page-header-wrapper",[a("a-card",{attrs:{bordered:!1}},[a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"下级代理"}},[a("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(a){t.$set(t.queryParam,"agentId",a)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(e,n){return a("a-select-option",{key:n,attrs:{value:e.id}},[t._v(" "+t._s(e.agentName)+" ")])})),1)],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"入金状态"}},[a("a-select",{attrs:{placeholder:"请选择入金状态"},model:{value:t.queryParam.state,callback:function(a){t.$set(t.queryParam,"state",a)},expression:"queryParam.state"}},[a("a-select-option",{attrs:{value:0}},[t._v("审核中")]),a("a-select-option",{attrs:{value:1}},[t._v("入金成功")]),a("a-select-option",{attrs:{value:2}},[t._v("入金失败")]),a("a-select-option",{attrs:{value:3}},[t._v("入金取消")])],1)],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"支付方式"}},[a("a-select",{attrs:{placeholder:"请选择支付方式"},model:{value:t.queryParam.payChannel,callback:function(a){t.$set(t.queryParam,"payChannel",a)},expression:"queryParam.payChannel"}},[a("a-select-option",{attrs:{value:0}},[t._v("支付宝")]),a("a-select-option",{attrs:{value:1}},[t._v("对公转账")]),a("a-select-option",{attrs:{value:""}},[t._v("模拟持仓")])],1)],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"真实姓名"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入真实姓名"},model:{value:t.queryParam.realName,callback:function(a){t.$set(t.queryParam,"realName",a)},expression:"queryParam.realName"}})],1)],1)],1),a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:12,lg:8,sm:24}},[a("a-form-item",[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(a){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),a("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id"},scopedSlots:t._u([{key:"payChannel",fn:function(e,n){return a("span",{},[[a("a-tag",{attrs:{color:0==n.payChannel?"blue":1==n.payChannel?"purple":"green"}},[t._v(" "+t._s(0==n.payChannel?"支付宝":1==n.payChannel?"对公转账":"现金转账")+" ")])]],2)}},{key:"orderStatus",fn:function(e,n){return a("span",{},[[a("a-tag",{attrs:{color:0==n.orderStatus?"blue":1==n.orderStatus?"green":2==n.orderStatus?"red":"orange"}},[t._v(" "+t._s(1==n.orderStatus?"成功":2==n.orderStatus?"失败":0==n.orderStatus?"审核中":"取消")+" ")])]],2)}}])})],1)],1)},r=[],i=(e(28706),e(60804)),s=e(80157),o=e(95093),l=e.n(o),u={name:"fundrecords",data:function(){var t=this;return{columns:[{title:"用户名称（ID）",dataIndex:"nickName",align:"center",width:180,customRender:function(t,a,e){return"".concat(a.nickName,"（").concat(a.userId,"）")}},{title:"订单号",dataIndex:"orderSn",align:"center"},{title:"充值金额",dataIndex:"payAmt",align:"center"},{title:"充值方式",dataIndex:"payChannel",align:"center",scopedSlots:{customRender:"payChannel"}},{title:"状态",dataIndex:"orderStatus",align:"center",scopedSlots:{customRender:"orderStatus"}},{title:"申请时间",dataIndex:"addTime",align:"center",width:180,customRender:function(t,a,e){return t?l()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"入金时间",dataIndex:"payTime",align:"center",width:180,customRender:function(t,a,e){return t?l()(t).format("YYYY-MM-DD HH:mm:ss"):""}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(a,e){return t.onSizeChange(a,e)},onChange:function(a,e){return t.onPageChange(a,e)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,agentId:void 0,state:void 0,payChannel:void 0,realName:""},datalist:[],agentlist:[],agentloading:!1,agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){this.getlist()},methods:{getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,agentId:void 0,state:void 0,payChannel:void 0,realName:""}},onChangeRangeDate:function(t,a){this.queryParam.beginTime=a[0],this.queryParam.endTime=a[1]},getagentlist:function(){var t=this,a=this;this.agentloading=!0,(0,s.LY)(this.agentqueryParam).then((function(e){t.agentlist=e.data.list,setTimeout((function(){a.agentloading=!1}),500)}))},getlist:function(){var t=this,a=this;this.loading=!0,(0,i.RD)(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,setTimeout((function(){a.loading=!1}),500)}))},onPageChange:function(t,a){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,a){this.queryParam.pageNum=t,this.queryParam.pageSize=a,this.getlist()}}},c=u,d=e(81656),g=(0,d.A)(c,n,r,!1,null,"15880935",null),m=g.exports},60804:function(t,a,e){e.d(a,{RD:function(){return o},eC:function(){return l}});var n=e(75769),r=e(55373),i=e.n(r),s={agentcashlist:"/agent/cash/list.do",agentrechargelist:"/agent/recharge/list.do",agentwithdrawlist:"/agent/withdraw/list.do"};function o(t){return(0,n.Ay)({url:s.agentrechargelist,method:"post",data:i().stringify(t)})}function l(t){return(0,n.Ay)({url:s.agentwithdrawlist,method:"post",data:i().stringify(t)})}}}]);