"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[9007],{57044:function(e,t,a){a.r(t),a.d(t,{default:function(){return f}});var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"account-settings-info-view"},[t("a-form",{ref:"addUserform",attrs:{layout:"vertical",form:e.addUserform}},[t("a-row",{attrs:{gutter:16,type:"flex",justify:"center"}},[t("a-col",{attrs:{order:e.isMobile?2:1,md:24,lg:16}},[t("a-form-item",{attrs:{label:"昵称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["adminName",{rules:[{required:!1,message:"请输入您的昵称"}]}],expression:"['adminName', { rules: [{ required: false, message: '请输入您的昵称', }] }]"}],attrs:{placeholder:"请输入您的昵称"}})],1),t("a-form-item",{attrs:{label:"ID"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["id",{rules:[{required:!0,message:"请输入您的ID"}]}],expression:"['id', { rules: [{ required: true, message: '请输入您的ID', }] }]"}],attrs:{placeholder:"请输入您的ID",disabled:""}})],1),t("a-form-item",{attrs:{label:"新密码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["adminPwd",{rules:[{required:!0,message:"请输入您的新密码"}]}],expression:"['adminPwd', { rules: [{ required: true, message: '请输入您的新密码', }] }]"}],attrs:{placeholder:"请输入您的新密码"}})],1),t("a-form-item",[t("a-button",{attrs:{type:"primary"},on:{click:e.saveinfo}},[e._v("保存基本信息")])],1)],1)],1)],1),t("avatar-modal",{ref:"modal",on:{ok:e.setavatar}})],1)},s=[],i=(a(48980),a(23146)),o=a(55434),n=a(7756),d=(a(91863),{mixins:[o.t],components:{AvatarModal:i.A},data:function(){return{preview:{},option:{img:"/avatar2.jpg",info:!0,size:1,outputType:"jpeg",canScale:!1,autoCrop:!0,autoCropWidth:180,autoCropHeight:180,fixedBox:!0,fixed:!0,fixedNumber:[1,1]},settingdetail:{},addUserform:this.$form.createForm(this),fields:["id","adminPwd","adminName"]}},mounted:function(){this.getnowuser()},methods:{getnowuser:function(){var e=this;(0,n.YB)().then((function(t){var a=t.data.list.findIndex((function(e){return e.adminPhone==window.localStorage.getItem("phones")}));e.addUserform.setFieldsValue({id:t.data.list[a].id,adminName:t.data.list[a].adminName})}))},setavatar:function(e){this.option.img=e},saveinfo:function(){var e=this,t=this.$refs.addUserform.form;t.validateFields((function(a,r){a||(0,n.T7)(r).then((function(a){0==a.status?(e.$message.success({content:"修改成功",duration:2}),t.resetFields(),e.getnowuser()):e.$message.error({content:a.msg})}))}))}}}),u=d,m=a(81656),l=(0,m.A)(u,r,s,!1,null,"fd908f24",null),f=l.exports}}]);