"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[9989],{39989:function(e,a,t){t.r(a),t.d(a,{default:function(){return d}});var n=function(){var e=this,a=e._self._c;return a("page-header-wrapper",[a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(a){e.$set(e.queryParam,"userId",a)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"真实姓名"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入真实姓名"},model:{value:e.queryParam.realName,callback:function(a){e.$set(e.queryParam,"realName",a)},expression:"queryParam.realName"}})],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"手机号"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入手机号"},model:{value:e.queryParam.phone,callback:function(a){e.$set(e.queryParam,"phone",a)},expression:"queryParam.phone"}})],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"消费类型"}},[a("a-select",{attrs:{placeholder:"请选择消费类型"},model:{value:e.queryParam.consumptionType,callback:function(a){e.$set(e.queryParam,"consumptionType",a)},expression:"queryParam.consumptionType"}},[a("a-select-option",{attrs:{value:""}},[e._v("全部")]),a("a-select-option",{attrs:{value:"POSITION_BUY"}},[e._v("购买股票")]),a("a-select-option",{attrs:{value:"WITHDRAW"}},[e._v("提现")]),a("a-select-option",{attrs:{value:"OTHER"}},[e._v("其他")])],1)],1)],1),a("a-col",{attrs:{md:12,lg:6,sm:24}},[a("a-form-item",{attrs:{label:"关联订单号"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入关联订单号"},model:{value:e.queryParam.relatedOrderSn,callback:function(a){e.$set(e.queryParam,"relatedOrderSn",a)},expression:"queryParam.relatedOrderSn"}})],1)],1),a("a-col",{attrs:{md:12,lg:8,sm:24}},[a("a-form-item",{attrs:{label:"时间范围"}},[a("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD HH:mm:ss",showTime:{defaultValue:[e.moment("00:00:00","HH:mm:ss"),e.moment("23:59:59","HH:mm:ss")]},placeholder:["开始时间","结束时间"]},model:{value:e.queryParam.timeRange,callback:function(a){e.$set(e.queryParam,"timeRange",a)},expression:"queryParam.timeRange"}})],1)],1),a("a-col",{attrs:{md:12,lg:8,sm:24}},[a("a-form-item",[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{icon:"redo"},on:{click:e.resetQueryParam}},[e._v("重置")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(a){e.queryParam.pageNum=1,e.getList()}}},[e._v("查询")])],1)])],1)],1)],1)],1)]),a("a-card",{attrs:{bordered:!1}},[a("a-table",{attrs:{bordered:"",loading:e.loading,pagination:e.pagination,columns:e.columns,"data-source":e.datalist,rowKey:"id"},scopedSlots:e._u([{key:"consumptionType",fn:function(t,n){return a("span",{},[["POSITION_BUY"===n.consumptionType?a("div",[e._v("购买股票")]):"WITHDRAW"===n.consumptionType?a("div",[e._v("提现")]):"OTHER"===n.consumptionType?a("div",[e._v("其他")]):a("div",[e._v(e._s(n.consumptionType))])]],2)}},{key:"amount",fn:function(t){return a("span",{},[[a("span",{staticStyle:{color:"#f5222d"}},[e._v(e._s(t))])]],2)}}])})],1)],1)},r=[],i=(t(28706),t(79432),t(26099),t(23500),t(60804)),o=t(95093),s=t.n(o),l={name:"FundConsumption",data:function(){var e=this;return{columns:[{title:"资金来源ID",dataIndex:"fundSourceId",align:"center",width:100},{title:"用户名称（ID）",dataIndex:"realName",align:"center",width:180,customRender:function(e,a,t){return"".concat(a.realName,"（").concat(a.userId,"）")}},{title:"用户手机号",dataIndex:"phone",align:"center",width:120},{title:"资金来源类型",dataIndex:"sourceType",align:"center",customRender:function(e,a,t){var n={RECHARGE:"充值",POSITION_SALE:"卖出股票",WITHDRAW_REJECT:"提现驳回返还",WITHDRAW_CANCEL:"提现取消返还",ADMIN_OPERATION:"管理员操作",ADMIN_DEPOSIT:"人工上分",ADMIN_WITHDRAW:"人工下分"};return n[e]||e}},{title:"消费类型",dataIndex:"consumptionType",align:"center",scopedSlots:{customRender:"consumptionType"}},{title:"消费金额",dataIndex:"consumptionAmount",align:"center",scopedSlots:{customRender:"amount"}},{title:"关联订单号",dataIndex:"relatedOrderSn",align:"center"},{title:"备注",dataIndex:"remark",align:"center"},{title:"消费时间",dataIndex:"createTime",align:"center",width:180,customRender:function(e,a,t){return e?s()(e).format("YYYY-MM-DD HH:mm:ss"):""}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(a,t){return e.onSizeChange(a,t)},onChange:function(a,t){return e.onPageChange(a,t)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,userId:"",realName:"",phone:"",consumptionType:"",relatedOrderSn:"",timeRange:[null,null]},datalist:[]}},created:function(){this.getList()},methods:{moment:s(),resetQueryParam:function(){this.queryParam={pageNum:1,pageSize:10,userId:"",realName:"",phone:"",consumptionType:"",relatedOrderSn:"",timeRange:[null,null]}},getList:function(){var e=this;this.loading=!0;var a={};Object.keys(this.queryParam).forEach((function(t){var n=e.queryParam[t];""!==n&&void 0!==n&&null!==n&&("timeRange"===t&&Array.isArray(n)&&n[0]&&n[1]?(a["startTime"]=s()(n[0]).format("YYYY-MM-DD HH:mm:ss"),a["endTime"]=s()(n[1]).format("YYYY-MM-DD HH:mm:ss")):"timeRange"!==t&&(a[t]=n))})),(0,i.rL)(a).then((function(a){e.datalist=a.data.list||[],e.pagination.total=a.data.total||0,e.loading=!1})).catch((function(){e.loading=!1}))},onPageChange:function(e,a){this.queryParam.pageNum=e,this.pagination.pageSize=a,this.getList()},onSizeChange:function(e,a){this.queryParam.pageNum=e,this.queryParam.pageSize=a,this.pagination.pageSize=a,this.getList()}}},u=l,m=t(81656),c=(0,m.A)(u,n,r,!1,null,"72db9510",null),d=c.exports}}]);