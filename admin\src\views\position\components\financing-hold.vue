<template>
    <div>
        <a-card :bordered="false">
            <div class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="持仓类型">
                                <a-select v-model="queryParam.positionType" placeholder="请选择持仓类型">
                                    <a-select-option :value="''">全部</a-select-option>
                                    <a-select-option :value="0">正式持仓</a-select-option>
                                    <a-select-option :value="1">模拟持仓</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="下级代理">
                                <a-select v-model="queryParam.agentId" placeholder="请选择下级代理" @focus="getagentlist" :loading="agentloading">
                                    <a-select-option v-for="(item, index) in agentlist" :key="index" :value="item.id">
                                        {{ item.agentName }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户Id">
                                <a-input v-model="queryParam.userId" style="width: 100%" placeholder="请输入用户Id" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户名">
                                <a-input v-model="queryParam.nickName" style="width: 100%" placeholder="请输入用户名" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="股票代码">
                                <a-input v-model="queryParam.stockCode" style="width: 100%" placeholder="请输入股票代码" />
                            </a-form-item>
                        </a-col>

                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="股票名称">
                                <a-input v-model="queryParam.stockName" style="width: 100%" placeholder="请输入股票名称" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="持仓订单号">
                                <a-input v-model="queryParam.positionSn" style="width: 100%" placeholder="请输入持仓订单号" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item>
                                <span class="table-page-search-submitButtons">
                                    <a-button @click="getqueryParam" icon="redo">
                                        重置</a-button>
                                    <a-button type="primary" icon="search" style="margin-left: 8px" @click="queryParam.pageNum = 1, getlist()">查询
                                    </a-button>
                                    <a-button type="primary" style="margin-left: 8px" @click="allChecked()">全选</a-button>
                                    <a-button type="primary" style="margin-left: 8px" @click="transaction()">一键成交</a-button>
                                    <a-button type="primary" style="margin-left: 8px" @click="virtualOneClickTransactionEvent()">模拟持仓一键成交</a-button>
                                    <!--                                    <a-button type="primary"  style="margin-left: 8px"-->
                                    <!--                                        @click="queryParam.pageNum = 1, getAllFinish(false)">选择项一键成交-->
                                    <!--                                    </a-button>-->
                                    <!--                                    <a-button type="primary"  style="margin-left: 8px"-->
                                    <!--                                           @click="queryParam.pageNum = 1, getAllFinish(true)">当前页全部成交-->
                                    <!--                                    </a-button>-->
                                </span>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-card>
        <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist" rowKey="id" :scroll="{ x: 2800 }">

            <template slot="selection" slot-scope="text, record, index">
                <a-checkbox :value="record.id" :checked="chooseList.indexOf(record.id) > -1" @change="onChange(record.id, $event)"></a-checkbox>
            </template>

            <span slot="stockName" slot-scope="text,record">
                <template>
                    <div>
                        <span style="margin-right:10px">{{ record.stockName }}</span>
                        <a-tag v-if='record.positionType != 3' :color="record.stockPlate == '科创' ? 'blue' : !record.stockPlate ? 'orange' : record.stockPlate == '创业' ? 'pink' : 'purple'">
                            {{ record.stockPlate == '科创' ? '科创' : !record.stockPlate ? '股票' : record.stockPlate }}
                        </a-tag>
                        <a-tag v-else :color="record.stockPlate == '科创' ? 'blue' : !record.stockPlate ? 'orange' : record.stockPlate == '创业' ? 'pink' : 'purple'">
                            大宗
                        </a-tag>
                        <p>({{ record.stockCode }})</p>
                    </div>
                </template>
            </span>

            <span slot="agentName" slot-scope="text,record">
                <template>
                    <div>
                        <span>{{ record.agentName }}</span>
                        <p>({{ record.agentCode }})</p>
                    </div>
                </template>
            </span>

            <span slot="positionType" slot-scope="text,record">
                <template>
                    <div>
                        <a-tag :color="record.positionType == 1 ? 'blue' : 'green'">
                            {{ record.positionType == 1 ? '模拟持仓' : '正式持仓' }}
                        </a-tag>
                    </div>
                </template>
            </span>

            <span slot="status" slot-scope="text,record">
                <template>
                    <div>
                        <a-tag :color="record.status == 1 ? 'blue' : 'red'">
                            {{ record.status != 1 ? '未成交' : '已经成交' }}
                        </a-tag>
                    </div>
                </template>
            </span>
            <span slot="orderDirection" slot-scope="text,record">
                <template>
                    <div>
                        <a-tag :color="record.orderDirection == '买涨' ? 'red' : 'green'">
                            {{ record.orderDirection }}
                        </a-tag>
                    </div>
                </template>
            </span>
            <span slot="now_price" slot-scope="text,record">
                <template>
                    <div>
                        <p :class="Number(record.now_price) - record.buyOrderPrice < 0 ? 'greens' : Number(record.now_price) - record.buyOrderPrice > 0 ? 'reds' : ''">
                            {{ record.now_price }}
                        </p>
                    </div>
                </template>
            </span>
            <span slot="profitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>
            <span slot="allProfitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>

            <template slot="action" slot-scope="text,record">
                <a slot="action" href="javascript:;" @click="Lockvisibledialog = true; clickpositionId = record.id" v-if="record.isLock == 0">锁仓</a>
                <a slot="action" href="javascript:;" @click="getLockopen(record.id)" v-else>解锁</a>
                <a-divider type="vertical" />
                <a slot="action" href="javascript:;" @click="getCompulsoryclosing(record.positionSn)">强制平仓</a>

                <a-divider v-if="record.status != 1" type="vertical" />
                <a v-if="record.status != 1" slot="action" href="javascript:;" @click="getBuyFinish(record)">点击成交</a>

                <a-divider v-if='record.positionType == 3 && record.buyNum ==0' type="vertical" />

                <a v-if='record.positionType == 3 && record.buyNum == 0' slot="action" href="javascript:;" @click="getDaZongQuxiao(record)">取消订单</a>

            </template>
        </a-table>
        <a-modal title="锁仓" :width="640" :visible="Lockvisibledialog" :confirmLoading="Lockvisibleloading" @ok="getDialogok" @cancel="handleCancel">
            <a-form :form="Lockvisibleform" ref="createModal">
                <a-form-item>
                    <a-input v-decorator="['lockMsg', { rules: [{ required: true, message: '请输入锁仓原因！', whitespace: true }] }]" placeholder="请输入锁仓原因！" />
                </a-form-item>
            </a-form>
        </a-modal>

        <a-modal title="购买成交" :width="640" :visible="ConfirmTreadDialog" :confirmLoading="ConfirmTreadDialogding" @ok="getTradeDialogok" @cancel="handleTreadCancel">
            <a-form :form="ConfirmTreadform" ref="createModal">
                <a-form-item>
                    <a-col :md="12" :lg="6" :sm="24">
                        <a-form-item label="成交比例">
                            <a-select v-model="buyRatio" placeholder="请选择成交比例">
                                <a-select-option :value="10">10%</a-select-option>
                                <a-select-option :value="20">20%</a-select-option>
                                <a-select-option :value="30">30%</a-select-option>
                                <a-select-option :value="40">40%</a-select-option>
                                <a-select-option :value="50">50%</a-select-option>
                                <a-select-option :value="60">60%</a-select-option>
                                <a-select-option :value="70">70%</a-select-option>
                                <a-select-option :value="80">80%</a-select-option>
                                <a-select-option :value="90">90%</a-select-option>
                                <a-select-option :value="100">100%</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-form-item>
            </a-form>
        </a-modal>

        <a-modal title="购买成交" :width="640" :visible="ConfirmTreadDialogAll" :confirmLoading="ConfirmTreadDialogdingAll" @ok="getTradeDialogokAll" @cancel="handleTreadCancelAll">
            <a-form :form="ConfirmTreadform" ref="createModal">
                <a-form-item>
                    <a-col :md="12" :lg="6" :sm="24">
                        <a-form-item label="成交比例">
                            <a-select v-model="buyRatio" placeholder="请选择成交比例">
                                <a-select-option :value="10">10%</a-select-option>
                                <a-select-option :value="20">20%</a-select-option>
                                <a-select-option :value="30">30%</a-select-option>
                                <a-select-option :value="40">40%</a-select-option>
                                <a-select-option :value="50">50%</a-select-option>
                                <a-select-option :value="60">60%</a-select-option>
                                <a-select-option :value="70">70%</a-select-option>
                                <a-select-option :value="80">80%</a-select-option>
                                <a-select-option :value="90">90%</a-select-option>
                                <a-select-option :value="100">100%</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-form-item>
            </a-form>
        </a-modal>

    </div>
</template>
<script>
import {
    positionlist,
    positionlock,
    positionsell,
    buyOneTread,
    buyAllTread,
    canCelOneTread,
    oneClickTransaction,
    virtualOneClickTransaction,
} from '@/api/position'
import { nextagent } from '@/api/home'
import moment from 'moment'
export default {
    name: 'financinghold',
    data() {
        return {
            columns: [
                {
                    title: '选择',
                    dataIndex: 'selection',
                    align: 'center',
                    width: 60,
                    scopedSlots: { customRender: 'selection' },
                },
                {
                    title: '融资名称',
                    dataIndex: 'stockName',
                    align: 'center',
                    width: 180,
                    scopedSlots: { customRender: 'stockName' },
                },

                {
                    title: '上级代理',
                    dataIndex: 'agentName',
                    align: 'center',
                    width: 180,
                    scopedSlots: { customRender: 'agentName' },
                },
                {
                    title: '账户类型',
                    dataIndex: 'positionType',
                    align: 'center',
                    scopedSlots: { customRender: 'positionType' },
                },
                {
                    title: '是否成交',
                    dataIndex: 'status',
                    align: 'center',
                    scopedSlots: { customRender: 'status' },
                },
                {
                    title: '用户名称（ID）',
                    dataIndex: 'nickName',
                    align: 'center',
                    customRender: (text, row, index) => {
                        return `${row.nickName}（${row.userId}）`
                    },
                },
                {
                    title: '持仓订单号（ID）',
                    dataIndex: 'positionSn',
                    align: 'center',
                    customRender: (text, row, index) => {
                        return `${row.positionSn}（${row.id}）`
                    },
                },
                {
                    title: '买卖方向',
                    dataIndex: 'orderDirection',
                    align: 'center',
                    scopedSlots: { customRender: 'orderDirection' },
                },
                {
                    title: '买入价',
                    dataIndex: 'buyOrderPrice',
                    align: 'center',
                    customRender: (text, row, index) => {
                        return text.toFixed(2)
                    },
                },
                {
                    title: '现价',
                    dataIndex: 'now_price',
                    align: 'center',
                    scopedSlots: { customRender: 'now_price' },
                },
                {
                    title: '浮动盈亏',
                    dataIndex: 'profitAndLose',
                    align: 'center',
                    scopedSlots: { customRender: 'profitAndLose' },
                },
                {
                    title: '总盈亏',
                    dataIndex: 'allProfitAndLose',
                    align: 'center',
                    scopedSlots: { customRender: 'allProfitAndLose' },
                },
                {
                    title: '总数量（股）',
                    dataIndex: 'orderNum',
                    align: 'center',
                },
                {
                    title: '总市值',
                    dataIndex: 'orderTotalPrice',
                    align: 'center',
                },

                {
                    title: '成交（股）',
                    dataIndex: 'buyNum',
                    align: 'center',
                },
                {
                    title: '成交金额',
                    dataIndex: 'buyPrice',
                    align: 'center',
                },

                {
                    title: '未成交（股）',
                    dataIndex: 'restNUm',
                    align: 'center',
                },
                {
                    title: '未成交金额',
                    dataIndex: 'restPrice',
                    align: 'center',
                },
                {
                    title: '未成交是否撤单',
                    dataIndex: 'backStatus',
                    align: 'center',
                    customRender: (text, row, index) => {
                        return text == 0 ? '未撤销' : '已撤销'
                    },
                },
                {
                    title: '杠杆倍数',
                    dataIndex: 'orderLever',
                    align: 'center',
                },
                {
                    title: '手续费',
                    dataIndex: 'orderFee',
                    align: 'center',
                },
                {
                    title: '印花税',
                    dataIndex: 'orderSpread',
                    align: 'center',
                },
                {
                    title: '留仓费',
                    dataIndex: 'orderStayFee',
                    align: 'center',
                },
                {
                    title: '留仓天数',
                    dataIndex: 'orderStayDays',
                    align: 'center',
                },
                {
                    title: '锁定原因',
                    dataIndex: 'lockMsg',
                    align: 'center',
                },
                {
                    title: '买入时间',
                    dataIndex: 'buyOrderTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    },
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'center',
                    fixed: 'right',
                    width: 250,
                    scopedSlots: { customRender: 'action' },
                },
            ],
            //表头
            pagination: {
                total: 0,
                pageSize: 10, //每页中显示10条数据
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'], //每页中显示的数据
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize), // 改变每页数量时更新显示
                onChange: (page, pageSize) => this.onPageChange(page, pageSize), //点击页码事件
                showTotal: (total) => `共有 ${total} 条数据`, //分页中显示总的数据
            },
            loading: false,
            //  state: 1-平仓  0-持仓
            queryParam: {
                pageNum: 1,
                pageSize: 10,
                positionType: '',
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: 0,
                stockCode: '',
                stockName: '',
                nickName: '',
            },
            datalist: [],
            agentlist: [],
            agentloading: false,
            Lockvisibledialog: false,
            Lockvisibleloading: false,
            ConfirmTreadDialog: false,
            ConfirmTreadDialogding: false,
            ConfirmTreadDialogAll: false,
            ConfirmTreadDialogdingAll: false,
            ConfirmTreadform: this.$form.createForm(this),
            Lockvisibleform: this.$form.createForm(this),
            clickpositionId: '',
            buyRatio: 100,
            agentqueryParam: {
                pageNum: 1,
                pageSize: 100,
            },
            chooseList: [],
            oneTreadData: {},
            isAll: false,
            ids: [],
        }
    },
    created() {
        this.getlist()
    },
    methods: {
        allChecked() {
            this.chooseList = []
            for (let i = 0; i < this.datalist.length; i++) {
                this.chooseList.push(this.datalist[i].id)
            }
        },
        virtualOneClickTransactionEvent() {
            virtualOneClickTransaction().then((res) => {
                if (res.status == 1) {
                    this.$message.error({ content: res.msg })
                } else {
                    this.$message.success({ content: res.data, duration: 2 })
                    this.getlist()
                }
            })
        },
        transaction() {
            if (this.chooseList.length <= 0) {
                this.$message.error({ content: '请选择ID' })
            } else {
                oneClickTransaction({
                    ids: this.chooseList.join(','),
                }).then((res) => {
                    if (res.status == 1) {
                        this.$message.error({ content: res.msg })
                    } else {
                        this.$message.success({ content: res.data, duration: 2 })
                        this.getlist()
                    }
                })
            }
        },
        getCompulsoryclosing(val) {
            var that = this
            this.$confirm({
                title: '提示',
                content: '确认要强制平仓吗?',
                onOk() {
                    var data = {
                        positionSn: val,
                    }
                    positionsell(data)
                        .then((res) => {
                            if (res.status == 0) {
                                that.$message.success({ content: res.msg, duration: 2 })
                                that.getlist()
                            } else {
                                that.$message.success({ content: res.msg, duration: 2 })
                            }
                        })
                        .catch((error) => {
                            // that.$message.error({ content: '平仓失败' });
                        })
                },
                onCancel() {
                    console.log('Cancel')
                    that.$message.success({ content: '平仓失败', duration: 2 })
                },
            })
        },
        getBuyFinish(val) {
            this.oneTreadData = {
                id: val.id,
                status: 1,
                userId: val.userId,
            }

            this.ConfirmTreadDialog = true
            var that = this

            // var data = {
            //   id: val.id,
            //   status: 1,
            //   userId: val.userId,
            // }
            // this.$confirm({
            //   title: '提示',
            //   content: '确认要购买成交吗?',
            //   onOk() {
            //     var data = {
            //       id: val.id,
            //       status: 1,
            //       userId: val.userId,
            //     }
            //
            //     buyOneTread(data).then(res => {
            //       if (res.status == 0) {
            //         that.$message.success({ content: res.msg, duration: 2 });
            //         that.getlist()
            //       } else {
            //         // that.$message.error({ content: '购买成交失败' });
            //       }
            //     }).catch(error => {
            //       // that.$message.error({ content: '购买成交失败' });
            //     })
            //   },
            //   onCancel() {
            //     console.log('Cancel');
            //   },
            // });
        },

        getDaZongQuxiao(val) {
            var that = this

            //
            // var data = {
            //   id: val.id,
            //   status: 1,
            //   userId: val.userId,
            // }
            this.$confirm({
                title: '提示',
                content: '确认要取消大宗吗?',
                onOk() {
                    var data = {
                        positionId: val.id,
                    }

                    canCelOneTread(data)
                        .then((res) => {
                            if (res.status == 0) {
                                that.$message.success({ content: res.data, duration: 2 })
                                that.getlist()
                            } else {
                                that.$message.success({ content: res.msg, duration: 2 })
                            }
                        })
                        .catch((error) => {
                            // that.$message.error({ content: '购买成交失败' });
                        })
                },
                onCancel() {
                    console.log('Cancel')
                },
            })
        },

        // http://127.0.0.1:8092/admin/position/batchAudit.do?ids=25,26,27,28&status=1&agentlds=5,1,9
        getAllFinish(allOrNot) {
            if (!allOrNot) {
                if (this.chooseList.length < 1) {
                    this.$message.error({ content: '请先选择需要成交的条目' })
                    return
                }
            }

            this.isAll = allOrNot
            this.ConfirmTreadDialogAll = true

            //
            // var  ids = "";
            // var agentIds = "";
            // if(isAll){
            //     ids = this.datalist.map(item => item.id).join(',');
            //     agentIds = this.datalist.map(item => item.agentId).join(',');
            // }else {
            //   var that = this
            //   if(this.chooseList.length<1){
            //     that.$message.error({ content: '请先选择需要成交的条目' });
            //     return;
            //   }
            //
            //
            //   var idlist = []
            //   var agnetlist = []
            //
            //   this.datalist.forEach(item => {
            //        if(this.chooseList.includes(item.id)){
            //          idlist.push(item.id);
            //          agnetlist.push(item.agentId);
            //        }
            //
            //   })
            //
            //    ids = idlist.map(item => item.id).join(',');
            //    agentIds = agnetlist.map(item => item.agentId).join(',');
            // }

            // this.$confirm({
            //   title: '提示',
            //   content: '确认要购买成交吗?',
            //   onOk() {
            //
            //     const queryParams = new URLSearchParams({ ids,  status: 1, agentIds }).toString();
            //
            //     console.log(3232323)
            //     console.log(queryParams)
            //     // admin/position/updateStatus.do 参数id,status(1为审核通过),userId
            //     buyAllTread(queryParams).then(res => {
            //       if (res.status == 0) {
            //         that.$message.success({ content: res.data, duration: 2 });
            //         that.getlist()
            //       } else {
            //         that.$message.success({ content: res.data, duration: 2 });
            //         // that.$message.error({ content: '购买成交失败' });
            //       }
            //     }).catch(error => {
            //       // that.$message.error({ content: '购买成交失败' });
            //       that.$message.error({ content:'购买成交失败', duration: 2 });
            //     })
            //   },
            //   onCancel() {
            //     console.log('Cancel');
            //   },
            // });
        },

        onChange(id, event) {
            if (event.target.checked) {
                this.chooseList.push(id)
            } else {
                const index = this.chooseList.indexOf(id)
                if (index > -1) {
                    this.chooseList.splice(index, 1)
                }
            }
            this.chooseList.forEach((item) => {
                console.log('Checked values:', item)
            })
        },

        getLockopen(val) {
            var that = this
            this.$confirm({
                title: '提示',
                content: '确认要解锁该持仓单?',
                onOk() {
                    var data = {
                        state: 0,
                        positionId: val,
                    }
                    positionlock(data).then((res) => {
                        if (res.status == 0) {
                            that.$message.success({ content: res.msg, duration: 2 })
                            that.getlist()
                        } else {
                            that.$message.error({ content: res.msg })
                        }
                    })
                },
                onCancel() {
                    console.log('Cancel')
                },
            })
        },

        handleTreadCancelAll() {
            this.ConfirmTreadDialogAll = false
            const form = this.$refs.createModal.form
            form.resetFields()
        },
        handleTreadCancel() {
            this.ConfirmTreadDialog = false
            const form = this.$refs.createModal.form
            form.resetFields()
        },

        handleCancel() {
            this.Lockvisibledialog = false
            const form = this.$refs.createModal.form
            form.resetFields()
        },

        getTradeDialogokAll() {
            var that = this
            var ids = ''
            var agentIds = ''
            var idlist = []
            var agnetlist = []
            const form = this.$refs.createModal.form
            // form.validateFields((errors, values) => {
            if (true) {
                // this.ConfirmTreadDialogdingAll = true

                if (this.isAll) {
                    ids = that.datalist.map((item) => item.id).join(',')
                    agentIds = that.datalist.map((item) => item.agentId).join(',')
                } else {
                    that.datalist.forEach((item) => {
                        if (that.chooseList.includes(item.id)) {
                            idlist.push(item.id)
                            agnetlist.push(item.agentId)
                        }
                    })

                    ids = idlist.join(',')
                    agentIds = agnetlist.join(',')
                }

                var buyRatio = that.buyRatio

                const queryParams = new URLSearchParams({ ids, status: 1, agentIds, buyRatio }).toString()

                console.log(queryParams)
                // console.log(ids)
                // console.log(agentIds)
                // admin/position/updateStatus.do 参数id,status(1为审核通过),userId
                buyAllTread(queryParams)
                    .then((res) => {
                        if (res.status == 0) {
                            this.ConfirmTreadDialogAll = false
                            this.$message.success({ content: res.data, duration: 2 })
                            this.getlist()
                        } else {
                            this.$message.success({ content: res.data, duration: 2 })
                            this.ConfirmTreadDialogdingAll = false
                        }
                        this.ConfirmTreadDialogdingAll = false
                    })
                    .catch((error) => {
                        this.$message.error({ content: '购买成交失败', duration: 2 })
                        this.ConfirmTreadDialogdingAll = false
                    })

                // buyOneTread( this.oneTreadData).then(res => {
                //   if (res.status == 0) {
                //     this.ConfirmTreadDialog = false
                //     that.$message.success({ content: res.msg, duration: 2 });
                //     that.getlist()
                //   } else {
                //     // that.$message.error({ content: '购买成交失败' });
                //   }
                //
                // }).catch(error => {
                //   // that.$message.error({ content: '购买成交失败' });
                //   this.ConfirmTreadDialogding = false
                // })
            }
            // })
        },

        getTradeDialogok() {
            var that = this
            const form = this.$refs.createModal.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    // this.ConfirmTreadDialogding = true
                    // var data = {
                    //   state: 1,
                    //   lockMsg: values.lockMsg,
                    //   positionId: this.clickpositionId,
                    // }

                    this.oneTreadData.buyRatio = this.buyRatio

                    buyOneTread(this.oneTreadData)
                        .then((res) => {
                            if (res.status == 0) {
                                this.ConfirmTreadDialog = false
                                that.$message.success({ content: res.data, duration: 2 })
                                that.getlist()
                            } else {
                                that.$message.success({ content: res.data, duration: 2 })
                            }
                        })
                        .catch((error) => {
                            // that.$message.error({ content: '购买成交失败' });
                            that.$message.success({ content: '购买成交失败', duration: 2 })
                            this.ConfirmTreadDialogding = false
                        })
                }
            })
        },

        getDialogok() {
            const form = this.$refs.createModal.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    this.Lockvisibleloading = true
                    var data = {
                        state: 1,
                        lockMsg: values.lockMsg,
                        positionId: this.clickpositionId,
                    }
                    positionlock(data)
                        .then((res) => {
                            if (res.status == 0) {
                                this.Lockvisibledialog = false
                                this.$message.success({ content: res.msg, duration: 2 })
                                form.resetFields()
                                this.getlist()
                            } else {
                                this.$message.error({ content: res.msg })
                            }
                            this.Lockvisibleloading = false
                        })
                        .catch((error) => {
                            reject(error)
                        })
                }
            })
        },
        getinit(state) {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                positionType: '',
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: state,
            }
            this.getlist()
        },
        getqueryParam() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                positionType: '',
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: this.queryParam.state,
            }
        },
        getagentlist() {
            var that = this
            this.agentloading = true
            nextagent(this.agentqueryParam).then((res) => {
                this.agentlist = res.data.list
                setTimeout(() => {
                    that.agentloading = false
                }, 500)
            })
        },
        getlist() {
            this.loading = true
            positionlist(this.queryParam).then((res) => {
                // this.datalist = res.data.list
                this.datalist = []
                res.data.list.forEach((item) => {
                    if (item.backStatus == 1 && item.buyNum == 0) {
                    } else {
                        this.datalist.push(item)
                    }
                })

                this.pagination.total = res.data.total
                this.loading = false
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.getlist()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.getlist()
        },
    },
}
</script>
<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
</style>