import request from '@/utils/request'
import qs from 'qs'
const userApi = {
    positionlist: '/agent/position/list.do', // 融资列表
    indexpositionlist: '/agent/index/position/list.do', // 指数列表
    futurespositionlist: '/agent/futures/position/list.do', // 期货列表
    positionsell: '/admin/position/sell.do', // 融资强制平仓
    positionlock: '/admin/position/lock.do', // 融资锁仓 解锁
    buyOneTread: '/admin/position/updateStatus.do', // 购买成交
    buyAllTread: 'admin/position/batchAudit.do',
    oneClickTransaction: '/admin/position/oneClickTransaction.do'
}

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function positionlist(parameter) {
    return request({
        url: userApi.positionlist,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function oneClickTransaction(parameter) {
    return request({
        url: userApi.oneClickTransaction,
        method: 'post',
        data: qs.stringify(parameter),
    })
}


export function buyAllTread(parameter) {
    return request({
        url: userApi.buyAllTread + '?' + parameter.toString(),

        // url: userApi.buyAllTread,
        method: 'get',
        // data: qs.stringify(parameter)
        data: parameter
    })
}


export function buyOneTread(parameter) {
    return request({
        url: userApi.buyOneTread,
        method: 'post',
        data: qs.stringify(parameter)
    })
}

export function positionlock(parameter) {
    return request({
        url: userApi.positionlock,
        method: 'post',
        data: qs.stringify(parameter),
    })
}


export function indexpositionlist(parameter) {
    return request({
        url: userApi.indexpositionlist,
        method: 'post',
        data: qs.stringify(parameter),
    })
}
export function futurespositionlist(parameter) {
    return request({
        url: userApi.futurespositionlist,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function positionsell(parameter) {
    return request({
        url: userApi.positionsell,
        method: 'post',
        data: qs.stringify(parameter),
    })
}