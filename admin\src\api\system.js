import request from '@/utils/request'
import qs from 'qs'

export function getInitConfig() {
    return request({
        url: 'admin/system/getInitConfig',
        method: 'post'
    })
}

export function getUploadToken(key = 'default') {
    return request({
        url: '/admin/system/getUploadToken',
        method: 'post',
        data: qs.stringify({ key })
    })
}

export function updateConfig(parameter) {
    return request({
        url: 'admin/system/updateConfig',
        method: 'post',
        data: qs.stringify(parameter)
    })
}
