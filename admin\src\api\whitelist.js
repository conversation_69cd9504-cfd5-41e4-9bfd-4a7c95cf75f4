import request from '@/utils/request'
import qs from 'qs'
const whitelistApi = {
  whitelistList: '/admin/whitelist/list.do',
  whitelistAdd: '/admin/whitelist/add.do',
  whitelistDel: '/admin/whitelist/del.do',
}

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function whitelistList(parameter) {
  console.log(parameter)
  return request({
    url: whitelistApi.whitelistList,
    method: 'get',
    params: parameter,
  })
}

export function whitelistAdd(parameter) {
  return request({
    url: whitelistApi.whitelistAdd,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function whitelistDel(parameter) {
  return request({
    url: whitelistApi.whitelistDel,
    method: 'post',
    data: qs.stringify(parameter),
  })
}