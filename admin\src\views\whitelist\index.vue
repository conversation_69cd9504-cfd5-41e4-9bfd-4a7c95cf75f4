<template>
    <page-header-wrapper>
        <a-card :bordered="false">
            <div class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item>
                                <span class="table-page-search-submitButtons">
                                    <a-button type="primary" icon="plus" style="margin-left: 8px"
                                        @click="$refs.addwhitelistdialog.show = true">添加白名单</a-button>
                                </span>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-card>
        <a-card :bordered="false">
            <a-table bordered :loading="loading" :pagination="pagination" @change="handleTableChange" :columns="columns"
                :data-source="dataList" rowKey="phone">
                <span slot="agentName" slot-scope="text,record">
                    <template>
                        <div>
                            <span>{{ record.agentName }}（{{ record.agentId }}）</span>
                        </div>
                    </template>
                </span>
                <span slot="isLock" slot-scope="text,record">
                    <template>
                        <div>
                            <a-tag :color="record.isLock == 0 ? 'green' : 'red'">{{ record.isLock == 0 ? '可交易' : '不可交易'
                                }}</a-tag>
                        </div>
                    </template>
                </span>
                <span slot="isLogin" slot-scope="text,record">
                    <template>
                        <div>
                            <a-tag :color="record.isLogin == 0 ? 'green' : 'red'">{{ record.isLogin == 0 ? '可登陆' :
                                '不可登陆' }}</a-tag>
                        </div>
                    </template>
                </span>
                <template slot="action" slot-scope="text,record">
                    <a slot="action" href="javascript:;" @click="getDeluser(record)">删除</a>
                </template>
            </a-table>
        </a-card>
        <addwhitelistdialog ref="addwhitelistdialog" :init="init"></addwhitelistdialog>
    </page-header-wrapper>
</template>
<script>
import { whitelistList, whitelistAdd, whitelistDel } from '@/api/whitelist'
import addwhitelistdialog from './components/addwhitelistdialog'
import moment from 'moment'
export default {
    name: 'Agentlist',
    components: {
        addwhitelistdialog
    },
    data() {
        return {
            queryParam: {
                pageNum: 1,
                pageSize: 10
            },
            labelCol: {
                xs: { span: 24 },
                sm: { span: 7 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 13 }
            },
            columns: [
                {
                    title: 'ID',
                    dataIndex: 'id',
                    align: 'center'
                },
                {
                    title: 'IP',
                    dataIndex: 'ipAddr',
                    align: 'center'
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'center',
                    scopedSlots: { customRender: 'action' }
                }
            ], // 表头
            dataList: [],
            pagination: {
                total: 0,
                pageSize: 10, // 每页中显示10条数据
                current: 1,
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize), // 改变每页数量时更新显示
                onChange: (page, pageSize) => this.onPageChange(page, pageSize), // 点击页码事件
                showTotal: (total) => `共有 ${total} 条数据` // 分页中显示总的数据
            },
            loading: false,
            agentqueryParam: {
                pageNum: 1,
                pageSize: 100
            },
            currentDetails: {}
        }
    },
    created() {
        this.getWhiteList()
    },
    methods: {
        getDeluser(val) {
            var that = this
            this.$confirm({
                title: '提示',
                content: '确认删除IP！',
                onOk() {
                    var data = {
                        id: val.id
                    }
                    whitelistDel(data).then((res) => {
                        if (res.status == 0) {
                            that.$message.success({ content: res.msg, duration: 2 })
                            that.init()
                        } else {
                            that.$message.error({ content: res.msg })
                        }
                    })
                },
                onCancel() {
                    console.log('Cancel')
                }
            })
        },
        init() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10
            }
            this.pagination.current = 1
            this.getWhiteList()
        },
        editInit() {
            this.getWhiteList()
        },
        getWhiteList() {
            var that = this
            this.loading = true
            whitelistList(this.queryParam).then((res) => {
                this.dataList = res.data.list
                this.pagination.total = res.data.total
                setTimeout(() => {
                    that.loading = false
                }, 500)
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.pagination.current = page
            this.getWhiteList()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.pagination.current = current
            this.queryParam.pageSize = pageSize
            this.getWhiteList()
        },
        handleTableChange() { }
    }
}
</script>
