<template>
    <page-header-wrapper>
        <a-card :bordered="false" title="平台设置">
            <div slot="extra">
                <a-button type="primary" :loading="saveLoading" @click="handleSave">
                    保存设置
                </a-button>
            </div>

            <a-spin :spinning="loading">
                <a-form :form="form" :label-col="labelCol" :wrapper-col="wrapperCol">
                    <a-row :gutter="48">
                        <a-col :span="24">
                            <a-divider orientation="left">支付设置</a-divider>
                        </a-col>
                    </a-row>

                    <a-row :gutter="48">
                        <a-col :md="12" :lg="12" :sm="24">
                            <a-form-item label="支付开关">
                                <a-radio-group
                                    v-decorator="['payEnabled', { rules: [{ required: true, message: '请选择支付开关状态' }] }]">
                                    <a-radio :value="1">
                                        <a-icon type="check-circle" style="color: #52c41a" />
                                        开启
                                    </a-radio>
                                    <a-radio :value="0">
                                        <a-icon type="close-circle" style="color: #f5222d" />
                                        关闭
                                    </a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                    </a-row>

                    <a-row :gutter="48">
                        <a-col :md="24" :lg="24" :sm="24">
                            <a-form-item label="支付关闭提示信息">
                                <a-textarea placeholder="请输入支付关闭时的提示信息，当支付开关关闭时，用户将看到此提示信息" :rows="4" v-decorator="['payDisabledMessage', {
                                    rules: [
                                        { required: true, message: '请输入支付关闭时的提示信息' },
                                        { max: 200, message: '提示信息不能超过200个字符' }
                                    ]
                                }]" />
                            </a-form-item>
                        </a-col>
                    </a-row>

                    <a-row :gutter="48">
                        <a-col :span="24">
                            <a-divider orientation="left">充值限制设置</a-divider>
                        </a-col>
                    </a-row>

                    <a-row :gutter="48">
                        <a-col :md="12" :lg="12" :sm="24">
                            <a-form-item label="充值次数限制开关">
                                <a-radio-group
                                    v-decorator="['rechargeLimitEnabled', { rules: [{ required: true, message: '请选择充值次数限制开关状态' }] }]">
                                    <a-radio :value="1">
                                        <a-icon type="check-circle" style="color: #52c41a" />
                                        开启
                                    </a-radio>
                                    <a-radio :value="0">
                                        <a-icon type="close-circle" style="color: #f5222d" />
                                        关闭
                                    </a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="12" :sm="24">
                            <a-form-item label="每日充值最大笔数">
                                <a-input-number
                                    v-decorator="['rechargeLimitDaily', { rules: [{ required: true, message: '请输入每日充值最大笔数' }] }]"
                                    :min="1" :max="999" placeholder="请输入每日最大充值笔数" style="width: 100%" />
                            </a-form-item>
                        </a-col>
                    </a-row>

                    <a-row :gutter="48">
                        <a-col :md="24" :lg="24" :sm="24">
                            <a-form-item label="充值次数超限提示信息">
                                <a-textarea placeholder="请输入当用户充值次数超过限制时显示的提示信息" :rows="4" v-decorator="['rechargeLimitMessage', {
                                    rules: [
                                        { required: true, message: '请输入充值次数超限时的提示信息' },
                                        { max: 200, message: '提示信息不能超过200个字符' }
                                    ]
                                }]" :maxLength="200" showCount />
                            </a-form-item>
                        </a-col>
                    </a-row>

                    <a-row :gutter="48">
                        <a-col :span="24">
                            <a-divider orientation="left">其他设置</a-divider>
                        </a-col>
                    </a-row>

                    <a-row :gutter="48">
                        <a-col :md="24" :lg="24" :sm="24">
                            <a-form-item label="七牛域名">
                                <a-input placeholder="七牛云存储域名地址" disabled v-decorator="['qiniuDomain', {}]" />
                                <div class="ant-form-explain">
                                    <a-icon type="info-circle" style="color: #1890ff" />
                                    此项为系统配置，暂不支持修改
                                </div>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </a-spin>
        </a-card>

        <!-- 操作提示 -->
        <a-card :bordered="false" title="操作说明" style="margin-top: 16px">
            <div class="setting-tips">
                <h4><a-icon type="bulb" /> 使用说明：</h4>
                <ul>
                    <li><strong>支付开关：</strong>控制系统整体支付功能的开启和关闭状态</li>
                    <li><strong>支付关闭提示信息：</strong>当支付功能关闭时，向用户显示的提示信息</li>
                    <li><strong>充值次数限制开关：</strong>控制是否启用每日充值次数限制功能</li>
                    <li><strong>每日充值最大笔数：</strong>用户每日可发起的充值订单最大数量（不论状态）</li>
                    <li><strong>充值次数超限提示信息：</strong>当用户超过每日充值次数限制时显示的提示信息</li>
                    <li><strong>七牛域名：</strong>系统文件存储域名，由系统管理员配置</li>
                </ul>

                <a-alert message="重要提示" description="支付开关关闭后，所有充值操作都将被禁用；充值次数限制可以有效防止恶意刷单行为，请合理设置限制数量。" type="warning"
                    show-icon style="margin-top: 16px" />
            </div>
        </a-card>
    </page-header-wrapper>
</template>

<script>
import { getInitConfig, updateConfig } from '@/api/system'

export default {
    name: 'PlatformSetting',
    data() {
        return {
            form: this.$form.createForm(this),
            loading: false,
            saveLoading: false,
            configData: {},
            labelCol: {
                xs: { span: 24 },
                sm: { span: 5 },
                md: { span: 5 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 19 },
                md: { span: 19 }
            }
        }
    },
    mounted() {
        this.getConfigData()
    },
    methods: {
        // 获取配置数据
        async getConfigData() {
            this.loading = true
            try {
                const response = await getInitConfig()
                console.log('获取配置数据响应:', response)
                if (response.status === 0 && response.success) {
                    this.configData = response.data
                    this.$nextTick(() => {
                        // 设置表单初始值
                        this.form.setFieldsValue({
                            payEnabled: this.configData.payEnabled,
                            payDisabledMessage: this.configData.payDisabledMessage || '',
                            qiniuDomain: this.configData.qiniuDomain || '',
                            rechargeLimitEnabled: this.configData.rechargeLimitEnabled,
                            rechargeLimitDaily: this.configData.rechargeLimitDaily,
                            rechargeLimitMessage: this.configData.rechargeLimitMessage || ''
                        })
                    })
                } else {
                    this.$message.error(response.msg || '获取配置失败')
                }
            } catch (error) {
                console.error('获取配置失败:', error)
                this.$message.error('获取配置失败，请稍后重试')
            } finally {
                this.loading = false
            }
        },

        // 保存配置
        handleSave() {
            this.form.validateFields((err, values) => {
                if (!err) {
                    this.saveConfig(values)
                }
            })
        },

        // 提交保存配置
        async saveConfig(values) {
            this.saveLoading = true
            try {
                const params = {
                    id: this.configData.id,
                    payEnabled: values.payEnabled,
                    payDisabledMessage: values.payDisabledMessage,
                    rechargeLimitEnabled: values.rechargeLimitEnabled,
                    rechargeLimitDaily: values.rechargeLimitDaily,
                    rechargeLimitMessage: values.rechargeLimitMessage
                    // qiniuDomain 不提交，因为是禁用字段
                }

                console.log('保存配置参数:', params)
                const response = await updateConfig(params)
                console.log('保存配置响应:', response)

                if (response.status === 0 && response.success) {
                    this.$message.success('保存成功')
                    // 重新获取配置数据
                    this.getConfigData()
                } else {
                    this.$message.error(response.msg || '保存失败')
                }
            } catch (error) {
                console.error('保存配置失败:', error)
                this.$message.error('保存失败，请稍后重试')
            } finally {
                this.saveLoading = false
            }
        }
    }
}
</script>

<style scoped>
.setting-tips {
    color: #666;
}

.setting-tips h4 {
    margin-bottom: 12px;
    color: #333;
}

.setting-tips ul {
    margin: 0;
    padding-left: 20px;
}

.setting-tips li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.ant-form-explain {
    margin-top: 4px;
    font-size: 12px;
    color: #999;
}

.ant-divider-horizontal.ant-divider-with-text-left::before {
    width: 5%;
}

.ant-divider-horizontal.ant-divider-with-text-left::after {
    width: 95%;
}
</style>
