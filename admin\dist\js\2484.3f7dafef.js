(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[2484],{5419:function(e){e.exports=function(e,t,a,r){var i="undefined"!==typeof r?[r,e]:[e],s=new Blob(i,{type:a||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(s,t);else{var n=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(s):window.webkitURL.createObjectURL(s),o=document.createElement("a");o.style.display="none",o.href=n,o.setAttribute("download",t),"undefined"===typeof o.download&&o.setAttribute("target","_blank"),document.body.appendChild(o),o.click(),setTimeout((function(){document.body.removeChild(o),window.URL.revokeObjectURL(n)}),200)}}},22484:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return we}});var r=function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"table-page-search-wrapper"},[t("a-form",{attrs:{layout:"inline"}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户筛选"}},[t("a-select",{attrs:{placeholder:"请选择用户类型","default-value":{key:"0"}},model:{value:e.queryParam.accountType,callback:function(t){e.$set(e.queryParam,"accountType",t)},expression:"queryParam.accountType"}},[t("a-select-option",{attrs:{value:""}},[e._v("全部用户")]),t("a-select-option",{attrs:{value:0}},[e._v("真实用户")]),t("a-select-option",{attrs:{value:1}},[e._v("模拟用户")])],1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"认证状态"}},[t("a-select",{attrs:{placeholder:"请选择认证状态","default-value":{key:"0"}},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[t("a-select-option",{attrs:{value:""}},[e._v("全部用户")]),t("a-select-option",{attrs:{value:0}},[e._v("注册未实名")]),t("a-select-option",{attrs:{value:1}},[e._v("实名待审核")]),t("a-select-option",{attrs:{value:2}},[e._v("已实名")])],1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"下级代理"}},[t("a-select",{attrs:{placeholder:"请选择下级代理"},model:{value:e.queryParam.agentId,callback:function(t){e.$set(e.queryParam,"agentId",t)},expression:"queryParam.agentId"}},e._l(e.agentlist,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.id}},[e._v(e._s(a.agentName)+" ")])})),1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"真实姓名"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写真实姓名"},model:{value:e.queryParam.realName,callback:function(t){e.$set(e.queryParam,"realName",t)},expression:"queryParam.realName"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户手机"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请填写用户手机号"},model:{value:e.queryParam.phone,callback:function(t){e.$set(e.queryParam,"phone",t)},expression:"queryParam.phone"}})],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",[t("span",{staticClass:"table-page-search-submitButtons"},[t("a-button",{attrs:{icon:"redo"},on:{click:function(t){return e.resetParams()}}},[e._v("重置")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){e.queryParam.pageNum=1,e.pagination.current=1,e.getuserList()}}},[e._v("查询")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"plus"},on:{click:function(t){e.$refs.adduserdialog.addUserdialog=!0}}},[e._v("添加账户")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.exportUsers()}}},[e._v("导出用户")])],1)])],1)],1)],1)],1)]),t("a-card",{attrs:{bordered:!1}},[t("a-table",{attrs:{bordered:"",loading:e.loading,pagination:e.pagination,columns:e.columns,"data-source":e.dataList,rowKey:"phone"},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"agentName",fn:function(a,r){return t("span",{},[[t("div",[t("span",[e._v(e._s(r.agentName)+"（"+e._s(r.agentId)+"）")])])]],2)}},{key:"isLock",fn:function(a,r){return t("span",{},[[t("div",[t("a-tag",{attrs:{color:0==r.isLock?"green":"red"}},[e._v(e._s(0==r.isLock?"可交易":"不可交易"))])],1)]],2)}},{key:"isLogin",fn:function(a,r){return t("span",{},[[t("div",[t("a-tag",{attrs:{color:0==r.isLogin?"green":"red"}},[e._v(e._s(0==r.isLogin?"可登陆":"不可登陆"))])],1)]],2)}},{key:"action",fn:function(a,r){return[t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){e.currentDetails=r,e.$refs.detailuserdialog.userDialog=!0}},slot:"action"},[e._v("用户详情")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){e.currentDetails=r,e.$refs.rechargeDialog.show=!0}},slot:"action"},[e._v("充值明细")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.$refs.EditUserinfodialog.getEditorder(r)}},slot:"action"},[e._v("编辑")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.$refs.editUserbankdialog.getbankinfo(r)}},slot:"action"},[e._v("银行卡")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.$refs.editCapitaluserdialog.getEditorder(r)}},slot:"action"},[e._v("资金")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.$refs.virtualEditCapitalUserDialog.getEditorder(r)}},slot:"action"},[e._v("虚拟资金")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){e.currentDetails=r,e.$refs.audituserdialog.userDialog=!0}},slot:"action"},[e._v("实名审核")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){e.currentDetails=r,e.$refs.rejectdialog.userDialog=!0}},slot:"action"},[e._v("合同审核")]),t("a-divider",{attrs:{type:"vertical"}}),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.getDeluser(r)}},slot:"action"},[e._v("删除")])]}}])})],1),t("adduserdialog",{ref:"adduserdialog",attrs:{getinit:e.getinit,agentlist:e.agentlist}}),t("detailuserdialog",{ref:"detailuserdialog",attrs:{currentDetails:e.currentDetails}}),t("EditUserinfodialog",{ref:"EditUserinfodialog",attrs:{getinit:e.geteditinit,agentlist:e.agentlist}}),t("editUserbankdialog",{ref:"editUserbankdialog",attrs:{getinit:e.geteditinit}}),t("editCapitaluserdialog",{ref:"editCapitaluserdialog",attrs:{getinit:e.geteditinit}}),t("virtualEditCapitalUserDialog",{ref:"virtualEditCapitalUserDialog",attrs:{getinit:e.geteditinit}}),t("audituserdialog",{ref:"audituserdialog",attrs:{currentDetails:e.currentDetails,getinit:e.geteditinit}}),t("rejectdialoglog",{ref:"rejectdialog",attrs:{currentDetails:e.currentDetails,getinit:e.geteditinit}}),t("rechargeDialog",{ref:"rechargeDialog",attrs:{currentDetails:e.currentDetails}})],1)},i=[],s=(a(2892),a(9868),a(27495),a(25440),a(80157)),n=a(5419),o=a.n(n),l=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"添加用户(添加的金额默认为融资资金)",width:500,visible:e.addUserdialog,confirmLoading:e.addUserDialogloading},on:{ok:e.OkaddUserdialog,cancel:e.CanceladdUserdialog}},[t("a-form",{ref:"addUserform",attrs:{form:e.addUserform}},[t("a-form-item",{attrs:{label:"所属代理",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentId",{rules:[{required:!0,message:"请选择所属代理"}]}],expression:"['agentId', { rules: [{ required: true, message: '请选择所属代理', }] }]"}],attrs:{placeholder:"请选择所属代理"}},e._l(e.agentlist,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.id}},[e._v(e._s(a.agentName)+" ")])})),1)],1),t("a-form-item",{attrs:{label:"账号类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["accountType",{rules:[{required:!0,message:"请选择账号类型"}]}],expression:"['accountType', { rules: [{ required: true, message: '请选择账号类型', }] }]"}],attrs:{placeholder:"请选择账号类型"}},[t("a-select-option",{attrs:{value:"0"}},[e._v("正式")]),t("a-select-option",{attrs:{value:"1"}},[e._v("模拟")])],1)],1),t("a-form-item",{attrs:{label:"手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0,message:"请输入手机号"}]}],expression:"['phone', { rules: [{ required: true, message: '请输入手机号', }] }]"}],attrs:{placeholder:"请输入手机号"}})],1),t("a-form-item",{attrs:{label:"密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["pwd",{rules:[{required:!0,message:"请输入密码"}]}],expression:"['pwd', { rules: [{ required: true, message: '请输入密码', }] }]"}],attrs:{placeholder:"请输入密码"}})],1),t("a-form-item",{attrs:{label:"金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["amt",{rules:[{required:!0,message:"请输入金额"}]}],expression:"['amt', { rules: [{ required: true, message: '请输入金额', }] }]"}],attrs:{placeholder:"请输入金额"}})],1)],1)],1)],1)},c=[],d={components:{},props:{getinit:{type:Function,default:function(){}},agentlist:{type:Array}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},addUserform:this.$form.createForm(this),addUserdialog:!1,addUserDialogloading:!1}},methods:{CanceladdUserdialog:function(){this.addUserdialog=!1;var e=this.$refs.addUserform.form;e.resetFields()},OkaddUserdialog:function(){var e=this,t=this.$refs.addUserform.form;t.validateFields((function(a,r){a||(e.addUserDialogloading=!0,(0,s.m)(r).then((function(a){0==a.status?(e.addUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.addUserDialogloading=!1})))}))}}},u=d,g=a(81656),m=(0,g.A)(u,l,c,!1,null,null,null),p=m.exports,f=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"用户详情",width:1e3,visible:e.userDialog,footer:!1},on:{cancel:function(t){e.userDialog=!1}}},[t("a-descriptions",{attrs:{bordered:"",title:e.currentDetails.realName?e.currentDetails.realName:"未认证",column:{xxl:3,xl:3,lg:3,md:3,sm:2,xs:1}}},[t("a-descriptions-item",{attrs:{label:"用户ID"}},[e._v(" "+e._s(e.currentDetails.id?e.currentDetails.id:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"手机号码"}},[e._v(" "+e._s(e.currentDetails.phone?e.currentDetails.phone:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"登录状态"}},[t("a-tag",{attrs:{color:1==e.currentDetails.positionType?"red":"green"}},[e._v(" "+e._s(1==e.currentDetails.isLogin?"不可登录":"正常")+" ")])],1),t("a-descriptions-item",{attrs:{label:"账号类型"}},[t("a-tag",{attrs:{color:1==e.currentDetails.accountType?"blue":"green"}},[e._v(" "+e._s(1==e.currentDetails.accountType?"模拟用户":"实盘用户")+" ")])],1),t("a-descriptions-item",{attrs:{label:"交易状态"}},[t("a-tag",{attrs:{color:1==e.currentDetails.isLock?"red":"green"}},[e._v(" "+e._s(1==e.currentDetails.isLock?"不可交易":"正常")+" ")])],1),t("a-descriptions-item",{attrs:{label:"所属代理"}},[e._v(" "+e._s(e.currentDetails.agentName?e.currentDetails.agentName:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"真实姓名"}},[e._v(" "+e._s(e.currentDetails.realName?e.currentDetails.realName:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"身份证号码"}},[e._v(" "+e._s(e.currentDetails.idCard?e.currentDetails.idCard:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册ip"}},[e._v(" "+e._s(e.currentDetails.regIp?e.currentDetails.regIp:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册地址"}},[e._v(" "+e._s(e.currentDetails.regAddress?e.currentDetails.regAddress:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册时间"}},[e._v(" "+e._s(e._f("moment")(e.currentDetails.regTime))+" ")]),t("a-descriptions-item",{attrs:{label:"总资金(￥)"}},[e._v(" "+e._s(Number(e.currentDetails.userAmt+e.currentDetails.userIndexAmt).toFixed(2))+" ")]),t("a-descriptions-item",{attrs:{label:"融资总资金(￥)"}},[e._v(" "+e._s(e.currentDetails.userAmt?e.currentDetails.userAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"指数总资金(￥)"}},[e._v(" "+e._s(e.currentDetails.userIndexAmt?e.currentDetails.userIndexAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"融资可用资金(￥)"}},[e._v(" "+e._s(e.currentDetails.enableAmt?e.currentDetails.enableAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"指数可用资金(￥)"}},[e._v(" "+e._s(e.currentDetails.enableIndexAmt?e.currentDetails.enableIndexAmt:"0")+" ")]),t("a-descriptions-item",{attrs:{label:"身份证正面"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img2Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img2Key)}}})]),t("a-descriptions-item",{attrs:{label:"身份证背面"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img1Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img1Key)}}})]),t("a-descriptions-item",{attrs:{label:"手持身份证"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img3Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img3Key)}}})])],1)],1),t("a-modal",{attrs:{visible:e.previewVisible,footer:null},on:{cancel:function(t){e.previewVisible=!1}}},[t("img",{staticStyle:{width:"100%"},attrs:{alt:"example",src:e.previewImage}})])],1)},h=[],v={components:{},props:{currentDetails:{type:Object}},data:function(){return{userDialog:!1,previewVisible:!1,previewImage:""}},methods:{previewImageFun:function(e){e&&(this.previewImage=e,this.previewVisible=!0)}}},b=v,w=(0,g.A)(b,f,h,!1,null,null,null),C=w.exports,y=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"修改用户信息",width:600,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"所属代理",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentId",{}],expression:"['agentId', {}]"}],attrs:{placeholder:"请选择所属代理"}},e._l(e.agentlist,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.id}},[e._v(e._s(a.agentName)+" ")])})),1)],1),t("a-form-item",{attrs:{label:"账户类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["accountType",{}],expression:"['accountType', {}]"}],attrs:{placeholder:"请选择账户类型"}},[t("a-select-option",{attrs:{value:0}},[e._v("真实客户")]),t("a-select-option",{attrs:{value:1}},[e._v("虚拟用户")])],1)],1),t("a-form-item",{attrs:{label:"登录状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["isLogin",{}],expression:"['isLogin', {}]"}],attrs:{placeholder:"请选择登录状态"}},[t("a-select-option",{attrs:{value:0}},[e._v("可登录")]),t("a-select-option",{attrs:{value:1}},[e._v("不可登录")])],1)],1),t("a-form-item",{attrs:{label:"交易状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["isLock",{}],expression:"['isLock', {}]"}],attrs:{placeholder:"请选择交易状态"}},[t("a-select-option",{attrs:{value:0}},[e._v("可交易")]),t("a-select-option",{attrs:{value:1}},[e._v("不可交易")])],1)],1),t("a-form-item",{attrs:{label:"手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{}],expression:"['phone', {}]"}],attrs:{placeholder:"请输入手机号"}})],1),t("a-form-item",{attrs:{label:"用户名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["realName",{}],expression:"['realName', {}]"}],attrs:{placeholder:"请输入用户名"}})],1),t("a-form-item",{attrs:{label:"密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["pwd",{}],expression:"['pwd', {}]"}],attrs:{placeholder:"请输入密码"}})],1),t("a-form-item",{attrs:{label:"资金密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["withPwd",{}],expression:"['withPwd', {}]"}],attrs:{placeholder:"请输入密码"}})],1),t("a-form-item",{attrs:{label:"身份证号码",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["idCard",{}],expression:"['idCard', {}]"}],attrs:{placeholder:"请输入身份证号码"}})],1),t("a-form-item",{attrs:{label:"地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["addr",{}],expression:"['addr', {}]"}],attrs:{placeholder:"请输入地址"}})],1),t("a-form-item",{attrs:{label:"身份证正面",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("div",{staticClass:"upload-container"},[t("a-upload-dragger",{staticClass:"custom-dragger",attrs:{name:"file","show-upload-list":!1,"before-upload":e.beforeUploadImg1,customRequest:e.uploadImg1}},[e.img1Loading?t("div",{staticClass:"loading-container"},[t("a-icon",{staticClass:"loading-icon",attrs:{type:"loading"}}),t("p",{staticClass:"loading-text"},[e._v("上传中...")])],1):e.img1Url?t("div",{staticClass:"preview-container"},[t("img",{staticClass:"preview-image",attrs:{src:e.img1Url,alt:"身份证正面"}}),t("div",{staticClass:"preview-mask"},[t("p",[e._v("点击重新上传")])])]):[t("p",{staticClass:"ant-upload-drag-icon"},[t("a-icon",{attrs:{type:"idcard"}})],1),t("p",{staticClass:"ant-upload-text"},[e._v("点击或拖拽上传身份证正面照片")]),t("p",{staticClass:"ant-upload-hint"},[e._v("支持JPG、PNG格式")])]],2)],1)]),t("a-form-item",{attrs:{label:"身份证反面",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("div",{staticClass:"upload-container"},[t("a-upload-dragger",{staticClass:"custom-dragger",attrs:{name:"file","show-upload-list":!1,"before-upload":e.beforeUploadImg2,customRequest:e.uploadImg2}},[e.img2Loading?t("div",{staticClass:"loading-container"},[t("a-icon",{staticClass:"loading-icon",attrs:{type:"loading"}}),t("p",{staticClass:"loading-text"},[e._v("上传中...")])],1):e.img2Url?t("div",{staticClass:"preview-container"},[t("img",{staticClass:"preview-image",attrs:{src:e.img2Url,alt:"身份证反面"}}),t("div",{staticClass:"preview-mask"},[t("p",[e._v("点击重新上传")])])]):[t("p",{staticClass:"ant-upload-drag-icon"},[t("a-icon",{attrs:{type:"idcard"}})],1),t("p",{staticClass:"ant-upload-text"},[e._v("点击或拖拽上传身份证反面照片")]),t("p",{staticClass:"ant-upload-hint"},[e._v("支持JPG、PNG格式")])]],2)],1)]),t("a-form-item",{attrs:{label:"手持身份证",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("div",{staticClass:"upload-container"},[t("a-upload-dragger",{staticClass:"custom-dragger",attrs:{name:"file","show-upload-list":!1,"before-upload":e.beforeUploadImg3,customRequest:e.uploadImg3}},[e.img3Loading?t("div",{staticClass:"loading-container"},[t("a-icon",{staticClass:"loading-icon",attrs:{type:"loading"}}),t("p",{staticClass:"loading-text"},[e._v("上传中...")])],1):e.img3Url?t("div",{staticClass:"preview-container"},[t("img",{staticClass:"preview-image",attrs:{src:e.img3Url,alt:"手持身份证"}}),t("div",{staticClass:"preview-mask"},[t("p",[e._v("点击重新上传")])])]):[t("p",{staticClass:"ant-upload-drag-icon"},[t("a-icon",{attrs:{type:"user"}})],1),t("p",{staticClass:"ant-upload-text"},[e._v("点击或拖拽上传手持身份证照片")]),t("p",{staticClass:"ant-upload-hint"},[e._v("支持JPG、PNG格式")])]],2)],1)])],1)],1)],1)},D=[],U=a(55464),x=a(56252),_=(a(62010),a(26099),a(47764),a(23500),a(62953),a(3296),a(27208),a(48408),a(91863)),k=a.n(_),I={components:{},props:{getinit:{type:Function,default:function(){}},agentlist:{type:Array,default:function(){return[]}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["agentId","phone","realName","pwd","idCard","isLogin","isLock","accountType","img1Key","img2Key","img3Key"],currentDetails:{},img1Url:"",img2Url:"",img3Url:"",img1Loading:!1,img2Loading:!1,img3Loading:!1}},mounted:function(){},methods:{getEditorder:function(e){var t=this;this.currentDetails=e,this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(k()(e,this.fields)),this.img1Url=e.img1Key||"",this.img2Url=e.img2Key||"",this.img3Url=e.img3Key||""},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields(),this.img1Url="",this.img2Url="",this.img3Url=""},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(r.id=e.currentDetails.id,r.pwd&&(r.userPwd=r.pwd),r.img1Key=e.img1Url,r.img2Key=e.img2Url,r.img3Key=e.img3Url,e.editUserDialogloading=!0,(0,s.Rr)(r).then((function(a){0===a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))},beforeUploadImg1:function(e){var t="image/jpeg"===e.type||"image/png"===e.type;if(!t)return this.$message.error("只能上传JPG或PNG格式的图片!"),!1;var a=e.size/1024/1024<2;return!!a||(this.$message.error("图片大小不能超过2MB!"),!1)},beforeUploadImg2:function(e){return this.beforeUploadImg1(e)},beforeUploadImg3:function(e){return this.beforeUploadImg1(e)},uploadImg1:function(e){var t=this;return(0,x.A)((0,U.A)().mark((function a(){var r,i,s;return(0,U.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=e.file,i=new File([r],"front_".concat(r.name),{type:r.type}),t.img1Loading=!0,a.prev=3,a.next=6,t.uploadToQiniu(i);case 6:s=a.sent,s?(t.img1Url=s,t.$message.success("上传成功")):t.$message.error("上传失败: 未获取到有效URL"),a.next=14;break;case 10:a.prev=10,a.t0=a["catch"](3),console.error("上传图片错误:",a.t0),t.$message.error("上传失败: "+(a.t0.message||"未知错误"));case 14:return a.prev=14,t.img1Loading=!1,a.finish(14);case 17:case"end":return a.stop()}}),a,null,[[3,10,14,17]])})))()},uploadImg2:function(e){var t=this;return(0,x.A)((0,U.A)().mark((function a(){var r,i,s;return(0,U.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=e.file,i=new File([r],"back_".concat(r.name),{type:r.type}),t.img2Loading=!0,a.prev=3,a.next=6,t.uploadToQiniu(i);case 6:s=a.sent,s?(t.img2Url=s,t.$message.success("上传成功")):t.$message.error("上传失败: 未获取到有效URL"),a.next=14;break;case 10:a.prev=10,a.t0=a["catch"](3),console.error("上传图片错误:",a.t0),t.$message.error("上传失败: "+(a.t0.message||"未知错误"));case 14:return a.prev=14,t.img2Loading=!1,a.finish(14);case 17:case"end":return a.stop()}}),a,null,[[3,10,14,17]])})))()},uploadImg3:function(e){var t=this;return(0,x.A)((0,U.A)().mark((function a(){var r,i,s;return(0,U.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=e.file,i=new File([r],"holding_".concat(r.name),{type:r.type}),t.img3Loading=!0,a.prev=3,a.next=6,t.uploadToQiniu(i);case 6:s=a.sent,s?(t.img3Url=s,t.$message.success("上传成功")):t.$message.error("上传失败: 未获取到有效URL"),a.next=14;break;case 10:a.prev=10,a.t0=a["catch"](3),console.error("上传图片错误:",a.t0),t.$message.error("上传失败: "+(a.t0.message||"未知错误"));case 14:return a.prev=14,t.img3Loading=!1,a.finish(14);case 17:case"end":return a.stop()}}),a,null,[[3,10,14,17]])})))()},convertToJPEG:function(e){return(0,x.A)((0,U.A)().mark((function t(){return(0,U.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("image/jpeg"!==e.type){t.next=2;break}return t.abrupt("return",e);case 2:return t.abrupt("return",new Promise((function(t,a){var r=new Image,i=URL.createObjectURL(e);r.onload=function(){var e=document.createElement("canvas"),a=e.getContext("2d");e.width=r.width,e.height=r.height,a.drawImage(r,0,0),e.toBlob((function(e){t(e),URL.revokeObjectURL(i)}),"image/jpeg",.92)},r.onerror=a,r.src=i})));case 3:case"end":return t.stop()}}),t)})))()},compressImageWithCanvas:function(e,t,a,r){return(0,x.A)((0,U.A)().mark((function i(){return(0,U.A)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.abrupt("return",new Promise((function(i,s){var n=new Image,o=URL.createObjectURL(e);n.onload=function(){var e=document.createElement("canvas"),s=e.getContext("2d"),l=n.width,c=n.height;l>c?l>t&&(c=Math.round(t/l*c),l=t):c>a&&(l=Math.round(a/c*l),c=a),e.width=l,e.height=c,s.drawImage(n,0,0,l,c),e.toBlob((function(e){i(e),URL.revokeObjectURL(o)}),"image/jpeg",r)},n.onerror=s,n.src=o})));case 1:case"end":return i.stop()}}),i)})))()},uploadToQiniu:function(e){var t=this;return(0,x.A)((0,U.A)().mark((function a(){var r,i,s,n,o,l,c;return(0,U.A)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,e){a.next=3;break}throw new Error("请选择文件");case 3:return a.next=5,t.convertToJPEG(e);case 5:return r=a.sent,a.next=8,t.compressImageWithCanvas(r,1024,1024,.7);case 8:return i=a.sent,s=new FormData,s.append("file",i),a.next=13,t.$http.post("/admin/upload/uploadToQiniu.do",s,{headers:{"Content-Type":"multipart/form-data"}});case 13:if(n=a.sent,console.log("上传响应:",n),!(n.data&&0===n.data.status&&n.data.data&&n.data.data.url)){a.next=21;break}return o=n.data.data.url,console.log("上传成功，URL:",o),a.abrupt("return",o);case 21:if(0!==n.status||!n.data||!n.data.url){a.next=27;break}return l=n.data.url,console.log("上传成功，URL:",l),a.abrupt("return",l);case 27:throw new Error((null===(c=n.data)||void 0===c?void 0:c.msg)||n.msg||"上传失败");case 28:a.next=35;break;case 30:throw a.prev=30,a.t0=a["catch"](0),console.error("上传到七牛云时出错:",a.t0),t.$message.error("上传失败: "+(a.t0.message||"未知错误")),a.t0;case 35:case"end":return a.stop()}}),a,null,[[0,30]])})))()}}},N=I,$=(0,g.A)(N,y,D,!1,null,"34c3494c",null),L=$.exports,A=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"修改银行卡信息",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"银行名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["bankName",{}],expression:"['bankName', {}]"}],attrs:{placeholder:"请输入银行名称"}})],1),t("a-form-item",{attrs:{label:"银行卡",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["bankNo",{}],expression:"['bankNo', {}]"}],attrs:{placeholder:"请输入银行卡"}})],1),t("a-form-item",{attrs:{label:"支行地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["bankAddress",{}],expression:"['bankAddress', {}]"}],attrs:{placeholder:"请输入支行地址"}})],1)],1)],1)],1)},S=[],F={components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["bankName","bankAddress","bankNo"],currentDetails:{}}},methods:{getbankinfo:function(e){var t=this;this.currentDetails=e;var a={userId:e.id};(0,s.EV)(a).then((function(e){0==e.status&&t.getEditorder(e.data)}))},getEditorder:function(e){var t=this;this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(k()(e,this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(r.id=e.currentDetails.id,e.editUserDialogloading=!0,(0,s.lu)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},P=F,q=(0,g.A)(P,A,S,!1,null,null,null),j=q.exports,R=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"账户扣入款",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"用户id",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId",{}],expression:"['userId', {}]"}],attrs:{placeholder:"请输入用户id",disabled:""}})],1),t("a-form-item",{attrs:{label:"金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["amt",{rules:[{required:!0,message:"请输入金额"}]}],expression:"['amt', {rules: [{ required: true, message: '请输入金额', }] }]"}],attrs:{placeholder:"请输入金额"}})],1),t("a-form-item",{attrs:{label:"扣入款",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["direction",{rules:[{required:!0,message:"请选择扣入款"}]}],expression:"['direction', { rules: [{ required: true, message: '请选择扣入款', }] }]"}],attrs:{placeholder:"请选择扣入款"}},[t("a-select-option",{attrs:{value:"1"}},[e._v("扣款")]),t("a-select-option",{attrs:{value:"0"}},[e._v("入款")])],1)],1),t("a-form-item",{attrs:{label:"备注",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["remark",{}],expression:"['remark', { }]"}],attrs:{placeholder:"请输入备注"}})],1)],1)],1)],1)},O=[],T={components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["amt","direction","userId","remark"],currentDetails:{}}},methods:{getEditorder:function(e){var t=this;this.currentDetails=e,this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(k()(e,this.fields)),this.editUserform.setFieldsValue(k()({userId:e.id},this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(e.editUserDialogloading=!0,(0,s.$W)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},E=T,K=(0,g.A)(E,R,O,!1,null,null,null),z=K.exports,M=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"虚拟账户扣入款",width:500,visible:e.editUserdialog,confirmLoading:e.editUserDialogloading},on:{ok:e.OkeditUserdialog,cancel:e.CanceleditUserdialog}},[t("a-form",{ref:"editUserform",attrs:{form:e.editUserform}},[t("a-form-item",{attrs:{label:"用户id",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId",{}],expression:"['userId', {}]"}],attrs:{placeholder:"请输入用户id",disabled:""}})],1),t("a-form-item",{attrs:{label:"金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["amt",{rules:[{required:!0,message:"请输入金额"}]}],expression:"['amt', {rules: [{ required: true, message: '请输入金额', }] }]"}],attrs:{placeholder:"请输入金额"}})],1),t("a-form-item",{attrs:{label:"扣入款",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["direction",{rules:[{required:!0,message:"请选择扣入款"}]}],expression:"['direction', { rules: [{ required: true, message: '请选择扣入款', }] }]"}],attrs:{placeholder:"请选择扣入款"}},[t("a-select-option",{attrs:{value:"1"}},[e._v("扣款")]),t("a-select-option",{attrs:{value:"0"}},[e._v("入款")])],1)],1)],1)],1)],1)},V=[],Y={components:{},props:{getinit:{type:Function,default:function(){}}},data:function(){return{labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},editUserform:this.$form.createForm(this),editUserdialog:!1,editUserDialogloading:!1,fields:["amt","direction","userId"],currentDetails:{}}},methods:{getEditorder:function(e){var t=this;this.currentDetails=e,this.editUserdialog=!0,this.fields.forEach((function(e){return t.editUserform.getFieldDecorator(e)})),this.editUserform.setFieldsValue(k()(e,this.fields)),this.editUserform.setFieldsValue(k()({userId:e.id},this.fields))},CanceleditUserdialog:function(){this.editUserdialog=!1;var e=this.$refs.editUserform.form;e.resetFields()},OkeditUserdialog:function(){var e=this,t=this.$refs.editUserform.form;t.validateFields((function(a,r){a||(e.editUserDialogloading=!0,(0,s.P1)(r).then((function(a){0==a.status?(e.editUserdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.editUserDialogloading=!1})))}))}}},G=Y,B=(0,g.A)(G,M,V,!1,null,null,null),H=B.exports,J=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"实名认证审核",width:1e3,visible:e.userDialog,footer:!1},on:{cancel:function(t){e.userDialog=!1}}},[t("a-descriptions",{attrs:{bordered:"",title:e.currentDetails.realName?e.currentDetails.realName:"未认证",column:{xxl:3,xl:3,lg:3,md:3,sm:2,xs:1}}},[t("a-descriptions-item",{attrs:{label:"真实姓名"}},[e._v(" "+e._s(e.currentDetails.realName?e.currentDetails.realName:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"认证状态"}},[t("a-tag",{attrs:{color:0==e.currentDetails.isActive||1==e.currentDetails.isActive?"blue":2==e.currentDetails.isActive?"green":"red"}},[e._v(" "+e._s(0==e.currentDetails.isActive?"待认证":1==e.currentDetails.isActive?"待审核":2==e.currentDetails.isActive?"认证成功":"驳回")+" ")])],1),3==e.currentDetails.isActive?t("a-descriptions-item",{attrs:{label:"驳回原因"}},[e._v(" "+e._s(e.currentDetails.authMsg?e.currentDetails.authMsg:"--")+" ")]):e._e(),t("a-descriptions-item",{attrs:{label:"身份证号码"}},[e._v(" "+e._s(e.currentDetails.idCard?e.currentDetails.idCard:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"地址"}},[e._v(" "+e._s(e.currentDetails.addr?e.currentDetails.addr:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册ip"}},[e._v(" "+e._s(e.currentDetails.regIp?e.currentDetails.regIp:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册地址"}},[e._v(" "+e._s(e.currentDetails.regAddress?e.currentDetails.regAddress:"--")+" ")]),t("a-descriptions-item",{attrs:{label:"注册时间"}},[e._v(" "+e._s(e._f("moment")(e.currentDetails.regTime))+" ")]),t("a-descriptions-item",{attrs:{label:"身份证正面"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img2Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img2Key)}}})]),t("a-descriptions-item",{attrs:{label:"身份证背面"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img1Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img1Key)}}})]),t("a-descriptions-item",{attrs:{label:"手持身份证111"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.img3Key,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.img3Key)}}})])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.currentDetails.isActive||1==e.currentDetails.isActive,expression:"currentDetails.isActive == 0 || currentDetails.isActive == 1"}],staticStyle:{"margin-top":"20px",display:"flex","justify-content":"center"}},[t("a-button",{attrs:{type:"danger"},on:{click:function(t){e.userDialog=!1,e.bohuidialog=!0}}},[e._v(" 驳回 ")]),t("a-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.gettongguo(2)}}},[e._v(" 通过 ")])],1)],1),t("a-modal",{attrs:{title:"驳回原因",width:500,visible:e.bohuidialog,confirmLoading:e.bohuidialogloading},on:{ok:e.Okbohuidialog,cancel:e.Cancelbohuidialog}},[t("a-form",{ref:"bohuiform",attrs:{form:e.bohuiform}},[t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["authMsg",{rules:[{required:!0,message:"请输入驳回原因"}]}],expression:"['authMsg', { rules: [{ required: true, message: '请输入驳回原因', }] }]"}],attrs:{placeholder:"请输入驳回原因"}})],1)],1)],1),t("a-modal",{attrs:{visible:e.previewVisible,footer:null},on:{cancel:function(t){e.previewVisible=!1}}},[t("img",{staticStyle:{width:"100%"},attrs:{alt:"example",src:e.previewImage}})])],1)},Q=[],W={components:{},props:{currentDetails:{type:Object},getinit:{type:Function,default:function(){}}},data:function(){return{userDialog:!1,bohuidialog:!1,bohuidialogloading:!1,bohuiform:this.$form.createForm(this),previewVisible:!1,previewImage:""}},methods:{previewImageFun:function(e){e&&(this.previewImage=e,this.previewVisible=!0)},Okbohuidialog:function(){var e=this,t=this.$refs.bohuiform.form;t.validateFields((function(a,r){a||(r.userId=e.currentDetails.id,r.state=3,e.bohuidialogloading=!0,(0,s.iE)(r).then((function(a){0==a.status?(e.bohuidialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.bohuidialogloading=!1})))}))},Cancelbohuidialog:function(){this.bohuidialog=!1;var e=this.$refs.bohuiform.form;e.resetFields()},gettongguo:function(e){var t=this,a={userId:this.currentDetails.id,state:e};(0,s.iE)(a).then((function(e){0==e.status?(t.userDialog=!1,t.getinit()):t.$message.error({content:e.msg}),t.userDialog=!1}))}}},X=W,Z=(0,g.A)(X,J,Q,!1,null,null,null),ee=Z.exports,te=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"合同审核",width:1e3,visible:e.userDialog,footer:!1},on:{cancel:function(t){e.userDialog=!1}}},[t("a-descriptions",{attrs:{bordered:"",title:e.currentDetails.signatureMsg?"已签合同":"未签合同",column:{xxl:3,xl:3,lg:3,md:3,sm:2,xs:1}}},[t("a-descriptions-item",{attrs:{label:"签名图片"}},[t("img",{staticStyle:{width:"140px",height:"70px"},attrs:{src:e.currentDetails.signatureMsg,alt:""},on:{click:function(t){return e.previewImageFun(e.currentDetails.signatureMsg)}}})])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:null!=e.currentDetails.signatureMsg&&e.currentDetails.signatureMsg.length>2,expression:"currentDetails.signatureMsg != null && currentDetails.signatureMsg.length > 2"}],staticStyle:{"margin-top":"20px",display:"flex","justify-content":"center"}},[t("a-button",{attrs:{type:"danger"},on:{click:function(t){return e.postRejectSignature(e.currentDetails)}}},[e._v(" 驳回 ")])],1)],1),t("a-modal",{attrs:{title:"确认驳回",width:500,visible:e.bohuidialog,confirmLoading:e.bohuidialogloading},on:{ok:e.Okbohuidialog,cancel:e.Cancelbohuidialog}}),t("a-modal",{attrs:{visible:e.previewVisible,footer:null},on:{cancel:function(t){e.previewVisible=!1}}},[t("img",{staticStyle:{width:"100%"},attrs:{alt:"example",src:e.previewImage}})])],1)},ae=[],re={components:{},props:{currentDetails:{type:Object},getinit:{type:Function,default:function(){}}},data:function(){return{userDialog:!1,bohuidialog:!1,bohuidialogloading:!1,bohuiform:this.$form.createForm(this),previewVisible:!1,previewImage:""}},methods:{previewImageFun:function(e){e&&(this.previewImage=e,this.previewVisible=!0)},postRejectSignature:function(e){var t=this;this.userDialog=!1,this.$confirm({title:"驳回开户合同",content:"确认驳回此用户的开户合同?",onOk:function(){var a={userId:e.id};(0,s.hS)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getinit()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},Okbohuidialog:function(){var e=this,t=this.$refs.bohuiform.form;t.validateFields((function(a,r){a||(r.userId=e.currentDetails.id,r.state=3,e.bohuidialogloading=!0,(0,s.iE)(r).then((function(a){0==a.status?(e.bohuidialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getinit()):e.$message.error({content:a.msg}),e.bohuidialogloading=!1})))}))},Cancelbohuidialog:function(){this.bohuidialog=!1;var e=this.$refs.bohuiform.form;e.resetFields()},gettongguo:function(e){var t=this,a={userId:this.currentDetails.id,state:e};(0,s.iE)(a).then((function(e){0==e.status?(t.userDialog=!1,t.getinit()):t.$message.error({content:e.msg}),t.userDialog=!1}))}}},ie=re,se=(0,g.A)(ie,te,ae,!1,null,null,null),ne=se.exports,oe=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"充值明细",width:1500,visible:e.show,footer:!1},on:{cancel:function(t){e.show=!1}}},[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"table-page-search-wrapper"},[t("a-form",{attrs:{layout:"inline"}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"充值状态"}},[t("a-select",{attrs:{placeholder:"请选择充值状态"},model:{value:e.queryParam.state,callback:function(t){e.$set(e.queryParam,"state",t)},expression:"queryParam.state"}},[t("a-select-option",{attrs:{value:0}},[e._v("审核中")]),t("a-select-option",{attrs:{value:1}},[e._v("入金成功")])],1)],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:8,sm:24}},[t("a-form-item",[t("span",{staticClass:"table-page-search-submitButtons"},[t("a-button",{attrs:{icon:"redo"},on:{click:e.resetParam}},[e._v("重置")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){e.queryParam.pageNum=1,e.getList()}}},[e._v("查询 ")])],1)])],1)],1)],1)],1)]),t("a-card",{attrs:{bordered:!1}},[t("a-table",{staticStyle:{width:"100%"},attrs:{bordered:"",loading:e.loading,pagination:e.pagination,columns:e.columns,"data-source":e.datalist,rowKey:"id"},scopedSlots:e._u([{key:"payChannel",fn:function(a,r){return t("span",{},[[t("div",[t("a-tag",{attrs:{color:0==r.payChannel?"blue":1==r.payChannel?"orange":"cyan"}},[e._v(" "+e._s(0==r.payChannel?"支付宝":1==r.payChannel?"对公转账":"现金转账")+" ")])],1)]],2)}},{key:"orderStatus",fn:function(a,r){return t("span",{},[[t("div",[t("a-tag",{attrs:{color:0==r.orderStatus?"blue":1==r.orderStatus?"green":2==r.orderStatus?"red":"orange"}},[e._v(" "+e._s(0==r.orderStatus?"审核中":1==r.orderStatus?"充值成功":2==r.orderStatus?"充值失败":"订单取消")+" ")])],1)]],2)}}])})],1)],1)],1)},le=[],ce=(a(28706),a(60804)),de=a(95093),ue=a.n(de),ge={name:"rechargeDialog",props:{currentDetails:{type:Object},getinit:{type:Function,default:function(){}}},data:function(){var e=this;return{show:!1,columns:[{title:"用户名称（ID）",dataIndex:"nickName",align:"center",width:180,customRender:function(e,t,a){return"".concat(t.nickName,"（").concat(t.userId,"）")}},{title:"订单ID",dataIndex:"id",align:"center"},{title:"代理用户",dataIndex:"agentName",align:"center"},{title:"订单号",dataIndex:"orderSn",align:"center"},{title:"充值渠道",dataIndex:"payChannel",align:"center",scopedSlots:{customRender:"payChannel"}},{title:"充值金额",dataIndex:"payAmt",align:"center"},{title:"申请时间",dataIndex:"addTime",align:"center",width:180,customRender:function(e,t,a){return e?ue()(e).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"支付时间",dataIndex:"payTime",align:"center",width:180,customRender:function(e,t,a){return e?ue()(e).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"状态",dataIndex:"orderStatus",align:"center",scopedSlots:{customRender:"orderStatus"}},{title:"备注",dataIndex:"remark",align:"center"},{title:"手机号",dataIndex:"phone",align:"center"},{title:"操作者",dataIndex:"operator",align:"center"}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,userId:"",state:void 0},totalResult:{},datalist:[]}},watch:{show:function(e){e&&(this.resetParam(),this.getList())}},mounted:function(){this.getList()},methods:{resetParam:function(){this.queryParam={pageNum:1,pageSize:10,userId:""},this.datalist=[]},getList:function(){var e=this,t=this;this.queryParam.userId=this.currentDetails.id,this.loading=!0,(0,ce.Wc)(this.queryParam).then((function(a){e.datalist=a.data.list,e.pagination.total=a.data.total,setTimeout((function(){t.loading=!1}),500)}))},onPageChange:function(e,t){this.queryParam.pageNum=e,this.getList()},onSizeChange:function(e,t){this.queryParam.pageNum=e,this.queryParam.pageSize=t,this.getList()}}},me=ge,pe=(0,g.A)(me,oe,le,!1,null,"36c478c6",null),fe=pe.exports,he={name:"Agentlist",components:{adduserdialog:p,detailuserdialog:C,EditUserinfodialog:L,editUserbankdialog:j,editCapitaluserdialog:z,virtualEditCapitalUserDialog:H,audituserdialog:ee,rejectdialoglog:ne,rechargeDialog:fe},data:function(){var e=this;return{queryParam:{agentId:void 0,realName:"",phone:"",pageNum:1,pageSize:10,accountType:"",status:""},labelCol:{xs:{span:24},sm:{span:7}},wrapperCol:{xs:{span:24},sm:{span:13}},columns:[{title:"用户ID",dataIndex:"id",align:"center"},{title:"所属代理（Id）",scopedSlots:{customRender:"agentName"},align:"center"},{title:"手机号",dataIndex:"phone",align:"center",customRender:function(e,t,a){return e.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2")}},{title:"真实姓名",dataIndex:"realName",align:"center"},{title:"总资金",dataIndex:"userAmt",align:"center",customRender:function(e,t,a){return null==e||isNaN(e)?"0.00":Number(e).toFixed(2)}},{title:"持仓总买入金额",dataIndex:"totalBuyPrice",align:"center",customRender:function(e,t,a){return null==e||isNaN(e)?"0.00":Number(e).toFixed(2)}},{title:"可用资金",dataIndex:"enableAmt",align:"center",customRender:function(e,t,a){return null==e||isNaN(e)?"0.00":Number(e).toFixed(2)}},{title:"当前仓位",dataIndex:"currentPositionAmt",align:"center",customRender:function(e,t,a){return null==e||isNaN(e)?"0.00":Number(e).toFixed(2)}},{title:"总持仓",dataIndex:"totalPositionAmt",align:"center",customRender:function(e,t,a){return null==e||isNaN(e)?"0.00":Number(e).toFixed(2)}},{title:"认证信息",dataIndex:"isActive",align:"center",customRender:function(e,t,a){return 0==e?"待认证":1==e?"待审核":2==e?"认证成功":3==e?"驳回":""}},{title:"交易状态",dataIndex:"isLock",align:"center",scopedSlots:{customRender:"isLock"}},{title:"登录状态",dataIndex:"isLogin",align:"center",scopedSlots:{customRender:"isLogin"}},{title:"注册时间",dataIndex:"regTime",align:"center",customRender:function(e,t,a){return ue()(e).format("YYYY-MM-DD HH:mm:ss")}},{title:"操作",key:"action",align:"center",scopedSlots:{customRender:"action"}}],dataList:[],pagination:{total:0,pageSize:10,current:1,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,agentqueryParam:{pageNum:1,pageSize:100},agentlist:[],currentDetails:{}}},created:function(){this.getuserList(),this.getagentlist()},methods:{resetParams:function(){this.queryParam={agentId:void 0,realName:"",phone:"",pageNum:1,pageSize:10,accountType:"",status:""}},getDeluser:function(e){var t=this;this.$confirm({title:"提示",content:"确认删除该用户吗？此操作不可恢复，将删除该用户所有的充值、提现与持仓记录！",onOk:function(){var a={userId:e.id};(0,s.vB)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getinit()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},postRejectSignature:function(e){var t=this;this.$confirm({title:"驳回开户合同",content:"确认驳回此用户的开户合同?",onOk:function(){var a={userId:e.id};(0,s.hS)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getinit()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},getinit:function(){this.queryParam={agentId:void 0,realName:"",phone:"",pageNum:1,pageSize:10,accountType:0,status:""},this.pagination.current=1,this.getuserList()},geteditinit:function(){this.getuserList()},getuserList:function(){var e=this,t=this;this.loading=!0,(0,s.Cv)(this.queryParam).then((function(a){e.dataList=a.data.list,e.pagination.total=a.data.total,setTimeout((function(){t.loading=!1}),500)}))},getagentlist:function(){var e=this;(0,s.vP)(this.agentqueryParam).then((function(t){e.agentlist=t.data.list}))},onPageChange:function(e,t){this.queryParam.pageNum=e,this.pagination.current=e,this.getuserList()},onSizeChange:function(e,t){this.queryParam.pageNum=e,this.pagination.current=e,this.queryParam.pageSize=t,this.getuserList()},handleTableChange:function(){},exportUsers:function(){var e=this;this.$message.info({content:"正在导出用户列表，请稍候...",duration:2}),(0,s.cD)().then((function(t){o()(t,"用户列表.xls"),e.$message.success({content:"导出成功",duration:2})})).catch((function(t){e.$message.error({content:"导出失败："+t.message})}))}}},ve=he,be=(0,g.A)(ve,r,i,!1,null,null,null),we=be.exports},91863:function(e,t,a){var r=1/0,i=9007199254740991,s="[object Arguments]",n="[object Function]",o="[object GeneratorFunction]",l="[object Symbol]",c="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,d="object"==typeof self&&self&&self.Object===Object&&self,u=c||d||Function("return this")();function g(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function m(e,t){var a=-1,r=e?e.length:0,i=Array(r);while(++a<r)i[a]=t(e[a],a,e);return i}function p(e,t){var a=-1,r=t.length,i=e.length;while(++a<r)e[i+a]=t[a];return e}var f=Object.prototype,h=f.hasOwnProperty,v=f.toString,b=u.Symbol,w=f.propertyIsEnumerable,C=b?b.isConcatSpreadable:void 0,y=Math.max;function D(e,t,a,r,i){var s=-1,n=e.length;a||(a=k),i||(i=[]);while(++s<n){var o=e[s];t>0&&a(o)?t>1?D(o,t-1,a,r,i):p(i,o):r||(i[i.length]=o)}return i}function U(e,t){return e=Object(e),x(e,t,(function(t,a){return a in e}))}function x(e,t,a){var r=-1,i=t.length,s={};while(++r<i){var n=t[r],o=e[n];a(o,n)&&(s[n]=o)}return s}function _(e,t){return t=y(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,i=y(a.length-t,0),s=Array(i);while(++r<i)s[r]=a[t+r];r=-1;var n=Array(t+1);while(++r<t)n[r]=a[r];return n[t]=s,g(e,this,n)}}function k(e){return $(e)||N(e)||!!(C&&e&&e[C])}function I(e){if("string"==typeof e||j(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}function N(e){return A(e)&&h.call(e,"callee")&&(!w.call(e,"callee")||v.call(e)==s)}var $=Array.isArray;function L(e){return null!=e&&F(e.length)&&!S(e)}function A(e){return q(e)&&L(e)}function S(e){var t=P(e)?v.call(e):"";return t==n||t==o}function F(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=i}function P(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function q(e){return!!e&&"object"==typeof e}function j(e){return"symbol"==typeof e||q(e)&&v.call(e)==l}var R=_((function(e,t){return null==e?{}:U(e,m(D(t,1),I))}));e.exports=R}}]);