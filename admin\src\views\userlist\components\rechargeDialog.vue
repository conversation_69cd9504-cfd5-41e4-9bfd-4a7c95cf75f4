<template>
    <div>
        <a-modal title="充值明细" :width="1500" :visible="show" :footer="false" @cancel="show = false">
            <a-card :bordered="false">
                <div class="table-page-search-wrapper">
                    <a-form layout="inline">
                        <a-row :gutter="48">
                            <a-col :md="12" :lg="6" :sm="24">
                                <a-form-item label="充值状态">
                                    <a-select v-model="queryParam.state" placeholder="请选择充值状态">
                                        <a-select-option :value="0">审核中</a-select-option>
                                        <a-select-option :value="1">入金成功</a-select-option>
                                        <!-- <a-select-option :value="2">入金失败</a-select-option> -->
                                        <!-- <a-select-option :value="3">入金取消</a-select-option> -->
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="48">
                            <a-col :md="12" :lg="8" :sm="24">
                                <a-form-item>
                                    <span class="table-page-search-submitButtons">
                                        <a-button @click="resetParam" icon="redo">重置</a-button>
                                        <a-button type="primary" icon="search" style="margin-left: 8px" @click="queryParam.pageNum = 1, getList()">查询
                                        </a-button>
                                    </span>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </div>
            </a-card>
            <a-card :bordered="false">
                <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist" rowKey="id" style="width: 100%;">
                    <span slot="payChannel" slot-scope="text,record">
                        <template>
                            <div>
                                <a-tag :color="record.payChannel == 0 ? 'blue' : record.payChannel == 1 ? 'orange' : 'cyan'">
                                    {{ record.payChannel == 0 ? '支付宝' : record.payChannel == 1 ? '对公转账' : '现金转账'
                                }}
                                </a-tag>
                            </div>
                        </template>
                    </span>
                    <span slot="orderStatus" slot-scope="text,record">
                        <template>
                            <div>
                                <a-tag :color="record.orderStatus == 0 ? 'blue' : record.orderStatus == 1 ? 'green' : record.orderStatus == 2 ? 'red' : 'orange'">
                                    {{ record.orderStatus == 0 ? '审核中' : record.orderStatus == 1 ? '充值成功' :
                                        record.orderStatus == 2 ? '充值失败' : '订单取消'
                                }}
                                </a-tag>
                            </div>
                        </template>
                    </span>
                </a-table>
            </a-card>
        </a-modal>
    </div>
</template>
<script>
import { rechargelist } from '@/api/capital'
import moment from 'moment'

export default {
    name: 'rechargeDialog',
    props: {
        currentDetails: {
            type: Object,
        },
        getinit: {
            type: Function,
            default: function () {},
        },
    },
    data() {
        return {
            show: false,
            columns: [
                {
                    title: '用户名称（ID）',
                    dataIndex: 'nickName',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return `${row.nickName}（${row.userId}）`
                    },
                },
                {
                    title: '订单ID',
                    dataIndex: 'id',
                    align: 'center',
                },
                {
                    title: '代理用户',
                    dataIndex: 'agentName',
                    align: 'center',
                },
                {
                    title: '订单号',
                    dataIndex: 'orderSn',
                    align: 'center',
                },
                {
                    title: '充值渠道',
                    dataIndex: 'payChannel',
                    align: 'center',
                    scopedSlots: { customRender: 'payChannel' },
                },
                {
                    title: '充值金额',
                    dataIndex: 'payAmt',
                    align: 'center',
                },
                {
                    title: '申请时间',
                    dataIndex: 'addTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    },
                },
                {
                    title: '支付时间',
                    dataIndex: 'payTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    },
                },
                {
                    title: '状态',
                    dataIndex: 'orderStatus',
                    align: 'center',
                    scopedSlots: { customRender: 'orderStatus' },
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    align: 'center',
                },
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    align: 'center',
                },
                {
                    title: '操作者',
                    dataIndex: 'operator',
                    align: 'center',
                },
            ],
            //表头
            pagination: {
                total: 0,
                pageSize: 10, //每页中显示10条数据
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'], //每页中显示的数据
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize), // 改变每页数量时更新显示
                onChange: (page, pageSize) => this.onPageChange(page, pageSize), //点击页码事件
                showTotal: (total) => `共有 ${total} 条数据`, //分页中显示总的数据
            },
            loading: false,
            queryParam: {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                state: undefined,
            },
            totalResult: {},
            datalist: [],
        }
    },
    watch: {
        show: function (val) {
            if (val) {
                this.resetParam()
                this.getList()
            }
        },
    },
    mounted() {
        this.getList()
    },
    methods: {
        resetParam() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                userId: '',
            }
            this.datalist = []
        },
        getList() {
            var that = this
            this.queryParam.userId = this.currentDetails.id
            this.loading = true
            rechargelist(this.queryParam).then((res) => {
                this.datalist = res.data.list
                this.pagination.total = res.data.total
                setTimeout(() => {
                    that.loading = false
                }, 500)
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.getList()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.getList()
        },
    },
}
</script>
<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
/deep/ .ant-card-body {
    padding: 0;
}
</style>