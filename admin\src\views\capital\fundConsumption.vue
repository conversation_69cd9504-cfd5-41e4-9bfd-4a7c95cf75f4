<template>
    <page-header-wrapper>
        <a-card :bordered="false">
            <div class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户ID">
                                <a-input v-model="queryParam.userId" style="width: 100%" placeholder="请输入用户ID" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="真实姓名">
                                <a-input v-model="queryParam.realName" style="width: 100%" placeholder="请输入真实姓名" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="手机号">
                                <a-input v-model="queryParam.phone" style="width: 100%" placeholder="请输入手机号" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="消费类型">
                                <a-select v-model="queryParam.consumptionType" placeholder="请选择消费类型">
                                    <a-select-option value="">全部</a-select-option>
                                    <a-select-option value="POSITION_BUY">购买股票</a-select-option>
                                    <a-select-option value="WITHDRAW">提现</a-select-option>
                                    <a-select-option value="OTHER">其他</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="关联订单号">
                                <a-input v-model="queryParam.relatedOrderSn" style="width: 100%"
                                    placeholder="请输入关联订单号" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="8" :sm="24">
                            <a-form-item label="时间范围">
                                <a-range-picker v-model="queryParam.timeRange" style="width: 100%"
                                    format="YYYY-MM-DD HH:mm:ss" :showTime="{
                                        defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')]
                                    }" :placeholder="['开始时间', '结束时间']" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="8" :sm="24">
                            <a-form-item>
                                <span class="table-page-search-submitButtons">
                                    <a-button @click="resetQueryParam" icon="redo">重置</a-button>
                                    <a-button type="primary" icon="search" style="margin-left: 8px"
                                        @click="queryParam.pageNum = 1, getList()">查询</a-button>
                                </span>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-card>

        <a-card :bordered="false">
            <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist"
                rowKey="id">
                <span slot="consumptionType" slot-scope="text,record">
                    <template>
                        <div v-if="record.consumptionType === 'POSITION_BUY'">购买股票</div>
                        <div v-else-if="record.consumptionType === 'WITHDRAW'">提现</div>
                        <div v-else-if="record.consumptionType === 'OTHER'">其他</div>
                        <div v-else>{{ record.consumptionType }}</div>
                    </template>
                </span>
                <span slot="amount" slot-scope="text">
                    <template>
                        <span style="color: #f5222d;">{{ text }}</span>
                    </template>
                </span>
            </a-table>
        </a-card>
    </page-header-wrapper>
</template>

<script>
import { fundConsumptionList } from '@/api/capital'
import moment from 'moment'

export default {
    name: 'FundConsumption',
    data() {
        return {
            columns: [
                {
                    title: '资金来源ID',
                    dataIndex: 'fundSourceId',
                    align: 'center',
                    width: 100
                },
                {
                    title: '用户名称（ID）',
                    dataIndex: 'realName',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return `${row.realName}（${row.userId}）`
                    }
                },
                {
                    title: '用户手机号',
                    dataIndex: 'phone',
                    align: 'center',
                    width: 120
                },
                {
                    title: '资金来源类型',
                    dataIndex: 'sourceType',
                    align: 'center',
                    customRender: (text, row, index) => {
                        const typeMap = {
                            'RECHARGE': '充值',
                            'POSITION_SALE': '卖出股票',
                            'WITHDRAW_REJECT': '提现驳回返还',
                            'WITHDRAW_CANCEL': '提现取消返还',
                            'ADMIN_OPERATION': '管理员操作',
                            'ADMIN_DEPOSIT': '人工上分',
                            'ADMIN_WITHDRAW': '人工下分'
                        }
                        return typeMap[text] || text
                    }
                },

                {
                    title: '消费类型',
                    dataIndex: 'consumptionType',
                    align: 'center',
                    scopedSlots: { customRender: 'consumptionType' }
                },
                {
                    title: '消费金额',
                    dataIndex: 'consumptionAmount',
                    align: 'center',
                    scopedSlots: { customRender: 'amount' }
                },
                {
                    title: '关联订单号',
                    dataIndex: 'relatedOrderSn',
                    align: 'center'
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    align: 'center'
                },
                {
                    title: '消费时间',
                    dataIndex: 'createTime',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                }
            ],
            pagination: {
                total: 0,
                pageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize),
                onChange: (page, pageSize) => this.onPageChange(page, pageSize),
                showTotal: (total) => `共有 ${total} 条数据`
            },
            loading: false,
            queryParam: {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                realName: '',
                phone: '',
                consumptionType: '',
                relatedOrderSn: '',
                timeRange: [null, null]
            },
            datalist: []
        }
    },
    created() {
        this.getList()
    },
    methods: {
        moment,
        resetQueryParam() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                realName: '',
                phone: '',
                consumptionType: '',
                relatedOrderSn: '',
                timeRange: [null, null]
            }
        },
        getList() {
            this.loading = true

            // 过滤空参数，但保留 0，处理时间格式
            const filteredParams = {}
            Object.keys(this.queryParam).forEach(key => {
                const value = this.queryParam[key]
                if (value !== '' && value !== undefined && value !== null) {
                    // 处理时间范围格式
                    if (key === 'timeRange' && Array.isArray(value) && value[0] && value[1]) {
                        filteredParams['startTime'] = moment(value[0]).format('YYYY-MM-DD HH:mm:ss')
                        filteredParams['endTime'] = moment(value[1]).format('YYYY-MM-DD HH:mm:ss')
                    } else if (key !== 'timeRange') {
                        filteredParams[key] = value
                    }
                }
            })

            fundConsumptionList(filteredParams).then((res) => {
                this.datalist = res.data.list || []
                this.pagination.total = res.data.total || 0
                this.loading = false
            }).catch(() => {
                this.loading = false
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.pagination.pageSize = pageSize
            this.getList()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.pagination.pageSize = pageSize
            this.getList()
        }
    }
}
</script>

<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
</style>
