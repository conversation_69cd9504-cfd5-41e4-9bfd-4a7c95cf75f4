<template>
  <div :class="wrpCls">

    <span style='font-size: 18px;color: red'>{{this.orderNmber}}笔提款未处理</span>
    <avatar-dropdown :menu="showMenu" :current-user="currentUser" :class="prefixCls" />

    <audio ref="notifyAudio" style="display: none;">
      <source :src="require('@/assets/tksq.mp3')" type="audio/mpeg">
    </audio>
    <!-- <select-lang :class="prefixCls" /> -->
  </div>
</template>

<script>
import AvatarDropdown from './AvatarDropdown'
import SelectLang from '@/components/SelectLang'
import { adminlist } from '@/api/managesettings'
import notifyMp3 from '@/assets/tksq.mp3'
import { getOrderNum, getPendingOrderNum, withdrawupdateState } from '@/api/capital'
export default {
  name: 'RightContent',
  components: {
    AvatarDropdown,
    SelectLang
  },
  props: {
    prefixCls: {
      type: String,
      default: 'ant-pro-global-header-index-action'
    },
    isMobile: {
      type: Boolean,
      default: () => false
    },
    topMenu: {
      type: Boolean,
      required: true
    },
    theme: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      showMenu: true,
      currentUser: {},
      notifyAudio: null,
      notifyMp3,
      tag: '',
      intervalId: null,
      orderNmber:'0'
    }
  },
  computed: {
    wrpCls() {
      return {
        'ant-pro-global-header-index-right': true,
        [`ant-pro-global-header-index-${(this.isMobile || !this.topMenu) ? 'light' : this.theme}`]: true
      }
    }
  },

  mounted() {
    this.getnowuser()
    this.startPolling();
    let self = this, notifyAudio = this.$refs["notifyAudio"];
    document.addEventListener("click", audio);

    function audio() {
      self.userClick = true;
      notifyAudio && (notifyAudio.muted = false);
      document.removeEventListener("click", audio);
    }

    this.$watch("messageNumber", (newVal, oldVal) => {
      if (newVal - 0 > oldVal - 0) {//有新消息
        this.userClick && notifyAudio && notifyAudio.play();
      }
    })
  },
  beforeDestroy() {
    this.stopPolling();
  },
  created () {

    this.notifyAudio = new Audio(this.notifyMp3)
    this.getOrderNumber()
  },
  methods: {

    async fetchTag () {
      getPendingOrderNum({}).then(res => {
         console.log(res)
        if (res.data > 0) {
          this.userClick && this.notifyAudio && this.notifyAudio.play()
        } else {

        }
      }).catch(e => {

      })

      this.getOrderNumber()
    },
    startPolling () {
      // 开始轮询，每隔 5 秒执行一次 fetchTag
      this.intervalId = setInterval(this.fetchTag, 12000)
    },
    stopPolling () {
      // 清除定时器，停止轮询
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }
    },


    async getOrderNumber () {

      getOrderNum({}).then(res => {

        this.orderNmber = res.data;

      }).catch(e => {

        this.orderNmber = 0;
      })

    },


    getnowuser() {
      adminlist().then(res => {
        var index = res.data.list.findIndex(item => item.adminPhone == window.localStorage.getItem('phones'))
        setTimeout(() => {
          this.currentUser = {
            name: res.data.list[index].adminName
          }
        }, 1500)
      })
    },
  }
}
</script>
