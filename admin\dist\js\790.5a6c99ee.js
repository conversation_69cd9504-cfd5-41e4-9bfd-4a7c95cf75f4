(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[790],{5419:function(e){e.exports=function(e,t,a,r){var n="undefined"!==typeof r?[r,e]:[e],i=new Blob(n,{type:a||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(i,t);else{var o=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(i):window.webkitURL.createObjectURL(i),s=document.createElement("a");s.style.display="none",s.href=o,s.setAttribute("download",t),"undefined"===typeof s.download&&s.setAttribute("target","_blank"),document.body.appendChild(s),s.click(),setTimeout((function(){document.body.removeChild(s),window.URL.revokeObjectURL(o)}),200)}}},60790:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return y}});var r=function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("a-card",{attrs:{bordered:!1}},[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"table-page-search-wrapper"},[t("a-form",{attrs:{layout:"inline"}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户筛选"}},[t("a-select",{attrs:{placeholder:"请选择用户类型","default-value":{key:"0"}},on:{change:e.accountTypeChangeEvent},model:{value:e.queryParam.accountType,callback:function(t){e.$set(e.queryParam,"accountType",t)},expression:"queryParam.accountType"}},[t("a-select-option",{attrs:{value:0}},[e._v("真实用户")]),t("a-select-option",{attrs:{value:1}},[e._v("模拟用户")])],1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"充值状态"}},[t("a-select",{attrs:{placeholder:"请选择充值状态"},model:{value:e.queryParam.state,callback:function(t){e.$set(e.queryParam,"state",t)},expression:"queryParam.state"}},[t("a-select-option",{attrs:{value:0}},[e._v("审核中")]),t("a-select-option",{attrs:{value:1}},[e._v("入金成功")]),t("a-select-option",{attrs:{value:2}},[e._v("入金失败")]),t("a-select-option",{attrs:{value:3}},[e._v("入金取消")])],1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"下级代理"}},[t("a-select",{attrs:{placeholder:"请选择下级代理",loading:e.agentloading},on:{focus:e.getagentlist,change:e.agentIdChangeEvent},model:{value:e.queryParam.agentId,callback:function(t){e.$set(e.queryParam,"agentId",t)},expression:"queryParam.agentId"}},e._l(e.agentlist,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.id}},[e._v(" "+e._s(a.agentName)+" ")])})),1)],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户Id"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"真实姓名"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入真实姓名"},model:{value:e.queryParam.realName,callback:function(t){e.$set(e.queryParam,"realName",t)},expression:"queryParam.realName"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"手机号"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入手机号"},model:{value:e.queryParam.phone,callback:function(t){e.$set(e.queryParam,"phone",t)},expression:"queryParam.phone"}})],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:8,sm:24}},[t("a-form-item",{attrs:{label:"支付时间"}},[t("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD HH:mm:ss","show-time":{format:"HH:mm:ss"},ranges:e.dateRanges},on:{change:e.onChangeRangeDate},model:{value:e.times,callback:function(t){e.times=t},expression:"times"}})],1)],1),t("a-col",{attrs:{md:12,lg:8,sm:24}},[t("a-form-item",[t("span",{staticClass:"table-page-search-submitButtons"},[t("a-button",{attrs:{icon:"redo"},on:{click:e.getqueryParam}},[e._v(" 重置")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){e.queryParam.pageNum=1,e.getlist()}}},[e._v("查询 ")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.queryToday()}}},[e._v("查询今日")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){return e.queryYesterday()}}},[e._v("查询昨日")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"plus"},on:{click:function(t){e.addOrderdialog=!0}}},[e._v("创建充值订单 ")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"vertical-align-bottom"},on:{click:e.getexport}},[e._v("导出搜索数据 ")])],1)])],1)],1)],1)],1)]),t("div",[e._v(e._s(1==e.queryParam.accountType?"模拟用户":"真实用户")+" - 今日充值："+e._s(e.totalResult.todayRechargeAmount)+"，"+e._s(1==e.queryParam.accountType?"模拟用户":"真实用户")+" - 总充值："+e._s(e.totalResult.totalRechargeAmount)+"，"+e._s(1==e.queryParam.accountType?"模拟用户":"真实用户")+" - 今日首充人数："+e._s(e.totalResult.todayFirstRechargeNums)+"，"+e._s(1==e.queryParam.accountType?"模拟用户":"真实用户")+" - 今日首充金额："+e._s(e.totalResult.todayFirstRechargeAmount)+" ")]),t("a-table",{staticStyle:{"margin-top":"10px"},attrs:{bordered:"",loading:e.loading,pagination:e.pagination,columns:e.columns,"data-source":e.datalist,rowKey:"id"},scopedSlots:e._u([{key:"payChannel",fn:function(a,r){return t("span",{},[[t("div",[t("a-tag",{attrs:{color:0==r.payChannel?"blue":1==r.payChannel?"orange":"cyan"}},[e._v(" "+e._s(0==r.payChannel?"支付宝":1==r.payChannel?"对公转账":"现金转账")+" ")])],1)]],2)}},{key:"orderStatus",fn:function(a,r){return t("span",{},[[t("div",[t("a-tag",{attrs:{color:0==r.orderStatus?"blue":1==r.orderStatus?"green":2==r.orderStatus?"red":"orange"}},[e._v(" "+e._s(0==r.orderStatus?"审核中":1==r.orderStatus?"充值成功":2==r.orderStatus?"充值失败":"订单取消")+" ")])],1)]],2)}},{key:"action",fn:function(a,r){return[0==r.orderStatus?t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.getEditorder(r.id)}},slot:"action"},[e._v("修改状态")]):e._e(),0==r.orderStatus?t("a-divider",{attrs:{type:"vertical"}}):e._e(),t("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(t){return e.getDelorder(r.id)}},slot:"action"},[e._v("删除订单")])]}}])})],1),t("a-modal",{attrs:{title:"修改订单状态",width:500,visible:e.editOrderdialog,confirmLoading:e.editOrderDialogloading},on:{ok:e.OkeditOrderdialog,cancel:e.CanceleditOrderdialog}},[t("a-form",{ref:"editOrderform",attrs:{form:e.editOrderform}},[t("a-form-item",{attrs:{label:"订单ID"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["chargeId",{rules:[{type:"number",required:!0,message:"请输入锁仓原因！"}]}],expression:"['chargeId', { rules: [{ type: 'number', required: true, message: '请输入锁仓原因！', }] }]"}],attrs:{disabled:""}})],1),t("a-form-item",{attrs:{label:"充值状态"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["state",{rules:[{required:!0,message:"请选择充值状态"}]}],expression:"['state', { rules: [{ required: true, message: '请选择充值状态', }] }]"}],attrs:{placeholder:"请选择充值状态"}},[t("a-select-option",{attrs:{value:"1"}},[e._v("充值成功")]),t("a-select-option",{attrs:{value:"2"}},[e._v("充值失败")])],1)],1)],1)],1),t("a-modal",{attrs:{title:"创建充值订单",width:500,visible:e.addOrderdialog,confirmLoading:e.addOrderDialogloading},on:{ok:e.OkaddOrderdialog,cancel:e.CanceladdOrderdialog}},[t("a-form",{ref:"addOrderform",attrs:{form:e.addOrderform}},[t("a-form-item",{attrs:{label:"用户ID"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId",{rules:[{required:!0,message:"请输入用户ID！"}]}],expression:"['userId', { rules: [{ required: true, message: '请输入用户ID！', }] }]"}],attrs:{placeholder:"请输入用户ID"}})],1),t("a-form-item",{attrs:{label:"充值金额"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["amt",{rules:[{required:!0,message:"请输入充值金额！"}]}],expression:"['amt', { rules: [{ required: true, message: '请输入充值金额！', }] }]"}],attrs:{placeholder:"请输入充值金额"}})],1),t("a-form-item",{attrs:{label:"充值状态"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["state",{rules:[{required:!0,message:"请选择充值状态"}]}],expression:"['state', { rules: [{ required: true, message: '请选择充值状态', }] }]"}],attrs:{placeholder:"请选择充值状态"}},[t("a-select-option",{attrs:{value:"0"}},[e._v("充值中")]),t("a-select-option",{attrs:{value:"1"}},[e._v("充值成功")]),t("a-select-option",{attrs:{value:"2"}},[e._v("充值失败")])],1)],1),t("a-form-item",{attrs:{label:"充值渠道"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["payChannel",{rules:[{required:!0,message:"请选择充值渠道"}]}],expression:"['payChannel', { rules: [{ required: true, message: '请选择充值渠道', }] }]"}],attrs:{placeholder:"请选择充值渠道"}},[t("a-select-option",{attrs:{value:"0"}},[e._v("支付宝")]),t("a-select-option",{attrs:{value:"1"}},[e._v("对公转账")]),t("a-select-option",{attrs:{value:"2"}},[e._v("现金转账")])],1)],1)],1)],1)],1)},n=[],i=(a(28706),a(26099),a(23500),a(60804)),o=a(80157),s=a(91863),l=a.n(s),d=a(5419),c=a.n(d),u=a(95093),m=a.n(u),g=(a(5947),{name:"Rechargelist",data:function(){var e=this;return{dateRanges:{"今天":[m()().startOf("day"),m()().endOf("day")],"昨天":[m()().subtract(1,"days").startOf("day"),m()().subtract(1,"days").endOf("day")],"本周":[m()().startOf("week"),m()().endOf("week")],"本月":[m()().startOf("month"),m()().endOf("month")]},columns:[{title:"用户名称（ID）",dataIndex:"nickName",align:"center",width:180,customRender:function(e,t,a){return"".concat(t.nickName,"（").concat(t.userId,"）")}},{title:"订单ID",dataIndex:"id",align:"center"},{title:"代理用户",dataIndex:"agentName",align:"center"},{title:"订单号",dataIndex:"orderSn",align:"center"},{title:"充值渠道",dataIndex:"payChannel",align:"center",scopedSlots:{customRender:"payChannel"}},{title:"充值金额",dataIndex:"payAmt",align:"center"},{title:"申请时间",dataIndex:"addTime",align:"center",width:180,customRender:function(e,t,a){return e?m()(e).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"支付时间",dataIndex:"payTime",align:"center",width:180,customRender:function(e,t,a){return e?m()(e).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"状态",dataIndex:"orderStatus",align:"center",scopedSlots:{customRender:"orderStatus"}},{title:"备注",dataIndex:"remark",align:"center"},{title:"手机号",dataIndex:"phone",align:"center",customRender:function(e,t,a){if(!e)return"";var r=String(e);return r.substr(0,3)+"****"+r.substr(7)}},{title:"操作者",dataIndex:"operator",align:"center"},{title:"操作",key:"action",align:"center",width:180,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,queryParam:{accountType:void 0,pageNum:1,pageSize:10,agentId:void 0,state:void 0,userId:"",realName:"",beginTime:"",endTime:"",phone:""},datalist:[],agentlist:[],agentloading:!1,times:[],editOrderform:this.$form.createForm(this),editOrderdialog:!1,editOrderDialogloading:!1,fields:["chargeId","state"],addOrderform:this.$form.createForm(this),addOrderdialog:!1,addOrderDialogloading:!1,agentqueryParam:{pageNum:1,pageSize:100},totalResult:{}}},created:function(){this.getlist(),this.rechargeCountRechargeAmountEvent()},methods:{queryToday:function(){this.times=[m()().startOf("day"),m()().endOf("day")],this.queryParam.beginTime=m()(this.times[0]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.endTime=m()(this.times[1]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.pageNum=1,this.getlist()},queryYesterday:function(){var e=m()().subtract(1,"days").format("YYYY-MM-DD");this.times=[m()(e+" 00:00:00","YYYY-MM-DD HH:mm:ss"),m()(e+" 23:59:59","YYYY-MM-DD HH:mm:ss")],this.queryParam.beginTime=m()(this.times[0]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.endTime=m()(this.times[1]).format("YYYY-MM-DD HH:mm:ss"),this.queryParam.pageNum=1,this.getlist()},rechargeCountRechargeAmountEvent:function(){var e=this;(0,i.M_)({accountType:this.queryParam.accountType,agentId:this.queryParam.agentId}).then((function(t){e.totalResult=t.data}))},accountTypeChangeEvent:function(){this.rechargeCountRechargeAmountEvent()},agentIdChangeEvent:function(e){this.rechargeCountRechargeAmountEvent()},getexport:function(){(0,i.oq)(this.queryParam).then((function(e){c()(e,"充值列表.xls")}))},CanceladdOrderdialog:function(){this.addOrderdialog=!1;var e=this.$refs.addOrderform.form;e.resetFields()},OkaddOrderdialog:function(){var e=this,t=this.$refs.addOrderform.form;t.validateFields((function(a,r){a||(e.addOrderDialogloading=!0,(0,i.I1)(r).then((function(a){0==a.status?(e.addOrderdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getlist()):e.$message.error({content:a.msg}),e.addOrderDialogloading=!1})).catch((function(e){reject(e)})))}))},getEditorder:function(e){var t=this;this.editOrderdialog=!0,this.fields.forEach((function(e){return t.editOrderform.getFieldDecorator(e)})),this.editOrderform.setFieldsValue(l()({chargeId:e},this.fields))},OkeditOrderdialog:function(){var e=this,t=this.$refs.editOrderform.form;t.validateFields((function(a,r){a||(e.editOrderDialogloading=!0,(0,i.ST)(r).then((function(a){0==a.status?(e.editOrderdialog=!1,e.$message.success({content:a.msg,duration:2}),t.resetFields(),e.getlist()):e.$message.error({content:a.msg}),e.editOrderDialogloading=!1})).catch((function(e){reject(e)})))}))},CanceleditOrderdialog:function(){this.editOrderdialog=!1;var e=this.$refs.editOrderform.form;e.resetFields()},getDelorder:function(e){var t=this;this.$confirm({title:"提示",content:"确认删除充值订单吗？此操作不可恢复",onOk:function(){var a={cId:e};(0,i.LT)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getlist()):t.$message.error({content:e.msg})}))},onCancel:function(){console.log("Cancel")}})},onChangeRangeDate:function(e,t){if(e&&2===e.length&&e[0]&&e[1]){var a=e[0].clone().startOf("day"),r=e[1].clone().endOf("day");this.times=[a,r],this.queryParam.beginTime=a.format("YYYY-MM-DD HH:mm:ss"),this.queryParam.endTime=r.format("YYYY-MM-DD HH:mm:ss"),console.log("设置的时间范围：",this.queryParam.beginTime,this.queryParam.endTime)}else this.queryParam.beginTime="",this.queryParam.endTime="",this.times=[]},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,agentId:void 0,state:void 0,userId:"",realName:"",beginTime:"",endTime:"",phone:""},this.times=[]},getagentlist:function(){var e=this,t=this;this.agentloading=!0,(0,o.vP)(this.agentqueryParam).then((function(a){e.agentlist=a.data.list,setTimeout((function(){t.agentloading=!1}),500)}))},getlist:function(){var e=this,t=this;this.loading=!0,(0,i.Wc)(this.queryParam).then((function(a){e.datalist=a.data.list,e.pagination.total=a.data.total,setTimeout((function(){t.loading=!1}),500)}))},onPageChange:function(e,t){this.queryParam.pageNum=e,this.getlist()},onSizeChange:function(e,t){this.queryParam.pageNum=e,this.queryParam.pageSize=t,this.getlist()}}}),f=g,h=a(81656),p=(0,h.A)(f,r,n,!1,null,"4e0942e4",null),y=p.exports},91863:function(e,t,a){var r=1/0,n=9007199254740991,i="[object Arguments]",o="[object Function]",s="[object GeneratorFunction]",l="[object Symbol]",d="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,c="object"==typeof self&&self&&self.Object===Object&&self,u=d||c||Function("return this")();function m(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function g(e,t){var a=-1,r=e?e.length:0,n=Array(r);while(++a<r)n[a]=t(e[a],a,e);return n}function f(e,t){var a=-1,r=t.length,n=e.length;while(++a<r)e[n+a]=t[a];return e}var h=Object.prototype,p=h.hasOwnProperty,y=h.toString,v=u.Symbol,b=h.propertyIsEnumerable,O=v?v.isConcatSpreadable:void 0,q=Math.max;function w(e,t,a,r,n){var i=-1,o=e.length;a||(a=I),n||(n=[]);while(++i<o){var s=e[i];t>0&&a(s)?t>1?w(s,t-1,a,r,n):f(n,s):r||(n[n.length]=s)}return n}function P(e,t){return e=Object(e),Y(e,t,(function(t,a){return a in e}))}function Y(e,t,a){var r=-1,n=t.length,i={};while(++r<n){var o=t[r],s=e[o];a(s,o)&&(i[o]=s)}return i}function _(e,t){return t=q(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,n=q(a.length-t,0),i=Array(n);while(++r<n)i[r]=a[t+r];r=-1;var o=Array(t+1);while(++r<t)o[r]=a[r];return o[t]=i,m(e,this,o)}}function I(e){return k(e)||D(e)||!!(O&&e&&e[O])}function S(e){if("string"==typeof e||N(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}function D(e){return x(e)&&p.call(e,"callee")&&(!b.call(e,"callee")||y.call(e)==i)}var k=Array.isArray;function C(e){return null!=e&&R(e.length)&&!T(e)}function x(e){return H(e)&&C(e)}function T(e){var t=M(e)?y.call(e):"";return t==o||t==s}function R(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=n}function M(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function H(e){return!!e&&"object"==typeof e}function N(e){return"symbol"==typeof e||H(e)&&y.call(e)==l}var j=_((function(e,t){return null==e?{}:P(e,g(w(t,1),S))}));e.exports=j}}]);