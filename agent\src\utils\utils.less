.textOverflow() {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
}

.textOverflowMulti(@line: 3, @bg: #fff) {
  position: relative;
  max-height: @line * 1.5em;
  padding-right: 1em;
  margin-right: -1em;
  overflow: hidden;
  line-height: 1.5em;
  text-align: justify;

  &::before {
    position: absolute;
    right: 14px;
    bottom: 0;
    padding: 0 1px;
    background: @bg;
    content: '...';
  }

  &::after {
    position: absolute;
    right: 14px;
    width: 1em;
    height: 1em;
    margin-top: .2em;
    background: white;
    content: '';
  }
}

// mixins for clearfix
// ------------------------
.clearfix() {
  zoom: 1;

  &::before,
  &::after {
    display: table;
    content: ' ';
  }

  &::after {
    height: 0;
    clear: both;
    font-size: 0;
    visibility: hidden;
  }
}
