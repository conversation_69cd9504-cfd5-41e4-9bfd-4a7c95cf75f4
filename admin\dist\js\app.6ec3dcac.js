(function(){var e={505:function(e,t,n){"use strict";n.d(t,{N:function(){return u},Vp:function(){return d},iD:function(){return r},mN:function(){return c},ri:function(){return l}});var a=n(75769),i=n(55373),s=n.n(i),o={Login:"/api/admin/login.do",Logout:"/api/admin/logout.do",ForgePassword:"/auth/forge-password",Register:"/auth/register",twoStepCode:"/auth/2step-code",SendSms:"/account/sms",SendSmsErr:"/account/sms_err",UserInfo:"/api/user/info",UserMenu:"/user/nav"};function r(e){return(0,a.Ay)({url:o.Login,method:"post",data:s().stringify(e)})}function c(e){return(0,a.Ay)({url:o.SendSms,method:"post",data:e})}function d(){return(0,a.Ay)({url:o.UserInfo,method:"get",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(){return(0,a.Ay)({url:o.Logout,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(e){return(0,a.Ay)({url:o.twoStepCode,method:"post",data:e})}},5126:function(e,t,n){"use strict";n.r(t);var a=n(9661),i=n.n(a),s=n(65728),o=function(){return(0,s.cL)([{value:9,name:"AntV"},{value:8,name:"F2"},{value:8,name:"G2"},{value:8,name:"G6"},{value:8,name:"DataSet"},{value:8,name:"墨者学院"},{value:6,name:"Analysis"},{value:6,name:"Data Mining"},{value:6,name:"Data Vis"},{value:6,name:"Design"},{value:6,name:"Grammar"},{value:6,name:"Graphics"},{value:6,name:"Graph"},{value:6,name:"Hierarchy"},{value:6,name:"Labeling"},{value:6,name:"Layout"},{value:6,name:"Quantitative"},{value:6,name:"Relation"},{value:6,name:"Statistics"},{value:6,name:"可视化"},{value:6,name:"数据"},{value:6,name:"数据可视化"},{value:4,name:"Arc Diagram"},{value:4,name:"Bar Chart"},{value:4,name:"Canvas"},{value:4,name:"Chart"},{value:4,name:"DAG"},{value:4,name:"DG"},{value:4,name:"Facet"},{value:4,name:"Geo"},{value:4,name:"Line"},{value:4,name:"MindMap"},{value:4,name:"Pie"},{value:4,name:"Pizza Chart"},{value:4,name:"Punch Card"},{value:4,name:"SVG"},{value:4,name:"Sunburst"},{value:4,name:"Tree"},{value:4,name:"UML"},{value:3,name:"Chart"},{value:3,name:"View"},{value:3,name:"Geom"},{value:3,name:"Shape"},{value:3,name:"Scale"},{value:3,name:"Animate"},{value:3,name:"Global"},{value:3,name:"Slider"},{value:3,name:"Connector"},{value:3,name:"Transform"},{value:3,name:"Util"},{value:3,name:"DomUtil"},{value:3,name:"MatrixUtil"},{value:3,name:"PathUtil"},{value:3,name:"G"},{value:3,name:"2D"},{value:3,name:"3D"},{value:3,name:"Line"},{value:3,name:"Area"},{value:3,name:"Interval"},{value:3,name:"Schema"},{value:3,name:"Edge"},{value:3,name:"Polygon"},{value:3,name:"Heatmap"},{value:3,name:"Render"},{value:3,name:"Tooltip"},{value:3,name:"Axis"},{value:3,name:"Guide"},{value:3,name:"Coord"},{value:3,name:"Legend"},{value:3,name:"Path"},{value:3,name:"Helix"},{value:3,name:"Theta"},{value:3,name:"Rect"},{value:3,name:"Polar"},{value:3,name:"Dsv"},{value:3,name:"Csv"},{value:3,name:"Tsv"},{value:3,name:"GeoJSON"},{value:3,name:"TopoJSON"},{value:3,name:"Filter"},{value:3,name:"Map"},{value:3,name:"Pick"},{value:3,name:"Rename"},{value:3,name:"Filter"},{value:3,name:"Map"},{value:3,name:"Pick"},{value:3,name:"Rename"},{value:3,name:"Reverse"},{value:3,name:"sort"},{value:3,name:"Subset"},{value:3,name:"Partition"},{value:3,name:"Imputation"},{value:3,name:"Fold"},{value:3,name:"Aggregate"},{value:3,name:"Proportion"},{value:3,name:"Histogram"},{value:3,name:"Quantile"},{value:3,name:"Treemap"},{value:3,name:"Hexagon"},{value:3,name:"Binning"},{value:3,name:"kernel"},{value:3,name:"Regression"},{value:3,name:"Density"},{value:3,name:"Sankey"},{value:3,name:"Voronoi"},{value:3,name:"Projection"},{value:3,name:"Centroid"},{value:3,name:"H5"},{value:3,name:"Mobile"},{value:3,name:"K线图"},{value:3,name:"关系图"},{value:3,name:"烛形图"},{value:3,name:"股票图"},{value:3,name:"直方图"},{value:3,name:"金字塔图"},{value:3,name:"分面"},{value:3,name:"南丁格尔玫瑰图"},{value:3,name:"饼图"},{value:3,name:"线图"},{value:3,name:"点图"},{value:3,name:"散点图"},{value:3,name:"子弹图"},{value:3,name:"柱状图"},{value:3,name:"仪表盘"},{value:3,name:"气泡图"},{value:3,name:"漏斗图"},{value:3,name:"热力图"},{value:3,name:"玉玦图"},{value:3,name:"直方图"},{value:3,name:"矩形树图"},{value:3,name:"箱形图"},{value:3,name:"色块图"},{value:3,name:"螺旋图"},{value:3,name:"词云"},{value:3,name:"词云图"},{value:3,name:"雷达图"},{value:3,name:"面积图"},{value:3,name:"马赛克图"},{value:3,name:"盒须图"},{value:3,name:"坐标轴"},{value:3,name:""},{value:3,name:"Jacques Bertin"},{value:3,name:"Leland Wilkinson"},{value:3,name:"William Playfair"},{value:3,name:"关联"},{value:3,name:"分布"},{value:3,name:"区间"},{value:3,name:"占比"},{value:3,name:"地图"},{value:3,name:"时间"},{value:3,name:"比较"},{value:3,name:"流程"},{value:3,name:"趋势"},{value:2,name:"亦叶"},{value:2,name:"再飞"},{value:2,name:"完白"},{value:2,name:"巴思"},{value:2,name:"张初尘"},{value:2,name:"御术"},{value:2,name:"有田"},{value:2,name:"沉鱼"},{value:2,name:"玉伯"},{value:2,name:"画康"},{value:2,name:"祯逸"},{value:2,name:"绝云"},{value:2,name:"罗宪"},{value:2,name:"萧庆"},{value:2,name:"董珊珊"},{value:2,name:"陆沉"},{value:2,name:"顾倾"},{value:2,name:"Domo"},{value:2,name:"GPL"},{value:2,name:"PAI"},{value:2,name:"SPSS"},{value:2,name:"SYSTAT"},{value:2,name:"Tableau"},{value:2,name:"D3"},{value:2,name:"Vega"},{value:2,name:"统计图表"}])};i().mock(/\/data\/antv\/tag-cloud/,"get",o)},5839:function(e,t,n){var a={"./en-US":[45958],"./en-US.js":[45958],"./en-US/account":[21970,1980],"./en-US/account.js":[21970,1980],"./en-US/account/settings":[36748,2098],"./en-US/account/settings.js":[36748,2098],"./en-US/dashboard":[12089,6345],"./en-US/dashboard.js":[12089,6345],"./en-US/dashboard/analysis":[77062,8376],"./en-US/dashboard/analysis.js":[77062,8376],"./en-US/form":[98031,7533],"./en-US/form.js":[98031,7533],"./en-US/form/basicForm":[14932,8438],"./en-US/form/basicForm.js":[14932,8438],"./en-US/global":[12610,2418],"./en-US/global.js":[12610,2418],"./en-US/menu":[53050,7254],"./en-US/menu.js":[53050,7254],"./en-US/result":[29780,5924],"./en-US/result.js":[29780,5924],"./en-US/result/fail":[78915,77],"./en-US/result/fail.js":[78915,77],"./en-US/result/success":[10088,5802],"./en-US/result/success.js":[10088,5802],"./en-US/setting":[18749,4729],"./en-US/setting.js":[18749,4729],"./en-US/user":[28666,4606],"./en-US/user.js":[28666,4606],"./zh-CN":[48188,7644],"./zh-CN.js":[48188,7644],"./zh-CN/account":[77844],"./zh-CN/account.js":[77844],"./zh-CN/account/settings":[54142],"./zh-CN/account/settings.js":[54142],"./zh-CN/dashboard":[91771],"./zh-CN/dashboard.js":[91771],"./zh-CN/dashboard/analysis":[22492],"./zh-CN/dashboard/analysis.js":[22492],"./zh-CN/form":[89065],"./zh-CN/form.js":[89065],"./zh-CN/form/basicForm":[97178],"./zh-CN/form/basicForm.js":[97178],"./zh-CN/global":[39668],"./zh-CN/global.js":[39668],"./zh-CN/menu":[14868],"./zh-CN/menu.js":[14868],"./zh-CN/result":[11250],"./zh-CN/result.js":[11250],"./zh-CN/result/fail":[27669],"./zh-CN/result/fail.js":[27669],"./zh-CN/result/success":[64158],"./zh-CN/result/success.js":[64158],"./zh-CN/setting":[91363],"./zh-CN/setting.js":[91363],"./zh-CN/user":[11016],"./zh-CN/user.js":[11016]};function i(e){if(!n.o(a,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=a[e],i=t[0];return Promise.all(t.slice(1).map(n.e)).then((function(){return n(i)}))}i.keys=function(){return Object.keys(a)},i.id=5839,e.exports=i},7756:function(e,t,n){"use strict";n.d(t,{T7:function(){return l},VT:function(){return u},Xk:function(){return d},YB:function(){return r},_y:function(){return m},l7:function(){return f},so:function(){return c}});var a=n(75769),i=n(55373),s=n.n(i),o={adminlist:"/admin/list.do",adminupdateLock:"/admin/updateLock.do",adminadd:"/admin/add.do",adminupdate:"/admin/update.do",adminGetGoogle:"/admin/getGoogle.do",adminBindGoogle:"/admin/bindGoogle.do",adminUnbindGoogle:"/admin/unbindGoogle.do"};function r(e){return(0,a.Ay)({url:o.adminlist,method:"post",data:s().stringify(e)})}function c(e){return(0,a.Ay)({url:o.adminupdateLock,method:"post",data:s().stringify(e)})}function d(e){return(0,a.Ay)({url:o.adminadd,method:"post",data:s().stringify(e)})}function l(e){return(0,a.Ay)({url:o.adminupdate,method:"post",data:s().stringify(e)})}function u(e){return(0,a.Ay)({url:o.adminGetGoogle,method:"post",data:s().stringify(e)})}function m(e){return(0,a.Ay)({url:o.adminBindGoogle,method:"post",data:s().stringify(e)})}function f(e){return(0,a.Ay)({url:o.adminUnbindGoogle,method:"post",data:s().stringify(e)})}},8815:function(e,t,n){"use strict";n.d(t,{A:function(){return p}});var a,i,s=n(85471),o=new s.Ay,r=n(76338),c=(n(2008),n(50113),n(74423),n(62062),n(62010),n(26099),n(21699),n(23500),{name:"MultiTab",data:function(){return{fullPathList:[],pages:[],activeKey:"",newTabIndex:0}},created:function(){var e=this;o.$on("open",(function(t){if(!t)throw new Error("multi-tab: open tab ".concat(t," err"));e.activeKey=t})).$on("close",(function(t){t?e.closeThat(t):e.closeThat(e.activeKey)})).$on("rename",(function(t){var n=t.key,a=t.name;console.log("rename",n,a);try{var i=e.pages.find((function(e){return e.path===n}));i.meta.customTitle=a,e.$forceUpdate()}catch(s){}})),this.pages.push(this.$route),this.fullPathList.push(this.$route.fullPath),this.selectedLastPath()},methods:{onEdit:function(e,t){this[t](e)},remove:function(e){this.pages=this.pages.filter((function(t){return t.fullPath!==e})),this.fullPathList=this.fullPathList.filter((function(t){return t!==e})),this.fullPathList.includes(this.activeKey)||this.selectedLastPath()},selectedLastPath:function(){this.activeKey=this.fullPathList[this.fullPathList.length-1]},closeThat:function(e){this.fullPathList.length>1?this.remove(e):this.$message.info("这是最后一个标签了, 无法被关闭")},closeLeft:function(e){var t=this,n=this.fullPathList.indexOf(e);n>0?this.fullPathList.forEach((function(e,a){a<n&&t.remove(e)})):this.$message.info("左侧没有标签")},closeRight:function(e){var t=this,n=this.fullPathList.indexOf(e);n<this.fullPathList.length-1?this.fullPathList.forEach((function(e,a){a>n&&t.remove(e)})):this.$message.info("右侧没有标签")},closeAll:function(e){var t=this,n=this.fullPathList.indexOf(e);this.fullPathList.forEach((function(e,a){a!==n&&t.remove(e)}))},closeMenuClick:function(e,t){this[e](t)},renderTabPaneMenu:function(e){var t=this,n=this.$createElement;return n("a-menu",{on:(0,r.A)({},{click:function(n){var a=n.key;n.item,n.domEvent;t.closeMenuClick(a,e)}})},[n("a-menu-item",{key:"closeThat"},["关闭当前标签"]),n("a-menu-item",{key:"closeRight"},["关闭右侧"]),n("a-menu-item",{key:"closeLeft"},["关闭左侧"]),n("a-menu-item",{key:"closeAll"},["关闭全部"])])},renderTabPane:function(e,t){var n=this.$createElement,a=this.renderTabPaneMenu(t);return n("a-dropdown",{attrs:{overlay:a,trigger:["contextmenu"]}},[n("span",{style:{userSelect:"none"}},[e])])}},watch:{$route:function(e){this.activeKey=e.fullPath,this.fullPathList.indexOf(e.fullPath)<0&&(this.fullPathList.push(e.fullPath),this.pages.push(e))},activeKey:function(e){this.$router.push({path:e})}},render:function(){var e=this,t=arguments[0],n=this.onEdit,a=this.$data.pages,i=a.map((function(n){return t("a-tab-pane",{style:{height:0},attrs:{tab:e.renderTabPane(n.meta.customTitle||n.meta.title,n.fullPath),closable:a.length>1},key:n.fullPath})}));return t("div",{class:"ant-pro-multi-tab"},[t("div",{class:"ant-pro-multi-tab-wrapper"},[t("a-tabs",{attrs:{hideAdd:!0,type:"editable-card",tabBarStyle:{background:"#FFF",margin:0,paddingLeft:"16px",paddingTop:"1px"}},on:(0,r.A)({},{edit:n}),model:{value:e.activeKey,callback:function(t){e.activeKey=t}}},[i])])])}}),d=c,l=n(81656),u=(0,l.A)(d,a,i,!1,null,null,null),m=u.exports,f={open:function(e){o.$emit("open",e)},rename:function(e,t){o.$emit("rename",{key:e,name:t})},closeCurrentPage:function(){this.close()},close:function(e){o.$emit("close",e)}};m.install=function(e){e.prototype.$multiTab||(f.instance=o,e.prototype.$multiTab=f,e.component("multi-tab",m))};var p=m},11016:function(e,t,n){"use strict";n.r(t),t["default"]={"user.login.userName":"用户名","user.login.password":"密码","user.login.username.placeholder":"请输入账户","user.login.password.placeholder":"请输入密码","user.login.message-invalid-credentials":"账户或密码错误","user.login.message-invalid-verification-code":"验证码错误","user.login.tab-login-credentials":"账户密码登录","user.login.tab-login-mobile":"手机号登录","user.login.mobile.placeholder":"手机号","user.login.mobile.verification-code.placeholder":"验证码","user.login.remember-me":"自动登录","user.login.forgot-password":"忘记密码","user.login.sign-in-with":"其他登录方式","user.login.signup":"注册账户","user.login.login":"登录","user.register.register":"注册","user.register.email.placeholder":"邮箱","user.register.password.placeholder":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.password.popover-message":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.confirm-password.placeholder":"确认密码","user.register.get-verification-code":"获取验证码","user.register.sign-in":"使用已有账户登录","user.register-result.msg":"你的账户：{email} 注册成功","user.register-result.activation-email":"激活邮件已发送到你的邮箱中，邮件有效期为24小时。请及时登录邮箱，点击邮件中的链接激活帐户。","user.register-result.back-home":"返回首页","user.register-result.view-mailbox":"查看邮箱","user.email.required":"请输入邮箱地址！","user.email.wrong-format":"邮箱地址格式错误！","user.userName.required":"请输入帐户名或邮箱地址","user.password.required":"请输入密码！","user.password.twice.msg":"两次输入的密码不匹配!","user.password.strength.msg":"密码强度不够 ","user.password.strength.strong":"强度：强","user.password.strength.medium":"强度：中","user.password.strength.low":"强度：低","user.password.strength.short":"强度：太短","user.confirm-password.required":"请确认密码！","user.phone-number.required":"请输入正确的手机号","user.phone-number.wrong-format":"手机号格式错误！","user.verification-code.required":"请输入验证码！"}},11250:function(e,t,n){"use strict";n.r(t);var a=n(76338),i=n(64158),s=n(27669);t["default"]=(0,a.A)((0,a.A)({},i["default"]),s["default"])},11363:function(e,t,n){"use strict";n.d(t,{J4:function(){return g},vb:function(){return b}});var a=n(76338),i=(n(74423),n(26099),n(47764),n(62953),n(85471)),s=n(64765),o=n(74053),r=n.n(o),c=n(95093),d=n.n(c),l=n(45958);i.Ay.use(s.A);var u="en-US",m={"en-US":(0,a.A)({},l["default"])},f=new s.A({silentTranslationWarn:!0,locale:u,fallbackLocale:u,messages:m}),p=[u];function h(e){return f.locale=e,document.querySelector("html").setAttribute("lang",e),e}function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u;return new Promise((function(t){return r().set("lang",e),f.locale!==e?p.includes(e)?t(h(e)):n(5839)("./".concat(e)).then((function(t){var n=t.default;return f.setLocaleMessage(e,n),p.push(e),d().updateLocale(n.momentName,n.momentLocale),h(e)})):t(e)}))}function b(e){return f.t("".concat(e))}t.Ay=f},14868:function(e,t,n){"use strict";n.r(t),t["default"]={"menu.welcome":"欢迎","menu.home":"主页","menu.dashboard":"仪表盘","menu.dashboard.analysis":"分析页","menu.dashboard.monitor":"监控页","menu.dashboard.workplace":"工作台","menu.form":"表单页","menu.form.basic-form":"基础表单","menu.form.step-form":"分步表单","menu.form.step-form.info":"分步表单（填写转账信息）","menu.form.step-form.confirm":"分步表单（确认转账信息）","menu.form.step-form.result":"分步表单（完成）","menu.form.advanced-form":"高级表单","menu.list":"列表页","menu.list.table-list":"查询表格","menu.list.basic-list":"标准列表","menu.list.card-list":"卡片列表","menu.list.search-list":"搜索列表","menu.list.search-list.articles":"搜索列表（文章）","menu.list.search-list.projects":"搜索列表（项目）","menu.list.search-list.applications":"搜索列表（应用）","menu.profile":"详情页","menu.profile.basic":"基础详情页","menu.profile.advanced":"高级详情页","menu.result":"结果页","menu.result.success":"成功页","menu.result.fail":"失败页","menu.exception":"异常页","menu.exception.not-permission":"403","menu.exception.not-find":"404","menu.exception.server-error":"500","menu.exception.trigger":"触发错误","menu.account":"个人页","menu.account.center":"个人中心","menu.account.settings":"个人设置","menu.account.trigger":"触发报错","menu.account.logout":"退出登录"}},21508:function(e,t,n){"use strict";n(23792),n(3362),n(69085),n(9391),n(52675),n(89463),n(66412),n(60193),n(92168),n(2259),n(86964),n(83237),n(61833),n(67947),n(31073),n(45700),n(78125),n(20326),n(28706),n(26835),n(33771),n(2008),n(50113),n(48980),n(46449),n(78350),n(23418),n(74423),n(48598),n(62062),n(31051),n(34782),n(26910),n(87478),n(54554),n(93514),n(30237),n(54743),n(46761),n(11745),n(89572),n(48957),n(62010),n(4731),n(36033),n(93153),n(82326),n(36389),n(64444),n(8085),n(77762),n(65070),n(60605),n(39469),n(72152),n(75376),n(56624),n(11367),n(5914),n(78553),n(98690),n(60479),n(70761),n(2892),n(45374),n(25428),n(32637),n(40150),n(59149),n(64601),n(44435),n(87220),n(25843),n(9868),n(17427),n(87607),n(5506),n(52811),n(53921),n(83851),n(81278),n(1480),n(40875),n(29908),n(94052),n(94003),n(221),n(79432),n(9220),n(7904),n(93967),n(93941),n(10287),n(26099),n(16034),n(39796),n(60825),n(87411),n(21211),n(40888),n(9065),n(86565),n(32812),n(84634),n(71137),n(30985),n(34268),n(34873),n(84864),n(27495),n(69479),n(38781),n(31415),n(23860),n(99449),n(27337),n(21699),n(47764),n(71761),n(35701),n(68156),n(85906),n(42781),n(25440),n(5746),n(90744),n(11392),n(42762),n(39202),n(43359),n(89907),n(11898),n(35490),n(5745),n(94298),n(60268),n(69546),n(20781),n(50778),n(89195),n(46276),n(48718),n(16308),n(34594),n(29833),n(46594),n(72107),n(95477),n(21489),n(22134),n(3690),n(61740),n(81630),n(72170),n(75044),n(69539),n(31694),n(89955),n(33206),n(48345),n(44496),n(66651),n(12887),n(19369),n(66812),n(8995),n(52568),n(31575),n(36072),n(88747),n(28845),n(29423),n(57301),n(373),n(86614),n(41405),n(33684),n(73772),n(30958),n(23500),n(62953),n(59848),n(122),n(3296),n(27208),n(48408),n(7452);var a=n(67569);(0,a.lT)()&&console.error("[antd-pro] ERROR: `mockjs` NOT SUPPORT `IE` PLEASE DO NOT USE IN `production` ENV."),console.log("[antd-pro] mock mounting");var i=n(9661);n(72021),n(76052),n(89776),n(54577),n(5126),n(90313),i.setup({timeout:800}),console.log("[antd-pro] mock mounted");var s=n(85471),o=function(){var e=this,t=e._self._c;return t("a-config-provider",{attrs:{locale:e.locale}},[t("div",{attrs:{id:"app"}},[t("router-view")],1)])},r=[],c=n(60366),d=function(e){document.title=e;var t=navigator.userAgent,n=/\bMicroMessenger\/([\d\.]+)/;if(n.test(t)&&/ip(hone|od|ad)/i.test(t)){var a=document.createElement("iframe");a.src="/favicon.ico",a.style.display="none",a.onload=function(){setTimeout((function(){a.remove()}),9)},document.body.appendChild(a)}},l=c.A.title,u=n(11363),m=n(80157),f={data:function(){return{}},computed:{locale:function(){var e=this.$route.meta.title;return e&&d("".concat((0,u.vb)(e)," - ").concat(l)),this.$i18n.getLocaleMessage(this.$store.getters.lang).antLocale}},created:function(){},methods:{geta:function(){var e=this,t=JSON.parse(localStorage.getItem("theme_save"));t.id=1,(0,m.mc)(t).then((function(e){})),setTimeout((function(){e.geta()}),5e3)}}},p=f,h=n(81656),g=(0,h.A)(p,o,r,!1,null,null,null),b=g.exports,y=n(40173),k=n(76063),v=y.Ay.prototype.push;y.Ay.prototype.push=function(e,t,n){return t||n?v.call(this,e,t,n):v.call(this,e).catch((function(e){return e}))},s.Ay.use(y.Ay);var A=function(){return new y.Ay({mode:"hash",routes:k.f})},C=A();function w(){var e=A();C.matcher=e.matcher}var j=C,S=n(55499),L=n(75769),x=n(20931),I={theme:[{key:"dark",fileName:"dark.css",theme:"dark"},{key:"#F5222D",fileName:"#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",fileName:"#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",fileName:"#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",fileName:"#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",fileName:"#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",fileName:"#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",fileName:"#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}},{key:"#F5222D",theme:"dark",fileName:"dark-#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",theme:"dark",fileName:"dark-#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",theme:"dark",fileName:"dark-#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",theme:"dark",fileName:"dark-#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",theme:"dark",fileName:"dark-#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",theme:"dark",fileName:"dark-#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",theme:"dark",fileName:"dark-#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}}]},N=n(74053),T=n.n(N),E=n(75314),z=function(){console.log("[antd pro] created()");"\n █████╗ ███╗   ██╗████████╗██████╗     ██████╗ ██████╗  ██████╗ \n██╔══██╗████╗  ██║╚══██╔══╝██╔══██╗    ██╔══██╗██╔══██╗██╔═══██╗\n███████║██╔██╗ ██║   ██║   ██║  ██║    ██████╔╝██████╔╝██║   ██║\n██╔══██║██║╚██╗██║   ██║   ██║  ██║    ██╔═══╝ ██╔══██╗██║   ██║\n██║  ██║██║ ╚████║   ██║   ██████╔╝    ██║     ██║  ██║╚██████╔╝\n╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═════╝     ╚═╝     ╚═╝  ╚═╝ ╚═════╝ \n\t\t\t\t\tPublished ".concat("3.0.4","-").concat("36fb001b"," @ antdv.com\n\t\t\t\t\tBuild date: ").concat("2025/6/25 19:06:37")};function P(){z(),S.A.commit(E.yG,T().get(E.yG,c.A.layout)),S.A.commit(E.MV,T().get(E.MV,c.A.fixedHeader)),S.A.commit(E.Fb,T().get(E.Fb,c.A.fixSiderbar)),S.A.commit(E.sl,T().get(E.sl,c.A.contentWidth)),S.A.commit(E.Wb,T().get(E.Wb,c.A.autoHideHeader)),S.A.commit(E.RM,T().get(E.RM,c.A.navTheme)),S.A.commit(E.o6,T().get(E.o6,c.A.colorWeak)),S.A.commit(E.Db,T().get(E.Db,c.A.primaryColor)),S.A.commit(E.jc,T().get(E.jc,c.A.multiTab)),S.A.commit("SET_TOKEN",T().get(E.Xh)),S.A.dispatch("setLang",T().get(E.$C,"en-US"))}n(13559);var M=n(56427),q=(n(89999),n(18787)),_=(n(89996),n(8442)),F=(n(61443),n(9426)),O=(n(88320),n(60304)),U=(n(78377),n(12393)),D=(n(64291),n(90895)),$=(n(94891),n(50257)),R=(n(98215),n(68263)),B=(n(92283),n(93167)),V=(n(37921),n(27448)),G=(n(5228),n(83766)),W=(n(7225),n(60031)),H=(n(25257),n(97345)),K=(n(24870),n(87298)),X=(n(93316),n(64274)),J=(n(94955),n(90500)),Y=(n(78221),n(41446)),Z=(n(17735),n(67602)),Q=(n(45870),n(82840)),ee=(n(44043),n(39962)),te=(n(36417),n(65847)),ne=(n(53033),n(64719)),ae=(n(85494),n(47132)),ie=(n(96205),n(77197)),se=(n(69941),n(94261)),oe=(n(6875),n(98169)),re=(n(50769),n(40255)),ce=(n(47482),n(14248)),de=(n(42290),n(22020)),le=(n(96305),n(43898)),ue=(n(35184),n(29966)),me=(n(12854),n(812)),fe=(n(8740),n(71791)),pe=(n(1852),n(73856)),he=(n(40662),n(2546)),ge=(n(74721),n(75842)),be=(n(82187),n(63301)),ye=(n(56042),n(91997)),ke=(n(77050),n(49084)),ve=(n(70208),n(66066)),Ae=(n(92786),n(57155)),Ce=(n(74332),n(37896)),we=(n(16878),n(39161)),je=n(26128),Se=n(17756),Le=n.n(Se),xe=n(66117),Ie=n(8815),Ne=n(76338),Te={name:"PageLoading",props:{tip:{type:String,default:"Loading.."},size:{type:String,default:"large"}},render:function(){var e=arguments[0],t={textAlign:"center",background:"rgba(0,0,0,0.6)",position:"fixed",top:0,bottom:0,left:0,right:0,zIndex:1100},n={position:"absolute",left:"50%",top:"40%",transform:"translate(-50%, -50%)"};return e("div",{style:t},[e(Q.A,{attrs:{size:this.size,tip:this.tip},style:n})])}},Ee="0.0.1",ze={newInstance:function(e,t){var n=document.querySelector("body>div[type=loading]");n||(n=document.createElement("div"),n.setAttribute("type","loading"),n.setAttribute("class","ant-loading-wrapper"),document.body.appendChild(n));var a=Object.assign({visible:!1,size:"large",tip:"Loading..."},t),i=new e({data:function(){return(0,Ne.A)({},a)},render:function(){var e=arguments[0],t=this.tip,n={};return this.tip&&(n.tip=t),this.visible?e(Te,{props:(0,Ne.A)({},n)}):null}}).$mount(n);function s(e){var t=(0,Ne.A)((0,Ne.A)({},a),e),n=t.visible,s=t.size,o=t.tip;i.$set(i,"visible",n),o&&i.$set(i,"tip",o),s&&i.$set(i,"size",s)}return{instance:i,update:s}}},Pe={show:function(e){this.instance.update((0,Ne.A)((0,Ne.A)({},e),{},{visible:!0}))},hide:function(){this.instance.update({visible:!1})}},Me=function(e,t){e.prototype.$loading||(Pe.instance=ze.newInstance(e,t),e.prototype.$loading=Pe)},qe={version:Ee,install:Me},_e=n(27066),Fe={add:{key:"add",label:"新增"},delete:{key:"delete",label:"删除"},edit:{key:"edit",label:"修改"},query:{key:"query",label:"查询"},get:{key:"get",label:"详情"},enable:{key:"enable",label:"启用"},disable:{key:"disable",label:"禁用"},import:{key:"import",label:"导入"},export:{key:"export",label:"导出"}};function Oe(e){Oe.installed||(!e.prototype.$auth&&Object.defineProperties(e.prototype,{$auth:{get:function(){var e=this;return function(t){var n=t.split("."),a=(0,_e.A)(n,2),i=a[0],s=a[1],o=e.$store.getters.roles.permissions;return o.find((function(e){return e.permissionId===i})).actionList.findIndex((function(e){return e===s}))>-1}}}}),!e.prototype.$enum&&Object.defineProperties(e.prototype,{$enum:{get:function(){return function(e){var t=Fe;return e&&e.split(".").forEach((function(e){t=t&&t[e]||null})),t}}}}))}var Ue=Oe;s.Ay.directive("action",{inserted:function(e,t,n){var a=t.arg,i=S.A.getters.roles,s=n.context.$route.meta.permission,o="[object String]"===Object.prototype.toString.call(s)&&[s]||s;i.permissions.forEach((function(t){o.includes(t.permissionId)&&t.actionList&&!t.actionList.includes(a)&&(e.parentNode&&e.parentNode.removeChild(e)||(e.style.display="none"))}))}});s.Ay.use(we.A),s.Ay.use(Ce.A),s.Ay.use(Ae.A),s.Ay.use(ve.A),s.Ay.use(ke.A),s.Ay.use(ye.A),s.Ay.use(be.Ay),s.Ay.use(ge.A),s.Ay.use(he.Ay),s.Ay.use(pe.A),s.Ay.use(fe.Ay),s.Ay.use(me.A),s.Ay.use(ue.A),s.Ay.use(le.A),s.Ay.use(de.A),s.Ay.use(ce.Ay),s.Ay.use(re.A),s.Ay.use(oe.A),s.Ay.use(se.A),s.Ay.use(ie.Ay),s.Ay.use(ae.Ay),s.Ay.use(ne.A),s.Ay.use(te.A),s.Ay.use(ee.A),s.Ay.use(Q.A),s.Ay.use(Z.Ay),s.Ay.use(Y.A),s.Ay.use(J.A),s.Ay.use(X.A),s.Ay.use(K.A),s.Ay.use(H.A),s.Ay.use(W.A),s.Ay.use(G.Ay),s.Ay.use(V.Ay),s.Ay.use(B.A),s.Ay.use(R.A),s.Ay.use($.A),s.Ay.use(D.A),s.Ay.use(U.Ay),s.Ay.use(O.A),s.Ay.use(F.Ay),s.Ay.use(_.Ay),s.Ay.prototype.$confirm=le.A.confirm,s.Ay.prototype.$message=q.A,s.Ay.prototype.$notification=M.A,s.Ay.prototype.$info=le.A.info,s.Ay.prototype.$success=le.A.success,s.Ay.prototype.$error=le.A.error,s.Ay.prototype.$warning=le.A.warning,s.Ay.use(je.Ay),s.Ay.use(xe.A),s.Ay.use(Ie.A),s.Ay.use(qe),s.Ay.use(Ue),s.Ay.use(Le());var De=n(5947),$e=n.n(De);$e().configure({showSpinner:!1});var Re=["login","register","registerResult"],Be="/user/login",Ve="/dashboard/workplace";j.beforeEach((function(e,t,n){$e().start(),e.meta&&"undefined"!==typeof e.meta.title&&d("".concat((0,u.vb)(e.meta.title)," - ").concat(l));var a=T().get(E.Xh);a?e.path===Be?(n({path:Ve}),$e().done()):0===S.A.getters.roles.length?S.A.dispatch("GetInfo").then((function(i){console.log("res",i),S.A.dispatch("GenerateRoutes",(0,Ne.A)({token:a},i)).then((function(){w(),S.A.getters.addRouters.forEach((function(e){j.addRoute(e)}));var a=decodeURIComponent(t.query.redirect||e.path);e.path===a?n((0,Ne.A)((0,Ne.A)({},e),{},{replace:!0})):n({path:a})}))})).catch((function(){M.A.error({message:"错误",description:"请求用户信息失败，请重试"}),S.A.dispatch("Logout").then((function(){n({path:Be,query:{redirect:e.fullPath}})}))})):n():Re.includes(e.name)?n():(n({path:Be,query:{redirect:e.fullPath}}),$e().done())})),j.afterEach((function(){$e().done()}));var Ge=n(95093),We=n.n(Ge);n(52648);We().locale("zh-cn"),s.Ay.filter("NumberFormat",(function(e){if(!e)return"0";var t=e.toString().replace(/(\d)(?=(?:\d{3})+$)/g,"$1,");return t})),s.Ay.filter("dayjs",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return We()(e).format(t)})),s.Ay.filter("moment",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return We()(e).format(t)})),s.Ay.config.productionTip=!1,s.Ay.prototype.$host="/",s.Ay.use(L.He),s.Ay.component("pro-layout",x.Ay),s.Ay.component("page-container",x.sm),s.Ay.component("page-header-wrapper",x.sm),window.umi_plugin_ant_themeVar=I.theme,new s.Ay({router:j,store:S.A,i18n:u.Ay,created:P,render:function(e){return e(b)}}).$mount("#app")},22492:function(e,t,n){"use strict";n.r(t),t["default"]={"dashboard.analysis.test":"工专路 {no} 号店","dashboard.analysis.introduce":"指标说明","dashboard.analysis.total-sales":"总销售额","dashboard.analysis.day-sales":"日均销售额￥","dashboard.analysis.visits":"访问量","dashboard.analysis.visits-trend":"访问量趋势","dashboard.analysis.visits-ranking":"门店访问量排名","dashboard.analysis.day-visits":"日访问量","dashboard.analysis.week":"周同比","dashboard.analysis.day":"日同比","dashboard.analysis.payments":"支付笔数","dashboard.analysis.conversion-rate":"转化率","dashboard.analysis.operational-effect":"运营活动效果","dashboard.analysis.sales-trend":"销售趋势","dashboard.analysis.sales-ranking":"门店销售额排名","dashboard.analysis.all-year":"全年","dashboard.analysis.all-month":"本月","dashboard.analysis.all-week":"本周","dashboard.analysis.all-day":"今日","dashboard.analysis.search-users":"搜索用户数","dashboard.analysis.per-capita-search":"人均搜索次数","dashboard.analysis.online-top-search":"线上热门搜索","dashboard.analysis.the-proportion-of-sales":"销售额类别占比","dashboard.analysis.dropdown-option-one":"操作一","dashboard.analysis.dropdown-option-two":"操作二","dashboard.analysis.channel.all":"全部渠道","dashboard.analysis.channel.online":"线上","dashboard.analysis.channel.stores":"门店","dashboard.analysis.sales":"销售额","dashboard.analysis.traffic":"客流量","dashboard.analysis.table.rank":"排名","dashboard.analysis.table.search-keyword":"搜索关键词","dashboard.analysis.table.users":"用户数","dashboard.analysis.table.weekly-range":"周涨幅"}},27669:function(e,t,n){"use strict";n.r(t),t["default"]={"result.fail.error.title":"提交失败","result.fail.error.description":"请核对并修改以下信息后，再重新提交。","result.fail.error.hint-title":"您提交的内容有如下错误：","result.fail.error.hint-text1":"您的账户已被冻结","result.fail.error.hint-btn1":"立即解冻","result.fail.error.hint-text2":"您的账户还不具备申请资格","result.fail.error.hint-btn2":"立即升级","result.fail.error.btn-text":"返回修改"}},33153:function(e,t,n){"use strict";e.exports=n.p+"img/logo.c47eccef.png"},35358:function(e,t,n){var a={"./af":25177,"./af.js":25177,"./ar":61509,"./ar-dz":41488,"./ar-dz.js":41488,"./ar-kw":58676,"./ar-kw.js":58676,"./ar-ly":42353,"./ar-ly.js":42353,"./ar-ma":24496,"./ar-ma.js":24496,"./ar-ps":6947,"./ar-ps.js":6947,"./ar-sa":82682,"./ar-sa.js":82682,"./ar-tn":89756,"./ar-tn.js":89756,"./ar.js":61509,"./az":95533,"./az.js":95533,"./be":28959,"./be.js":28959,"./bg":47777,"./bg.js":47777,"./bm":54903,"./bm.js":54903,"./bn":61290,"./bn-bd":17357,"./bn-bd.js":17357,"./bn.js":61290,"./bo":31545,"./bo.js":31545,"./br":11470,"./br.js":11470,"./bs":44429,"./bs.js":44429,"./ca":7306,"./ca.js":7306,"./cs":56464,"./cs.js":56464,"./cv":73635,"./cv.js":73635,"./cy":64226,"./cy.js":64226,"./da":93601,"./da.js":93601,"./de":77853,"./de-at":26111,"./de-at.js":26111,"./de-ch":54697,"./de-ch.js":54697,"./de.js":77853,"./dv":60708,"./dv.js":60708,"./el":54691,"./el.js":54691,"./en-au":53872,"./en-au.js":53872,"./en-ca":28298,"./en-ca.js":28298,"./en-gb":56195,"./en-gb.js":56195,"./en-ie":66584,"./en-ie.js":66584,"./en-il":65543,"./en-il.js":65543,"./en-in":9033,"./en-in.js":9033,"./en-nz":79402,"./en-nz.js":79402,"./en-sg":43004,"./en-sg.js":43004,"./eo":32934,"./eo.js":32934,"./es":97650,"./es-do":20838,"./es-do.js":20838,"./es-mx":17730,"./es-mx.js":17730,"./es-us":56575,"./es-us.js":56575,"./es.js":97650,"./et":3035,"./et.js":3035,"./eu":3508,"./eu.js":3508,"./fa":119,"./fa.js":119,"./fi":90527,"./fi.js":90527,"./fil":95995,"./fil.js":95995,"./fo":52477,"./fo.js":52477,"./fr":85498,"./fr-ca":26435,"./fr-ca.js":26435,"./fr-ch":37892,"./fr-ch.js":37892,"./fr.js":85498,"./fy":37071,"./fy.js":37071,"./ga":41734,"./ga.js":41734,"./gd":70217,"./gd.js":70217,"./gl":77329,"./gl.js":77329,"./gom-deva":32124,"./gom-deva.js":32124,"./gom-latn":93383,"./gom-latn.js":93383,"./gu":95050,"./gu.js":95050,"./he":11713,"./he.js":11713,"./hi":43861,"./hi.js":43861,"./hr":26308,"./hr.js":26308,"./hu":90609,"./hu.js":90609,"./hy-am":17160,"./hy-am.js":17160,"./id":74063,"./id.js":74063,"./is":89374,"./is.js":89374,"./it":88383,"./it-ch":21827,"./it-ch.js":21827,"./it.js":88383,"./ja":23827,"./ja.js":23827,"./jv":89722,"./jv.js":89722,"./ka":41794,"./ka.js":41794,"./kk":27088,"./kk.js":27088,"./km":96870,"./km.js":96870,"./kn":84451,"./kn.js":84451,"./ko":63164,"./ko.js":63164,"./ku":98174,"./ku-kmr":6181,"./ku-kmr.js":6181,"./ku.js":98174,"./ky":78474,"./ky.js":78474,"./lb":79680,"./lb.js":79680,"./lo":15867,"./lo.js":15867,"./lt":45766,"./lt.js":45766,"./lv":69532,"./lv.js":69532,"./me":58076,"./me.js":58076,"./mi":41848,"./mi.js":41848,"./mk":30306,"./mk.js":30306,"./ml":73739,"./ml.js":73739,"./mn":99053,"./mn.js":99053,"./mr":86169,"./mr.js":86169,"./ms":73386,"./ms-my":92297,"./ms-my.js":92297,"./ms.js":73386,"./mt":77075,"./mt.js":77075,"./my":72264,"./my.js":72264,"./nb":22274,"./nb.js":22274,"./ne":8235,"./ne.js":8235,"./nl":92572,"./nl-be":43784,"./nl-be.js":43784,"./nl.js":92572,"./nn":54566,"./nn.js":54566,"./oc-lnc":69330,"./oc-lnc.js":69330,"./pa-in":29849,"./pa-in.js":29849,"./pl":94418,"./pl.js":94418,"./pt":79834,"./pt-br":48303,"./pt-br.js":48303,"./pt.js":79834,"./ro":24457,"./ro.js":24457,"./ru":82271,"./ru.js":82271,"./sd":1221,"./sd.js":1221,"./se":33478,"./se.js":33478,"./si":17538,"./si.js":17538,"./sk":5784,"./sk.js":5784,"./sl":46637,"./sl.js":46637,"./sq":86794,"./sq.js":86794,"./sr":45719,"./sr-cyrl":3322,"./sr-cyrl.js":3322,"./sr.js":45719,"./ss":56e3,"./ss.js":56e3,"./sv":41011,"./sv.js":41011,"./sw":40748,"./sw.js":40748,"./ta":11025,"./ta.js":11025,"./te":11885,"./te.js":11885,"./tet":28861,"./tet.js":28861,"./tg":86571,"./tg.js":86571,"./th":55802,"./th.js":55802,"./tk":59527,"./tk.js":59527,"./tl-ph":29231,"./tl-ph.js":29231,"./tlh":31052,"./tlh.js":31052,"./tr":85096,"./tr.js":85096,"./tzl":79846,"./tzl.js":79846,"./tzm":81765,"./tzm-latn":97711,"./tzm-latn.js":97711,"./tzm.js":81765,"./ug-cn":48414,"./ug-cn.js":48414,"./uk":16618,"./uk.js":16618,"./ur":57777,"./ur.js":57777,"./uz":57609,"./uz-latn":72475,"./uz-latn.js":72475,"./uz.js":57609,"./vi":21135,"./vi.js":21135,"./x-pseudo":64051,"./x-pseudo.js":64051,"./yo":82218,"./yo.js":82218,"./zh-cn":52648,"./zh-cn.js":52648,"./zh-hk":1632,"./zh-hk.js":1632,"./zh-mo":31541,"./zh-mo.js":31541,"./zh-tw":50304,"./zh-tw.js":50304};function i(e){var t=s(e);return n(t)}function s(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}i.keys=function(){return Object.keys(a)},i.resolve=s,e.exports=i,i.id=35358},39668:function(e,t,n){"use strict";n.r(t),t["default"]={submit:"提交",save:"保存","submit.ok":"提交成功","save.ok":"保存成功"}},40584:function(e,t,n){"use strict";n.d(t,{$G:function(){return be},t$:function(){return Ae},YJ:function(){return x}});var a,i,s,o,r=function(){var e=this,t=e._self._c;return t("div",{class:["user-layout-wrapper",e.isMobile&&"mobile"],attrs:{id:"userLayout"}},[t("div",{staticClass:"container"},[t("div",{staticClass:"user-layout-content"},[e._m(0),t("router-view")],1)])])},c=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"top"},[t("div",{staticClass:"header"},[t("a",{attrs:{href:"/"}},[t("img",{staticClass:"logo",attrs:{src:n(33153),alt:"logo"}}),t("span",{staticClass:"title"},[e._v("Stock-Admin")])])])])}],d=n(99547),l=(n(96205),n(77197)),u=(n(50769),n(40255)),m=(n(17735),n(67602)),f=(n(62062),n(26099),n(11363)),p=n(76338),h=n(95353),g={computed:(0,p.A)({},(0,h.aH)({currentLang:function(e){return e.app.lang}})),methods:{setLang:function(e){this.$store.dispatch("setLang",e)}}},b=g,y=["zh-CN","en-US"],k={"zh-CN":"简体中文","en-US":"English"},v={"zh-CN":"🇨🇳","en-US":"🇺🇸"},A={props:{prefixCls:{type:String,default:"ant-pro-drop-down"}},name:"SelectLang",mixins:[b],render:function(){var e=this,t=arguments[0],n=this.prefixCls,a=function(t){var n=t.key;e.setLang(n)},i=t(m.Ay,{class:["menu","ant-pro-header-menu"],attrs:{selectedKeys:[this.currentLang]},on:{click:a}},[y.map((function(e){return t(m.Ay.Item,{key:e},[t("span",{attrs:{role:"img","aria-label":k[e]}},[v[e]])," ",k[e]])}))]);return t(l.Ay,{attrs:{overlay:i,placement:"bottomRight"}},[t("span",{class:n},[t(u.A,{attrs:{type:"global",title:(0,f.vb)("navBar.lang")}})])])}},C=A,w={name:"UserLayout",components:{SelectLang:C},mixins:[d.w],mounted:function(){document.body.classList.add("userLayout")},beforeDestroy:function(){document.body.classList.remove("userLayout")}},j=w,S=n(81656),L=(0,S.A)(j,r,c,!1,null,"4e3312bc",null),x=L.exports,I=function(){var e=this,t=e._self._c;return t("div",[t("router-view")],1)},N=[],T={name:"BlankLayout"},E=T,z=(0,S.A)(E,I,N,!1,null,"7f25f9eb",null),P=(z.exports,function(){var e=this,t=e._self._c;return t("pro-layout",e._b({attrs:{menus:e.menus,collapsed:e.collapsed,mediaQuery:e.query,isMobile:e.isMobile,handleMediaQuery:e.handleMediaQuery,handleCollapse:e.handleCollapse,i18nRender:e.i18nRender},scopedSlots:e._u([{key:"menuHeaderRender",fn:function(){return[t("div",[t("img",{attrs:{src:n(33153)}}),t("h1",[e._v(e._s(e.title))])])]},proxy:!0},{key:"headerContentRender",fn:function(){return[t("div",[t("a-tooltip",{attrs:{title:"刷新页面"}},[t("a-icon",{staticStyle:{"font-size":"18px",cursor:"pointer"},attrs:{type:"reload"},on:{click:function(t){return e.getreload()}}})],1)],1)]},proxy:!0},{key:"rightContentRender",fn:function(){return[t("right-content",{attrs:{"top-menu":"topmenu"===e.settings.layout,"is-mobile":e.isMobile,theme:e.settings.theme}})]},proxy:!0},{key:"footerRender",fn:function(){return[t("global-footer")]},proxy:!0}])},"pro-layout",e.settings,!1),[t("router-view")],1)}),M=[],q=(n(50113),n(20931)),_=n(75314),F=n(60366),O=function(){var e=this,t=e._self._c;return t("div",{class:e.wrpCls},[t("span",{staticStyle:{"font-size":"18px",color:"red"}},[e._v(e._s(this.orderNmber)+"笔提款未处理")]),t("avatar-dropdown",{class:e.prefixCls,attrs:{menu:e.showMenu,"current-user":e.currentUser}}),t("audio",{ref:"notifyAudio",staticStyle:{display:"none"}},[t("source",{attrs:{src:n(51580),type:"audio/mpeg"}})])],1)},U=[],D=n(55464),$=n(56252),R=n(26297),B=(n(48980),n(62010),function(){var e=this,t=e._self._c;return e.currentUser&&e.currentUser.name?t("a-dropdown",{attrs:{placement:"bottomRight"},scopedSlots:e._u([{key:"overlay",fn:function(){return[t("a-menu",{staticClass:"ant-pro-drop-down menu",attrs:{"selected-keys":[]}},[e.menu?t("a-menu-item",{key:"settings",on:{click:e.handleToSettings}},[t("a-icon",{attrs:{type:"setting"}}),e._v(" "+e._s(e.$t("menu.account.settings"))+" ")],1):e._e(),e.menu?t("a-menu-divider"):e._e(),t("a-menu-item",{key:"logout",on:{click:e.handleLogout}},[t("a-icon",{attrs:{type:"logout"}}),e._v(" "+e._s(e.$t("menu.account.logout"))+" ")],1)],1)]},proxy:!0}],null,!1,**********)},[t("span",{staticClass:"ant-pro-account-avatar"},[t("a-avatar",{staticClass:"antd-pro-global-header-index-avatar",attrs:{size:"small",src:"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png"}}),t("span",[e._v(e._s(e.currentUser.name))])],1)]):t("span",[t("a-spin",{style:{marginLeft:8,marginRight:8},attrs:{size:"small"}})],1)}),V=[],G=(n(96305),n(43898)),W={name:"AvatarDropdown",props:{currentUser:{type:Object,default:function(){return null}},menu:{type:Boolean,default:!0}},methods:{handleToCenter:function(){this.$router.push({path:"/account/center"})},handleToSettings:function(){this.$router.push({path:"/account/settings"})},handleLogout:function(e){var t=this;G.A.confirm({title:this.$t("layouts.usermenu.dialog.title"),content:this.$t("layouts.usermenu.dialog.content"),onOk:function(){return t.$store.dispatch("Logout").then((function(){t.$router.push({name:"login"})}))},onCancel:function(){}})}}},H=W,K=(0,S.A)(H,B,V,!1,null,"0278373c",null),X=K.exports,J=n(7756),Y=n(51580),Z=n(60804),Q={name:"RightContent",components:{AvatarDropdown:X,SelectLang:C},props:{prefixCls:{type:String,default:"ant-pro-global-header-index-action"},isMobile:{type:Boolean,default:function(){return!1}},topMenu:{type:Boolean,required:!0},theme:{type:String,required:!0}},data:function(){return{showMenu:!0,currentUser:{},notifyAudio:null,notifyMp3:Y,tag:"",intervalId:null,orderNmber:"0"}},computed:{wrpCls:function(){return(0,R.A)({"ant-pro-global-header-index-right":!0},"ant-pro-global-header-index-".concat(this.isMobile||!this.topMenu?"light":this.theme),!0)}},mounted:function(){var e=this;this.getnowuser(),this.startPolling();var t=this,n=this.$refs["notifyAudio"];function a(){t.userClick=!0,n&&(n.muted=!1),document.removeEventListener("click",a)}document.addEventListener("click",a),this.$watch("messageNumber",(function(t,a){t-0>a-0&&e.userClick&&n&&n.play()}))},beforeDestroy:function(){this.stopPolling()},created:function(){this.notifyAudio=new Audio(this.notifyMp3),this.getOrderNumber()},methods:{fetchTag:function(){var e=this;return(0,$.A)((0,D.A)().mark((function t(){return(0,D.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:(0,Z.i3)({}).then((function(t){console.log(t),t.data>0&&e.userClick&&e.notifyAudio&&e.notifyAudio.play()})).catch((function(e){})),e.getOrderNumber();case 2:case"end":return t.stop()}}),t)})))()},startPolling:function(){this.intervalId=setInterval(this.fetchTag,12e3)},stopPolling:function(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)},getOrderNumber:function(){var e=this;return(0,$.A)((0,D.A)().mark((function t(){return(0,D.A)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:(0,Z.Zt)({}).then((function(t){e.orderNmber=t.data})).catch((function(t){e.orderNmber=0}));case 1:case"end":return t.stop()}}),t)})))()},getnowuser:function(){var e=this;(0,J.YB)().then((function(t){var n=t.data.list.findIndex((function(e){return e.adminPhone==window.localStorage.getItem("phones")}));setTimeout((function(){e.currentUser={name:t.data.list[n].adminName}}),1500)}))}}},ee=Q,te=(0,S.A)(ee,O,U,!1,null,null,null),ne=te.exports,ae=function(){var e=this,t=e._self._c;return t("global-footer",{staticClass:"footer custom-render",scopedSlots:e._u([{key:"links",fn:function(){},proxy:!0},{key:"copyright",fn:function(){},proxy:!0}])})},ie=[],se={name:"ProGlobalFooter",components:{GlobalFooter:q.Tn}},oe=se,re=(0,S.A)(oe,ae,ie,!1,null,null,null),ce=re.exports,de="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",le={props:{isMobile:Boolean},mounted:function(){},methods:{load:function(){if(de){var e=document.createElement("script");e.id="_adsbygoogle_js",e.src=de,this.$el.appendChild(e),setTimeout((function(){(window.adsbygoogle||[]).push({})}),2e3)}}},render:function(){}},ue=le,me=(0,S.A)(ue,a,i,!1,null,"a032cdc2",null),fe=me.exports,pe={name:"BasicLayout",components:{SettingDrawer:q.G5,RightContent:ne,GlobalFooter:ce,Ads:fe},data:function(){return{isProPreviewSite:!0,isDev:!0,menus:[],collapsed:!1,title:F.A.title,settings:{layout:F.A.layout,contentWidth:"sidemenu"===F.A.layout?_.OT.Fluid:F.A.contentWidth,theme:F.A.navTheme,primaryColor:F.A.primaryColor,fixedHeader:F.A.fixedHeader,fixSiderbar:F.A.fixSiderbar,colorWeak:F.A.colorWeak,hideHintAlert:!0,hideCopyButton:!1},query:{},isMobile:!1}},computed:(0,p.A)({},(0,h.aH)({mainMenu:function(e){return e.permission.addRouters}})),created:function(){var e=this,t=this.mainMenu.find((function(e){return"/"===e.path}));this.menus=t&&t.children||[],this.$watch("collapsed",(function(){e.$store.commit(_.cf,e.collapsed)})),this.$watch("isMobile",(function(){e.$store.commit(_.nd,e.isMobile)}))},mounted:function(){var e=this,t=navigator.userAgent;t.indexOf("Edge")>-1&&this.$nextTick((function(){e.collapsed=!e.collapsed,setTimeout((function(){e.collapsed=!e.collapsed}),16)})),console.log("production"),(0,q.V_)(this.settings.primaryColor)},methods:{i18nRender:f.vb,getreload:function(){this.$router.go(0)},handleMediaQuery:function(e){this.query=e,!this.isMobile||e["screen-xs"]?!this.isMobile&&e["screen-xs"]&&(this.isMobile=!0,this.collapsed=!1,this.settings.contentWidth=_.OT.Fluid):this.isMobile=!1},handleCollapse:function(e){this.collapsed=e},handleSettingChange:function(e){var t=e.type,n=e.value;switch(console.log("type",t,n),t&&(this.settings[t]=n),t){case"contentWidth":this.settings[t]=n;break;case"layout":"sidemenu"===n?this.settings.contentWidth=_.OT.Fluid:(this.settings.fixSiderbar=!1,this.settings.contentWidth=_.OT.Fixed);break}}}},he=pe,ge=(0,S.A)(he,P,M,!1,null,null,null),be=ge.exports,ye={name:"RouteView",props:{keepAlive:{type:Boolean,default:!0}},data:function(){return{}},render:function(){var e=arguments[0],t=this.$route.meta,n=this.$store.getters,a=e("keep-alive",[e("router-view")]),i=e("router-view");return(n.multiTab||t.keepAlive)&&(this.keepAlive||n.multiTab||t.keepAlive)?a:i}},ke=ye,ve=(0,S.A)(ke,s,o,!1,null,null,null),Ae=ve.exports,Ce=function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("router-view")],1)},we=[],je={name:"PageView"},Se=je,Le=(0,S.A)(Se,Ce,we,!1,null,null,null);Le.exports},42634:function(){},45958:function(e,t,n){"use strict";n.r(t);var a=n(76338),i=n(95692),s=n(52648),o=n.n(s),r=n(39668),c=n(14868),d=n(91363),l=n(11016),u=n(91771),m=n(89065),f=n(11250),p=n(77844),h={antLocale:i.A,momentName:"zh-cn",momentLocale:o()};t["default"]=(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({message:"-","layouts.usermenu.dialog.title":"信息","layouts.usermenu.dialog.content":"您确定要注销吗？","layouts.userLayout.title":"Stock Admin 是西湖区最具影响力的 Web 设计规范"},h),r["default"]),c["default"]),d["default"]),l["default"]),u["default"]),m["default"]),f["default"]),p["default"])},51580:function(e,t,n){"use strict";e.exports=n.p+"media/tksq.6d6b120f.mp3"},54142:function(e,t,n){"use strict";n.r(t),t["default"]={"account.settings.menuMap.basic":"基本设置","account.settings.menuMap.security":"安全设置","account.settings.menuMap.custom":"个性化","account.settings.menuMap.binding":"账号绑定","account.settings.menuMap.notification":"新消息通知","account.settings.basic.avatar":"头像","account.settings.basic.change-avatar":"更换头像","account.settings.basic.email":"邮箱","account.settings.basic.email-message":"请输入您的邮箱!","account.settings.basic.nickname":"昵称","account.settings.basic.nickname-message":"请输入您的昵称!","account.settings.basic.profile":"个人简介","account.settings.basic.profile-message":"请输入个人简介!","account.settings.basic.profile-placeholder":"个人简介","account.settings.basic.country":"国家/地区","account.settings.basic.country-message":"请输入您的国家或地区!","account.settings.basic.geographic":"所在省市","account.settings.basic.geographic-message":"请输入您的所在省市!","account.settings.basic.address":"街道地址","account.settings.basic.address-message":"请输入您的街道地址!","account.settings.basic.phone":"联系电话","account.settings.basic.phone-message":"请输入您的联系电话!","account.settings.basic.update":"更新基本信息","account.settings.basic.update.success":"更新基本信息成功","account.settings.security.strong":"强","account.settings.security.medium":"中","account.settings.security.weak":"弱","account.settings.security.password":"账户密码","account.settings.security.password-description":"当前密码强度：","account.settings.security.phone":"密保手机","account.settings.security.phone-description":"已绑定手机：","account.settings.security.question":"密保问题","account.settings.security.question-description":"未设置密保问题，密保问题可有效保护账户安全","account.settings.security.email":"备用邮箱","account.settings.security.email-description":"已绑定邮箱：","account.settings.security.mfa":"MFA 设备","account.settings.security.mfa-description":"未绑定 MFA 设备，绑定后，可以进行二次确认","account.settings.security.modify":"修改","account.settings.security.set":"设置","account.settings.security.bind":"绑定","account.settings.binding.taobao":"绑定淘宝","account.settings.binding.taobao-description":"当前未绑定淘宝账号","account.settings.binding.alipay":"绑定支付宝","account.settings.binding.alipay-description":"当前未绑定支付宝账号","account.settings.binding.dingding":"绑定钉钉","account.settings.binding.dingding-description":"当前未绑定钉钉账号","account.settings.binding.bind":"绑定","account.settings.notification.password":"账户密码","account.settings.notification.password-description":"其他用户的消息将以站内信的形式通知","account.settings.notification.messages":"系统消息","account.settings.notification.messages-description":"系统消息将以站内信的形式通知","account.settings.notification.todo":"待办任务","account.settings.notification.todo-description":"待办任务将以站内信的形式通知","account.settings.settings.open":"开","account.settings.settings.close":"关"}},54577:function(e,t,n){"use strict";n.r(t);var a=n(9661),i=n.n(a),s=n(65728),o=function(){return(0,s.cL)([{key:"key-01",title:"研发中心",icon:"mail",children:[{key:"key-01-01",title:"后端组",icon:null,group:!0,children:[{key:"key-01-01-01",title:"JAVA",icon:null},{key:"key-01-01-02",title:"PHP",icon:null},{key:"key-01-01-03",title:"Golang",icon:null}]},{key:"key-01-02",title:"前端组",icon:null,group:!0,children:[{key:"key-01-02-01",title:"React",icon:null},{key:"key-01-02-02",title:"Vue",icon:null},{key:"key-01-02-03",title:"Angular",icon:null}]}]},{key:"key-02",title:"财务部",icon:"dollar",children:[{key:"key-02-01",title:"会计核算",icon:null},{key:"key-02-02",title:"成本控制",icon:null},{key:"key-02-03",title:"内部控制",icon:null,children:[{key:"key-02-03-01",title:"财务制度建设",icon:null},{key:"key-02-03-02",title:"会计核算",icon:null}]}]}])},r=function(){return(0,s.cL)({data:[{id:"admin",name:"管理员",describe:"拥有所有权限",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"admin",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["delete","edit"],dataAccess:null},{roleId:"admin",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["query","get","edit","delete"],dataAccess:null},{roleId:"admin",permissionId:"menu",permissionName:"菜单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","import"],dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["query","add","get"],dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["add","get","edit","delete"],dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:["add","get"],dataAccess:null}]},{id:"svip",name:"SVIP",describe:"超级会员",status:1,creatorId:"system",createTime:1532417744846,deleted:0,permissions:[{roleId:"admin",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:["add","get","delete"],dataAccess:null},{roleId:"admin",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["add","query","get"],dataAccess:null},{roleId:"admin",permissionId:"menu",permissionName:"菜单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["add","get"],dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","query"],dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add","get","edit"],dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:["add","edit"],dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"edit",describe:"修改",defaultCheck:!1}],actionList:["add"],dataAccess:null}]},{id:"user",name:"普通会员",describe:"普通用户，只能查询",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"user",permissionId:"comment",permissionName:"评论管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:["query"],dataAccess:null},{roleId:"user",permissionId:"marketing",permissionName:"营销管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"member",permissionName:"会员管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"user",permissionId:"menu",permissionName:"菜单管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"}]',actionEntitySet:[{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"user",permissionId:"permission",permissionName:"权限管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"role",permissionName:"角色管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"test",permissionName:"测试权限",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null},{roleId:"user",permissionId:"user",permissionName:"用户管理",actions:"[]",actionEntitySet:[],actionList:null,dataAccess:null}]}],pageSize:10,pageNo:0,totalPage:1,totalCount:5})},c=function(){return(0,s.cL)([{id:"marketing",name:"营销管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:null,parents:null,type:null,deleted:0,actions:["add","query","get","edit","delete"]},{id:"member",name:"会员管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"menu",name:"菜单管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","import","get","edit"]},{id:"order",name:"订单管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"permission",name:"权限管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"role",name:"角色管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"test",name:"测试权限",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]},{id:"user",name:"用户管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"export","defaultCheck":false,"describe":"导出"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]}])},d=function(){return(0,s.cL)({data:[{id:"marketing",name:"营销管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:null,parents:null,type:null,deleted:0,actions:["add","query","get","edit","delete"]},{id:"member",name:"会员管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"menu",name:"菜单管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","import","get","edit"]},{id:"order",name:"订单管理",describe:null,status:1,actionData:'[{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","query","get","edit","delete"]},{id:"permission",name:"权限管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"role",name:"角色管理",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"查询"},{"action":"edit","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get","edit","delete"]},{id:"test",name:"测试权限",describe:null,status:1,actionData:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]},{id:"user",name:"用户管理",describe:null,status:1,actionData:'[{"action":"add","describe":"新增","defaultCheck":false},{"action":"get","describe":"查询","defaultCheck":false}]',sptDaTypes:null,optionalFields:"[]",parents:null,type:"default",deleted:0,actions:["add","get"]}],pageSize:10,pageNo:0,totalPage:1,totalCount:5})};i().mock(/\/org\/tree/,"get",o),i().mock(/\/role/,"get",r),i().mock(/\/permission\/no-pager/,"get",c),i().mock(/\/permission/,"get",d)},55499:function(e,t,n){"use strict";n.d(t,{A:function(){return N}});var a,i=n(85471),s=n(95353),o=n(26297),r=(n(26099),n(74053)),c=n.n(r),d=n(75314),l=n(11363),u={state:{sideCollapsed:!1,isMobile:!1,theme:"dark",layout:"",contentWidth:"",fixedHeader:!1,fixedSidebar:!1,autoHideHeader:!1,color:"",weak:!1,multiTab:!0,lang:"zh-CN",_antLocale:{}},mutations:(a={},(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)(a,d.cf,(function(e,t){e.sideCollapsed=t,c().set(d.cf,t)})),d.nd,(function(e,t){e.isMobile=t})),d.RM,(function(e,t){e.theme=t,c().set(d.RM,t)})),d.yG,(function(e,t){e.layout=t,c().set(d.yG,t)})),d.MV,(function(e,t){e.fixedHeader=t,c().set(d.MV,t)})),d.Fb,(function(e,t){e.fixedSidebar=t,c().set(d.Fb,t)})),d.sl,(function(e,t){e.contentWidth=t,c().set(d.sl,t)})),d.Wb,(function(e,t){e.autoHideHeader=t,c().set(d.Wb,t)})),d.Db,(function(e,t){e.color=t,c().set(d.Db,t)})),d.o6,(function(e,t){e.weak=t,c().set(d.o6,t)})),(0,o.A)((0,o.A)(a,d.$C,(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e.lang=t,e._antLocale=n,c().set(d.$C,t)})),d.jc,(function(e,t){c().set(d.jc,t),e.multiTab=t}))),actions:{setLang:function(e,t){var n=e.commit;return new Promise((function(e,a){n(d.$C,t),(0,l.J4)(t).then((function(){e()})).catch((function(e){a(e)}))}))}}},m=u,f=n(76338),p=(n(62062),n(62010),n(26398)),h=n.n(p),g=n(505),b=n(67569);c().addPlugin(h());var y={state:{token:"",name:"",welcome:"",avatar:"",roles:[],info:{}},mutations:{SET_TOKEN:function(e,t){e.token=t},SET_NAME:function(e,t){var n=t.name,a=t.welcome;e.name=n,e.welcome=a},SET_AVATAR:function(e,t){e.avatar=t},SET_ROLES:function(e,t){e.roles=t},SET_INFO:function(e,t){e.info=t}},actions:{Login:function(e,t){var n=e.commit;return new Promise((function(e,a){(0,g.iD)(t).then((function(i){var s=i;0==s.status?(c().set(d.Xh,s.data.token,s.data.expireTime),n("SET_TOKEN",s.data.token),window.localStorage.setItem("phones",t.adminPhone),e()):a(s.msg)})).catch((function(e){a(e)}))}))},GetInfo:function(e){var t=e.commit;return new Promise((function(e,n){(0,g.Vp)().then((function(a){var i=a.result;if(i.role&&i.role.permissions.length>0){var s=(0,f.A)({},i.role);s.permissions=i.role.permissions.map((function(e){var t=(0,f.A)((0,f.A)({},e),{},{actionList:(e.actionEntitySet||{}).map((function(e){return e.action}))});return t})),s.permissionList=s.permissions.map((function(e){return e.permissionId})),i.role=s,t("SET_ROLES",s),t("SET_INFO",i),t("SET_NAME",{name:i.name,welcome:(0,b.dH)()}),t("SET_AVATAR",i.avatar),e(i)}else n(new Error("getInfo: roles must be a non-null array !"))})).catch((function(e){n(e)}))}))},Logout:function(e){var t=e.commit,n=e.state;return new Promise((function(e){(0,g.ri)(n.token).then((function(){t("SET_TOKEN",""),t("SET_ROLES",[]),c().remove(d.Xh),e()})).catch((function(e){console.log("logout fail:",e)})).finally((function(){}))}))}}},k=y,v=(n(28706),n(2008),n(74423),n(21699),n(76063)),A=n(67193),C=n.n(A);function w(e,t){return!0}function j(e,t){var n=e.filter((function(e){return!!w(t.permissionList,e)&&(e.children&&e.children.length&&(e.children=j(e.children,t)),!0)}));return n}var S={state:{routers:v.f,addRouters:[]},mutations:{SET_ROUTERS:function(e,t){e.addRouters=t,e.routers=v.f.concat(t)}},actions:{GenerateRoutes:function(e,t){var n=e.commit;return new Promise((function(e){var a=t.role,i=C()(v.y),s=j(i,a);n("SET_ROUTERS",s),e()}))}}},L=S,x={isMobile:function(e){return e.app.isMobile},lang:function(e){return e.app.lang},theme:function(e){return e.app.theme},color:function(e){return e.app.color},token:function(e){return e.user.token},avatar:function(e){return e.user.avatar},nickname:function(e){return e.user.name},welcome:function(e){return e.user.welcome},roles:function(e){return e.user.roles},userInfo:function(e){return e.user.info},addRouters:function(e){return e.permission.addRouters},multiTab:function(e){return e.app.multiTab}},I=x;i.Ay.use(s.Ay);var N=new s.Ay.Store({modules:{app:m,user:k,permission:L},state:{},mutations:{},actions:{},getters:I})},60366:function(e,t){"use strict";t.A={navTheme:"dark",primaryColor:"#1890ff",layout:"sidemenu",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!1,colorWeak:!1,menu:{locale:!0},title:"Stock-Admin",pwa:!1,iconfontUrl:"",production:!1}},60804:function(e,t,n){"use strict";n.d(t,{$Q:function(){return h},Be:function(){return v},I1:function(){return l},LT:function(){return c},M_:function(){return m},ST:function(){return d},VW:function(){return g},Wc:function(){return r},ZB:function(){return f},Zt:function(){return y},dg:function(){return S},dh:function(){return p},i3:function(){return b},mf:function(){return k},oq:function(){return u},ot:function(){return w},pM:function(){return C},q1:function(){return A},rL:function(){return j}});var a=n(75769),i=n(55373),s=n.n(i),o={rechargelist:"/admin/recharge/list.do",rechargedel:"/admin/recharge/del.do",rechargeupdateState:"/admin/recharge/updateState.do",rechargecreateOrder:"/admin/recharge/createOrder.do",rechargeexport:"/admin/recharge/export.do",rechargeCountRechargeAmount:"/admin/recharge/countRechargeAmount.do",withdrawCountRechargeAmount:"/admin/withdraw/countRechargeAmount.do",withdrawlist:"/admin/withdraw/list.do",withdrawupdateState:"admin/withdraw/updateState.do",withdrawexport:"/admin/withdraw/export.do",cashlist:"/admin/cash/list.do",logtransList:"/admin/log/transList.do",getPendingOrderNum:"/admin/withdraw/getPendingOrderNum.do",getOrderNum:"/admin/withdraw/getRealPendingOrderNum.do",fundList:"/admin/user/fund/list.do",getWithdrawableAmount:"/admin/user/fund/withdrawable.do",updateUserFund:"/admin/user/fund/update.do",fundConsumptionList:"/admin/user/fund/consumption/list.do",fundConsumptionBySource:"/admin/user/fund/consumption/byFundSource.do"};function r(e){return(0,a.Ay)({url:o.rechargelist,method:"post",data:s().stringify(e)})}function c(e){return(0,a.Ay)({url:o.rechargedel,method:"post",data:s().stringify(e)})}function d(e){return(0,a.Ay)({url:o.rechargeupdateState,method:"post",data:s().stringify(e)})}function l(e){return(0,a.Ay)({url:o.rechargecreateOrder,method:"post",data:s().stringify(e)})}function u(e){return(0,a.Ay)({url:o.rechargeexport,method:"post",responseType:"blob",data:s().stringify(e)})}function m(e){return(0,a.Ay)({url:o.rechargeCountRechargeAmount,method:"post",data:s().stringify(e)})}function f(e){return(0,a.Ay)({url:o.withdrawCountRechargeAmount,method:"post",data:s().stringify(e)})}function p(e){return(0,a.Ay)({url:o.withdrawlist,method:"post",data:s().stringify(e)})}function h(e){return(0,a.Ay)({url:o.withdrawupdateState,method:"post",data:s().stringify(e)})}function g(e){return(0,a.Ay)({url:o.withdrawexport,method:"post",responseType:"blob",data:s().stringify(e)})}function b(e){return(0,a.Ay)({url:o.getPendingOrderNum+"?v="+Date.now(),method:"post",data:s().stringify(e)})}function y(e){return(0,a.Ay)({url:o.getOrderNum+"?v="+Date.now(),method:"post",data:s().stringify(e)})}function k(e){return(0,a.Ay)({url:o.cashlist,method:"post",data:s().stringify(e)})}function v(e){return(0,a.Ay)({url:o.logtransList,method:"post",data:s().stringify(e)})}function A(e){return(0,a.Ay)({url:o.fundList,method:"post",data:s().stringify(e)})}function C(e){return(0,a.Ay)({url:o.getWithdrawableAmount,method:"post",data:s().stringify(e)})}function w(e){return(0,a.Ay)({url:o.updateUserFund,method:"post",data:s().stringify(e)})}function j(e){return(0,a.Ay)({url:o.fundConsumptionList,method:"post",data:s().stringify(e)})}function S(e){return(0,a.Ay)({url:o.fundConsumptionBySource,method:"post",data:s().stringify(e)})}},64158:function(e,t,n){"use strict";n.r(t),t["default"]={"result.success.title":"提交成功","result.success.description":"提交结果页用于反馈一系列操作任务的处理结果， 如果仅是简单操作，使用 Message 全局提示反馈即可。 本文字区域可以展示简单的补充说明，如果有类似展示 “单据”的需求，下面这个灰色区域可以呈现比较复杂的内容。","result.success.operate-title":"项目名称","result.success.operate-id":"项目 ID","result.success.principal":"负责人","result.success.operate-time":"生效时间","result.success.step1-title":"创建项目","result.success.step1-operator":"曲丽丽","result.success.step2-title":"部门初审","result.success.step2-operator":"周毛毛","result.success.step2-extra":"催一下","result.success.step3-title":"财务复核","result.success.step4-title":"完成","result.success.btn-return":"返回列表","result.success.btn-project":"查看项目","result.success.btn-print":"打印"}},65728:function(e,t,n){"use strict";n.d(t,{LT:function(){return o},cL:function(){return s},lZ:function(){return r}});var a=n(44735),i=(n(79432),n(27495),n(25440),{message:"",timestamp:0,result:null,code:0}),s=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return i.result=e,void 0!==t&&null!==t&&(i.message=t),void 0!==n&&0!==n&&(i.code=n,i._status=n),null!==s&&"object"===(0,a.A)(s)&&Object.keys(s).length>0&&(i._headers=s),i.timestamp=(new Date).getTime(),i},o=function(e){var t=e.url,n=t.split("?")[1];return n?JSON.parse('{"'+decodeURIComponent(n).replace(/"/g,'\\"').replace(/&/g,'","').replace(/=/g,'":"')+'"}'):{}},r=function(e){return e.body&&JSON.parse(e.body)}},66117:function(e,t,n){"use strict";var a=n(76338),i=(n(26099),n(43898));t.A=function(e){function t(t,n,s){var o=this;if(s=s||{},o&&o._isVue){var r=document.querySelector("body>div[type=dialog]");r||(r=document.createElement("div"),r.setAttribute("type","dialog"),document.body.appendChild(r));var c=function(e,t){if(e instanceof Function){var n=e();n instanceof Promise?n.then((function(e){e&&t()})):n&&t()}else e||t()},d=new e({data:function(){return{visible:!0}},router:o.$router,store:o.$store,mounted:function(){var e=this;this.$on("close",(function(t){e.handleClose()}))},methods:{handleClose:function(){var e=this;c(this.$refs._component.onCancel,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("cancel"),d.$destroy()}))},handleOk:function(){var e=this;c(this.$refs._component.onOK||this.$refs._component.onOk,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("ok"),d.$destroy()}))}},render:function(e){var o=this,r=s&&s.model;r&&delete s.model;var c=Object.assign({},r&&{model:r}||{},{attrs:Object.assign({},(0,a.A)({},s.attrs||s),{visible:this.visible}),on:Object.assign({},(0,a.A)({},s.on||s),{ok:function(){o.handleOk()},cancel:function(){o.handleClose()}})}),d=n&&n.model;d&&delete n.model;var l=Object.assign({},d&&{model:d}||{},{ref:"_component",attrs:Object.assign({},(0,a.A)({},n&&n.attrs||n)),on:Object.assign({},(0,a.A)({},n&&n.on||n))});return e(i.A,c,[e(t,l)])}}).$mount(r)}}Object.defineProperty(e.prototype,"$dialog",{get:function(){return function(){t.apply(this,arguments)}}})}},67569:function(e,t,n){"use strict";n.d(t,{Av:function(){return o},Z$:function(){return a},dH:function(){return i},lT:function(){return s}});n(27495);function a(){var e=new Date,t=e.getHours();return t<9?"早上好":t<=11?"上午好":t<=13?"中午好":t<20?"下午好":"晚上好"}function i(){var e=["休息一会儿吧","准备吃什么呢?","要不要打一把 DOTA","我猜你可能累了"],t=Math.floor(Math.random()*e.length);return e[t]}function s(){var e=window.navigator.userAgent,t=function(t){return e.indexOf(t)>=0},n=function(){return"ActiveXObject"in window}();return t("MSIE")||n}function o(e){var t=0;if(!e)return t;for(var n={},a=0;a<e.length;a++)n[e[a]]=(n[e[a]]||0)+1,t+=5/n[e[a]];var i={digits:/\d/.test(e),lower:/[a-z]/.test(e),upper:/[A-Z]/.test(e),nonWords:/\W/.test(e)},s=0;for(var o in i)s+=!0===i[o]?1:0;return t+=10*(s-1),parseInt(t)}},72021:function(e,t,n){"use strict";n.r(t);n(74423);var a=n(9661),i=n.n(a),s=n(65728),o=["admin","super"],r=["8914de686ab28dc22f30d3d8e107ff6c","21232f297a57a5a743894a0e4a801fc3"],c=function(e){var t=(0,s.lZ)(e);return console.log("mock: body",t),o.includes(t.username)&&r.includes(t.password)?(0,s.cL)({id:i().mock("@guid"),name:i().mock("@name"),username:"admin",password:"",avatar:"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png",status:1,telephone:"",lastLoginIp:"*************",lastLoginTime:1534837621348,creatorId:"admin",createTime:*************,deleted:0,roleId:"admin",lang:"zh-CN",token:"4291d7da9005377ec9aec4a71ea837f"},"",200,{"Custom-Header":i().mock("@guid")}):(0,s.cL)({isLogin:!0},"账户或密码错误",401)},d=function(){return(0,s.cL)({},"[测试接口] 注销成功")},l=function(){return(0,s.cL)({captcha:i().mock("@integer(10000, 99999)")})},u=function(){return(0,s.cL)({stepCode:i().mock("@integer(0, 1)")})};i().mock(/\/auth\/login/,"post",c),i().mock(/\/auth\/logout/,"post",d),i().mock(/\/account\/sms/,"post",l),i().mock(/\/auth\/2step-code/,"post",u)},75314:function(e,t,n){"use strict";n.d(t,{$C:function(){return h},Db:function(){return m},Fb:function(){return d},MV:function(){return c},OT:function(){return g},RM:function(){return o},Wb:function(){return u},Xh:function(){return a},cf:function(){return i},jc:function(){return p},nd:function(){return s},o6:function(){return f},oF:function(){return b},sl:function(){return l},yG:function(){return r}});var a="Access-Token",i="sidebar_type",s="is_mobile",o="nav_theme",r="layout",c="fixed_header",d="fixed_sidebar",l="content_width",u="auto_hide_header",m="color",f="weak",p="multi_tab",h="app_language",g={Fluid:"Fluid",Fixed:"Fixed"},b={LIGHT:"light",DARK:"dark"}},75769:function(e,t,n){"use strict";n.d(t,{He:function(){return h},Ay:function(){return g}});var a=n(55464),i=n(56252),s=(n(26099),n(72505)),o=n.n(s),r=n(55499),c=n(74053),d=n.n(c),l=n(56427),u={vm:{},install:function(e,t){this.installed||(this.installed=!0,t?(e.axios=t,Object.defineProperties(e.prototype,{axios:{get:function(){return t}},$http:{get:function(){return t}}})):console.error("You have to install axios"))}},m=n(75314),f=o().create({baseURL:"/",headers:{"Content-Type":"application/x-www-form-urlencoded"},timeout:6e3}),p=function(e){if(e.response){var t=e.response.data,n=d().get(m.Xh);403===e.response.status&&l.A.error({message:"Forbidden",description:t.msg}),401!==e.response.status||t.result&&t.result.isLogin||(l.A.error({message:"Unauthorized",description:"Authorization verification failed"}),n&&r.A.dispatch("Logout").then((function(){setTimeout((function(){window.location.reload()}),1500)})))}return Promise.reject(e)};f.interceptors.request.use((function(e){var t=d().get(m.Xh);return t&&(e.headers["admintoken"]=t),e}),p),f.interceptors.response.use(function(){var e=(0,i.A)((0,a.A)().mark((function e(t){var n,i,s;return(0,a.A)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:return n=e.sent,i=n.data,i.code,s=i.msg,"請先登錄，無權限訪問admin"==s?(l.A.error({message:"重新登陆",description:"未登录或登录过期，请重新登录"}),r.A.dispatch("Logout").then((function(){setTimeout((function(){window.localStorage.clear(),window.location.reload()}),1500)}))):n.data||l.A.error({message:"网络错误",description:"网络错误，请稍后刷新页面重试！"}),e.abrupt("return",n.data);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());var h={vm:{},install:function(e){e.use(u,f)}},g=f},76052:function(e,t,n){"use strict";n.r(t);var a=n(9661),i=n.n(a),s=n(65728),o=function(e){console.log("options",e);var t={id:"4291d7da9005377ec9aec4a71ea837f",name:"天野远子",username:"admin",password:"",avatar:"/avatar2.jpg",status:1,telephone:"",lastLoginIp:"*************",lastLoginTime:1534837621348,creatorId:"admin",createTime:*************,merchantCode:"TLif2btpzg079h15bk",deleted:0,roleId:"admin",role:{}},n={id:"admin",name:"管理员",describe:"拥有所有权限",status:1,creatorId:"system",createTime:*************,deleted:0,permissions:[{roleId:"admin",permissionId:"dashboard",permissionName:"仪表盘",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"userlist",permissionName:"仪表盘",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"exception",permissionName:"异常页面权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"result",permissionName:"结果权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"profile",permissionName:"详细页权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"table",permissionName:"表格权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"form",permissionName:"表单权限",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"order",permissionName:"订单管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"permission",permissionName:"权限管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"role",permissionName:"角色管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"table",permissionName:"桌子管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"query",describe:"查询",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1}],actionList:null,dataAccess:null},{roleId:"admin",permissionId:"user",permissionName:"用户管理",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:null,dataAccess:null}]};return n.permissions.push({roleId:"admin",permissionId:"support",permissionName:"超级模块",actions:'[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"import","defaultCheck":false,"describe":"导入"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"},{"action":"export","defaultCheck":false,"describe":"导出"}]',actionEntitySet:[{action:"add",describe:"新增",defaultCheck:!1},{action:"import",describe:"导入",defaultCheck:!1},{action:"get",describe:"详情",defaultCheck:!1},{action:"update",describe:"修改",defaultCheck:!1},{action:"delete",describe:"删除",defaultCheck:!1},{action:"export",describe:"导出",defaultCheck:!1}],actionList:null,dataAccess:null}),t.role=n,(0,s.cL)(t)},r=function(e){var t=[{name:"dashboard",parentId:0,id:1,meta:{title:"menu.dashboard",icon:"dashboard",show:!0},component:"RouteView",redirect:"/dashboard/workplace"},{name:"workplace",parentId:1,id:7,meta:{title:"menu.dashboard.monitor",show:!0},component:"Workplace"},{name:"monitor",path:"https://www.baidu.com/",parentId:1,id:3,meta:{title:"menu.dashboard.workplace",target:"_blank",show:!0}},{name:"Analysis",parentId:1,id:2,meta:{title:"menu.dashboard.analysis",show:!0},component:"Analysis",path:"/dashboard/analysis"},{name:"Userlist",parentId:1,id:2,meta:{title:"用户管理",show:!0},component:"userlist",path:"/userlist/index"},{name:"form",parentId:0,id:10,meta:{icon:"form",title:"menu.form"},redirect:"/form/base-form",component:"RouteView"},{name:"basic-form",parentId:10,id:6,meta:{title:"menu.form.basic-form"},component:"BasicForm"},{name:"step-form",parentId:10,id:5,meta:{title:"menu.form.step-form"},component:"StepForm"},{name:"advanced-form",parentId:10,id:4,meta:{title:"menu.form.advanced-form"},component:"AdvanceForm"},{name:"list",parentId:0,id:10010,meta:{icon:"table",title:"menu.list",show:!0},redirect:"/list/table-list",component:"RouteView"},{name:"table-list",parentId:10010,id:10011,path:"/list/table-list/:pageNo([1-9]\\d*)?",meta:{title:"menu.list.table-list",show:!0},component:"TableList"},{name:"basic-list",parentId:10010,id:10012,meta:{title:"menu.list.basic-list",show:!0},component:"StandardList"},{name:"card",parentId:10010,id:10013,meta:{title:"menu.list.card-list",show:!0},component:"CardList"},{name:"search",parentId:10010,id:10014,meta:{title:"menu.list.search-list",show:!0},redirect:"/list/search/article",component:"SearchLayout"},{name:"article",parentId:10014,id:10015,meta:{title:"menu.list.search-list.articles",show:!0},component:"SearchArticles"},{name:"project",parentId:10014,id:10016,meta:{title:"menu.list.search-list.projects",show:!0},component:"SearchProjects"},{name:"application",parentId:10014,id:10017,meta:{title:"menu.list.search-list.applications",show:!0},component:"SearchApplications"},{name:"profile",parentId:0,id:10018,meta:{title:"menu.profile",icon:"profile",show:!0},redirect:"/profile/basic",component:"RouteView"},{name:"basic",parentId:10018,id:10019,meta:{title:"menu.profile.basic",show:!0},component:"ProfileBasic"},{name:"advanced",parentId:10018,id:10020,meta:{title:"menu.profile.advanced",show:!0},component:"ProfileAdvanced"},{name:"result",parentId:0,id:10021,meta:{title:"menu.result",icon:"check-circle-o",show:!0},redirect:"/result/success",component:"PageView"},{name:"success",parentId:10021,id:10022,meta:{title:"menu.result.success",hiddenHeaderContent:!0,show:!0},component:"ResultSuccess"},{name:"fail",parentId:10021,id:10023,meta:{title:"menu.result.fail",hiddenHeaderContent:!0,show:!0},component:"ResultFail"},{name:"exception",parentId:0,id:10024,meta:{title:"menu.exception",icon:"warning",show:!0},redirect:"/exception/403",component:"RouteView"},{name:"403",parentId:10024,id:10025,meta:{title:"menu.exception.not-permission",show:!0},component:"Exception403"},{name:"404",parentId:10024,id:10026,meta:{title:"menu.exception.not-find",show:!0},component:"Exception404"},{name:"500",parentId:10024,id:10027,meta:{title:"menu.exception.server-error",show:!0},component:"Exception500"},{name:"account",parentId:0,id:10028,meta:{title:"menu.account",icon:"user",show:!0},redirect:"/account/center",component:"RouteView"},{name:"center",parentId:10028,id:10029,meta:{title:"menu.account.center",show:!0},component:"AccountCenter"},{name:"settings",parentId:10028,id:10030,meta:{title:"menu.account.settings",hideHeader:!0,hideChildren:!0,show:!0},redirect:"/account/settings/basic",component:"AccountSettings"},{name:"BasicSettings",path:"/account/settings/basic",parentId:10030,id:10031,meta:{title:"account.settings.menuMap.basic",show:!1},component:"BasicSetting"},{name:"SecuritySettings",path:"/account/settings/security",parentId:10030,id:10032,meta:{title:"account.settings.menuMap.security",show:!1},component:"SecuritySettings"},{name:"CustomSettings",path:"/account/settings/custom",parentId:10030,id:10033,meta:{title:"account.settings.menuMap.custom",show:!1},component:"CustomSettings"},{name:"BindingSettings",path:"/account/settings/binding",parentId:10030,id:10034,meta:{title:"account.settings.menuMap.binding",show:!1},component:"BindingSettings"},{name:"NotificationSettings",path:"/account/settings/notification",parentId:10030,id:10034,meta:{title:"account.settings.menuMap.notification",show:!1},component:"NotificationSettings"}],n=(0,s.cL)(t);return console.log("json",n),n};i().mock(/\/api\/user\/info/,"get",o),i().mock(/\/api\/user\/nav/,"get",r)},76063:function(e,t,n){"use strict";n.d(t,{y:function(){return r},f:function(){return c}});n(26099),n(47764),n(62953);var a=n(40584),i=n(98490),s=n.n(i),o={name:"RouteView",render:function(e){return e("router-view")}},r=[{path:"/",name:"index",component:a.$G,meta:{title:"menu.home"},redirect:"/dashboard/workplace",children:[{path:"/dashboard",name:"dashboard",redirect:"/dashboard/workplace",component:o,meta:{title:"menu.dashboard",keepAlive:!0,icon:s(),permission:["dashboard"]},children:[{path:"/dashboard/workplace",name:"Workplace",component:function(){return n.e(1924).then(n.bind(n,91924))},meta:{title:"menu.dashboard.workplace",keepAlive:!0,permission:["dashboard"]}}]},{path:"/userlist",redirect:"/userlist/index",component:o,meta:{title:"用户管理",icon:"usergroup-delete",permission:["userlist"]},children:[{path:"/userlist/index",name:"Userlist",component:function(){return n.e(2484).then(n.bind(n,22484))},meta:{title:"用户列表",keepAlive:!0,permission:["userlist"]}},{path:"/userlist/agentlist",name:"Agentlist",component:function(){return n.e(1841).then(n.bind(n,81841))},meta:{title:"代理列表",keepAlive:!0,permission:["agentlist"]}}]},{path:"/product",redirect:"/product/shares",component:o,meta:{title:"产品管理",icon:"area-chart",permission:["shares"]},children:[{path:"/product/shares",name:"shares",component:function(){return n.e(1573).then(n.bind(n,31573))},meta:{title:"股票产品",keepAlive:!0,permission:["shares"]}},{path:"/product/index",name:"index",component:function(){return n.e(2824).then(n.bind(n,22824))},meta:{title:"指数产品",keepAlive:!0,permission:["index"]}}]},{path:"/position",redirect:"/position/financing",component:o,meta:{title:"持仓管理",icon:"money-collect",permission:["financing"]},children:[{path:"/position/financing",name:"financing",component:function(){return n.e(8423).then(n.bind(n,28423))},meta:{title:"持仓管理",keepAlive:!0,permission:["financing"]}},{path:"/position/createfinancing",name:"createfinancing",component:function(){return n.e(261).then(n.bind(n,40261))},meta:{title:"创建股票持仓",keepAlive:!0,permission:["createfinancing"]}}]},{path:"/newshares",redirect:"/newshares/newshareslist",component:o,meta:{title:"新股管理",icon:"sliders",permission:["newshareslist"]},children:[{path:"/newshares/newshareslist",name:"newshareslist",component:function(){return n.e(1966).then(n.bind(n,91966))},meta:{title:"新股列表",keepAlive:!0,permission:["newshareslist"]}},{path:"/newshares/newsharesrecord",name:"newsharesrecord",component:function(){return n.e(753).then(n.bind(n,40753))},meta:{title:"新股申购记录",keepAlive:!0,permission:["newsharesrecord"]}},{path:"/newshares/dazonglist",name:"dazonglist",component:function(){return n.e(8029).then(n.bind(n,68029))},meta:{title:"大宗交易列表",keepAlive:!0,permission:["dazonglist"]}}]},{path:"/capital",redirect:"/capital/rechargelist",component:o,meta:{title:"资金管理",icon:"dollar",permission:["rechargelist"]},children:[{path:"/capital/rechargelist",name:"rechargelist",component:function(){return n.e(790).then(n.bind(n,60790))},meta:{title:"充值列表",keepAlive:!0,permission:["rechargelist"]}},{path:"/capital/withdrawallist",name:"withdrawallist",component:function(){return n.e(2843).then(n.bind(n,42843))},meta:{title:"提现列表",keepAlive:!0,permission:["withdrawallist"]}},{path:"/capital/fundrecords",name:"fundrecords",component:function(){return n.e(7456).then(n.bind(n,77456))},meta:{title:"资金记录",keepAlive:!0,permission:["fundrecords"]}},{path:"/capital/fundtransferrecord",name:"fundtransferrecord",component:function(){return n.e(7870).then(n.bind(n,37870))},meta:{title:"资金互转记录",keepAlive:!0,permission:["fundtransferrecord"]}},{path:"/capital/fundsSource",name:"fundsSource",component:function(){return n.e(8741).then(n.bind(n,38741))},meta:{title:"可提现资金来源",keepAlive:!0,permission:["fundsSource"]}},{path:"/capital/fundConsumption",name:"fundConsumption",component:function(){return n.e(9989).then(n.bind(n,39989))},meta:{title:"资金消费记录",keepAlive:!0,permission:["fundConsumption"]}}]},{path:"/logmanage",redirect:"/logmanage/loginlog",component:o,meta:{title:"日志管理",icon:"solution",permission:["loginlog"]},children:[{path:"/logmanage/loginlog",name:"loginlog",component:function(){return n.e(8143).then(n.bind(n,48143))},meta:{title:"登录日志",keepAlive:!0,permission:["loginlog"]}},{path:"/logmanage/smslog",name:"smslog",component:function(){return n.e(313).then(n.bind(n,313))},meta:{title:"短信日志",keepAlive:!0,permission:["smslog"]}},{path:"/logmanage/scheduledtasks",name:"scheduledtasks",component:function(){return n.e(9242).then(n.bind(n,19242))},meta:{title:"定时任务",keepAlive:!0,permission:["scheduledtasks"]}},{path:"/logmanage/stationmessage",name:"stationmessage",component:function(){return n.e(8858).then(n.bind(n,38858))},meta:{title:"站内消息",keepAlive:!0,permission:["stationmessage"]}}]},{path:"/managesettings",redirect:"/managesettings/managelist",component:o,meta:{title:"管理设置",icon:"control",permission:["managelist"]},children:[{path:"/managesettings/managelist",name:"managelist",component:function(){return n.e(7287).then(n.bind(n,57287))},meta:{title:"管理列表",keepAlive:!0,permission:["managelist"]}}]},{path:"/whitelist",redirect:"/whitelist/whitelist",component:o,meta:{title:"白名单设置",icon:"control",permission:["whitelist"]},children:[{path:"/whitelist/whitelist",name:"whitelist",component:function(){return n.e(1581).then(n.bind(n,81581))},meta:{title:"白名单设置",keepAlive:!0,permission:["whitelist"]}}]},{path:"/risksetting",redirect:"/risksetting/productsetting",component:o,meta:{title:"风控设置",icon:"warning",permission:["productsetting"]},children:[{path:"/risksetting/productsetting",name:"productsetting",component:function(){return n.e(1251).then(n.bind(n,11251))},meta:{title:"产品配置",keepAlive:!0,permission:["productsetting"]}},{path:"/risksetting/sharessetting",name:"sharessetting",component:function(){return n.e(1190).then(n.bind(n,31190))},meta:{title:"股票风控",keepAlive:!0,permission:["sharessetting"]}},{path:"/risksetting/indexsetting",name:"indexsetting",component:function(){return n.e(700).then(n.bind(n,50700))},meta:{title:"指数风控",keepAlive:!0,permission:["indexsetting"]}},{path:"/risksetting/spreadsetting",name:"spreadsetting",component:function(){return n.e(3478).then(n.bind(n,63478))},meta:{title:"点差设置",keepAlive:!0,permission:["spreadsetting"]}}]},{path:"/allsetting",redirect:"/allsetting/noticesetting",component:o,meta:{title:"系统设置",icon:"setting",permission:["noticesetting"]},children:[{path:"/allsetting/noticesetting",name:"noticesetting",component:function(){return n.e(2617).then(n.bind(n,12617))},meta:{title:"公告设置",keepAlive:!0,permission:["noticesetting"]}},{path:"/allsetting/bannersetting",name:"bannersetting",component:function(){return n.e(9724).then(n.bind(n,59724))},meta:{title:"轮播图设置",keepAlive:!0,permission:["bannersetting"]}},{path:"/allsetting/paysetting",name:"paysetting",component:function(){return n.e(3037).then(n.bind(n,63037))},meta:{title:"支付渠道设置",keepAlive:!0,permission:["paysetting"]}},{path:"/allsetting/platformsetting",name:"platformsetting",component:function(){return n.e(183).then(n.bind(n,10183))},meta:{title:"平台设置",keepAlive:!0,permission:["platformsetting"]}}]},{path:"/account",component:o,redirect:"/account/center",name:"account",meta:{title:"个人中心",icon:"user",keepAlive:!0,permission:["user"]},children:[{path:"/account/settings",name:"settings",component:function(){return n.e(9640).then(n.bind(n,59640))},meta:{title:"menu.account.settings",hideHeader:!0,permission:["user"]},redirect:"/account/settings/basic",hideChildrenInMenu:!0,children:[{path:"/account/settings/basic",name:"BasicSettings",component:function(){return Promise.all([n.e(2623),n.e(8123)]).then(n.bind(n,32480))},meta:{title:"account.settings.menuMap.basic",hidden:!0,permission:["user"]}},{path:"/account/settings/security",name:"SecuritySettings",component:function(){return Promise.all([n.e(2623),n.e(9007)]).then(n.bind(n,57044))},meta:{title:"account.settings.menuMap.security",hidden:!0,keepAlive:!0,permission:["user"]}},{path:"/account/settings/custom",name:"CustomSettings",component:function(){return n.e(4951).then(n.bind(n,24951))},meta:{title:"account.settings.menuMap.custom",hidden:!0,keepAlive:!0,permission:["user"]}},{path:"/account/settings/binding",name:"BindingSettings",component:function(){return n.e(3302).then(n.bind(n,33302))},meta:{title:"account.settings.menuMap.binding",hidden:!0,keepAlive:!0,permission:["user"]}},{path:"/account/settings/notification",name:"NotificationSettings",component:function(){return n.e(9571).then(n.bind(n,29571))},meta:{title:"account.settings.menuMap.notification",hidden:!0,keepAlive:!0,permission:["user"]}}]}]}]},{path:"*",redirect:"/404",hidden:!0}],c=[{path:"/user",component:a.YJ,redirect:"/user/login",hidden:!0,children:[{path:"login",name:"login",component:function(){return n.e(6806).then(n.bind(n,39260))}},{path:"register",name:"register",component:function(){return n.e(6806).then(n.bind(n,71194))}},{path:"register-result",name:"registerResult",component:function(){return n.e(6806).then(n.bind(n,2275))}},{path:"recover",name:"recover",component:void 0}]},{path:"/404",component:function(){return n.e(1143).then(n.bind(n,32319))}}]},77844:function(e,t,n){"use strict";n.r(t);var a=n(76338),i=n(54142);t["default"]=(0,a.A)({},i["default"])},80157:function(e,t,n){"use strict";n.d(t,{$W:function(){return g},Cv:function(){return d},EV:function(){return f},P1:function(){return h},Rr:function(){return m},cD:function(){return L},cG:function(){return c},gs:function(){return v},hS:function(){return k},hj:function(){return C},iA:function(){return r},iE:function(){return b},lu:function(){return p},m:function(){return u},mc:function(){return S},oT:function(){return A},ts:function(){return w},vB:function(){return y},vP:function(){return l},xJ:function(){return j}});var a=n(75769),i=n(55373),s=n.n(i),o={countdata:"/admin/count.do",usermanag:"/admin/user/list.do",useraddSimulatedAccount:"/admin/user/addSimulatedAccount.do",userupdate:"/admin/user/update.do",usergetBank:"/admin/user/getBank.do",userupdateBank:"admin/user/updateBank.do",userupdateAmt:"/admin/user/updateAmt.do",virtualUpdateAmt:"/admin/user/virtualUpdateAmt.do",userauthByAdmin:"/admin/user/authByAdmin.do",userdelete:"/admin/user/delete.do",userexport:"/admin/user/export.do",nextagent:"/admin/agent/list.do",agentupdateAgentAmt:"/admin/agent/updateAgentAmt.do",agentdelAgent:"/admin/agent/delAgent.do",agentupdate:"/admin/agent/update.do",agentadd:"/admin/agent/add.do",stockgetMarket:"/api/stock/getMarket.do",adminsetSiteStyle:"/api/admin/setSiteStyle.do",admingetSiteStyle:"/api/admin/getSiteStyle.do",deleteSignature:"/admin/user/deleteSignature.do",agentTreeList:"/admin/agent/treeList.do"};function r(e){return(0,a.Ay)({url:o.countdata,method:"get",param:e})}function c(e){return(0,a.Ay)({url:o.agentTreeList,method:"get",param:e})}function d(e){return(0,a.Ay)({url:o.usermanag,method:"post",data:s().stringify(e)})}function l(e){return(0,a.Ay)({url:o.nextagent,method:"post",data:s().stringify(e)})}function u(e){return(0,a.Ay)({url:o.useraddSimulatedAccount,method:"post",data:s().stringify(e)})}function m(e){return(0,a.Ay)({url:o.userupdate,method:"post",data:s().stringify(e)})}function f(e){return(0,a.Ay)({url:o.usergetBank,method:"post",data:s().stringify(e)})}function p(e){return(0,a.Ay)({url:o.userupdateBank,method:"post",data:s().stringify(e)})}function h(e){return(0,a.Ay)({url:o.virtualUpdateAmt,method:"post",data:s().stringify(e)})}function g(e){return(0,a.Ay)({url:o.userupdateAmt,method:"post",data:s().stringify(e)})}function b(e){return(0,a.Ay)({url:o.userauthByAdmin,method:"post",data:s().stringify(e)})}function y(e){return(0,a.Ay)({url:o.userdelete,method:"post",data:s().stringify(e)})}function k(e){return(0,a.Ay)({url:o.deleteSignature,method:"post",data:s().stringify(e)})}function v(e){return(0,a.Ay)({url:o.agentupdateAgentAmt,method:"post",data:s().stringify(e)})}function A(e){return(0,a.Ay)({url:o.agentdelAgent,method:"post",data:s().stringify(e)})}function C(e){return(0,a.Ay)({url:o.agentupdate,method:"post",data:s().stringify(e)})}function w(e){return(0,a.Ay)({url:o.agentadd,method:"post",data:s().stringify(e)})}function j(e){return(0,a.Ay)({url:o.stockgetMarket,method:"post",data:s().stringify(e)})}function S(e){return(0,a.Ay)({url:o.adminsetSiteStyle,method:"post",data:s().stringify(e)})}function L(){return(0,a.Ay)({url:o.userexport,method:"post",responseType:"blob"})}},89065:function(e,t,n){"use strict";n.r(t);var a=n(76338),i=n(97178);t["default"]=(0,a.A)({},i["default"])},89776:function(e,t,n){"use strict";n.r(t);var a=n(9661),i=n.n(a),s=n(65728),o=5701,r=function(e){for(var t=(0,s.LT)(e),n=[],a=parseInt(t.pageNo),r=parseInt(t.pageSize),c=Math.ceil(o/r),d=(a-1)*r,l=(a>=c?o%r:r)+1,u=1;u<l;u++){var m=d+u;n.push({key:m,id:m,no:"No "+m,description:"这是一段描述",callNo:i().mock("@integer(1, 999)"),status:i().mock("@integer(0, 3)"),updatedAt:i().mock("@datetime"),editable:!1})}return(0,s.cL)({pageSize:r,pageNo:a,totalCount:o,totalPage:c,data:n})},c=function(){return(0,s.cL)({data:[{id:1,cover:"https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png",title:"Alipay",description:"那是一种内在的东西， 他们到达不了，也无法触及的",status:1,updatedAt:"2018-07-26 00:00:00"},{id:2,cover:"https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png",title:"Angular",description:"希望是一个好东西，也许是最好的，好东西是不会消亡的",status:1,updatedAt:"2018-07-26 00:00:00"},{id:3,cover:"https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png",title:"Stock Admin",description:"城镇中有那么多的酒馆，她却偏偏走进了我的酒馆",status:1,updatedAt:"2018-07-26 00:00:00"},{id:4,cover:"https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png",title:"Stock Admin",description:"那时候我只会想自己想要什么，从不想自己拥有什么",status:1,updatedAt:"2018-07-26 00:00:00"},{id:5,cover:"https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png",title:"Bootstrap",description:"凛冬将至",status:1,updatedAt:"2018-07-26 00:00:00"},{id:6,cover:"https://gw.alipayobjects.com/zos/rmsportal/ComBAopevLwENQdKWiIn.png",title:"Vue",description:"生命就像一盒巧克力，结果往往出人意料",status:1,updatedAt:"2018-07-26 00:00:00"}],pageSize:10,pageNo:0,totalPage:6,totalCount:57})},d=function(){return(0,s.cL)([{id:1,user:{nickname:"@name",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},project:{name:"白鹭酱油开发组",action:"更新",event:"番组计划"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"蓝莓酱",avatar:"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png"},project:{name:"白鹭酱油开发组",action:"更新",event:"番组计划"},time:"2018-08-23 09:35:37"},{id:1,user:{nickname:"@name",avatar:"@image(64x64)"},project:{name:"白鹭酱油开发组",action:"创建",event:"番组计划"},time:"2017-05-27 00:00:00"},{id:1,user:{nickname:"曲丽丽",avatar:"@image(64x64)"},project:{name:"高逼格设计天团",action:"更新",event:"六月迭代"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"@name",avatar:"@image(64x64)"},project:{name:"高逼格设计天团",action:"created",event:"六月迭代"},time:"2018-08-23 14:47:00"},{id:1,user:{nickname:"曲丽丽",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},project:{name:"高逼格设计天团",action:"created",event:"六月迭代"},time:"2018-08-23 14:47:00"}])},l=function(){return(0,s.cL)([{id:1,name:"科学搬砖组",avatar:"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png"},{id:2,name:"程序员日常",avatar:"https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png"},{id:1,name:"设计天团",avatar:"https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png"},{id:1,name:"中二少女团",avatar:"https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png"},{id:1,name:"骗你学计算机",avatar:"https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png"}])},u=function(){return(0,s.cL)([{item:"引用","个人":70,"团队":30,"部门":40},{item:"口碑","个人":60,"团队":70,"部门":40},{item:"产量","个人":50,"团队":60,"部门":40},{item:"贡献","个人":40,"团队":50,"部门":40},{item:"热度","个人":60,"团队":70,"部门":40},{item:"引用","个人":70,"团队":50,"部门":40}])};i().mock(/\/service/,"get",r),i().mock(/\/list\/search\/projects/,"get",c),i().mock(/\/workplace\/activity/,"get",d),i().mock(/\/workplace\/teams/,"get",l),i().mock(/\/workplace\/radar/,"get",u)},90313:function(e,t,n){"use strict";n.r(t);var a=n(9661),i=n.n(a),s=n(65728),o=["Alipay","Angular","Stock Admin","Stock Admin","Bootstrap","React","Vue","Webpack"],r=["https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png","https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png","https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png","https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png","https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png"],c=["https://gw.alipayobjects.com/zos/rmsportal/uMfMFlvUuceEyPpotzlq.png","https://gw.alipayobjects.com/zos/rmsportal/iZBVOIhGJiAnhplqjvZW.png","https://gw.alipayobjects.com/zos/rmsportal/iXjVmWVHbCJAyqvDxdtx.png","https://gw.alipayobjects.com/zos/rmsportal/gLaIAoVWTtLbBWZNYEMg.png"],d=["付小小","吴加好","周星星","林东东","曲丽丽"],l="段落示意：蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。",u="在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。",m="https://ant.design",f=function(e){var t=(0,s.LT)(e);console.log("queryParameters",t),t&&!t.count&&(t.count=5);for(var n=[],a=0;a<t.count;a++){var f=a+1,p=parseInt(5*Math.random(),10);n.push({id:f,avatar:r[p],owner:d[p],content:l,star:i().mock("@integer(1, 999)"),percent:i().mock("@integer(1, 999)"),like:i().mock("@integer(1, 999)"),message:i().mock("@integer(1, 999)"),description:u,href:m,title:o[a%8],updatedAt:i().mock("@datetime"),members:[{avatar:"https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png",name:"曲丽丽",id:"member1"},{avatar:"https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png",name:"王昭君",id:"member2"},{avatar:"https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png",name:"董娜娜",id:"member3"}],activeUser:Math.ceil(1e5*Math.random())+1e5,newUser:Math.ceil(1e3*Math.random())+1e3,cover:parseInt(a/4,10)%2===0?c[a%4]:c[3-a%4]})}return(0,s.cL)(n)};i().mock(/\/list\/article/,"get",f)},91363:function(e,t,n){"use strict";n.r(t),t["default"]={"app.setting.pagestyle":"整体风格设置","app.setting.pagestyle.light":"亮色菜单风格","app.setting.pagestyle.dark":"暗色菜单风格","app.setting.pagestyle.realdark":"暗黑模式","app.setting.themecolor":"主题色","app.setting.navigationmode":"导航模式","app.setting.content-width":"内容区域宽度","app.setting.fixedheader":"固定 Header","app.setting.fixedsidebar":"固定侧边栏","app.setting.sidemenu":"侧边菜单布局","app.setting.topmenu":"顶部菜单布局","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.othersettings":"其他设置","app.setting.weakmode":"色弱模式","app.setting.copy":"拷贝设置","app.setting.loading":"加载主题中","app.setting.copyinfo":"拷贝设置成功 src/config/defaultSettings.js","app.setting.production.hint":"","app.setting.themecolor.daybreak":"拂晓蓝","app.setting.themecolor.dust":"薄暮","app.setting.themecolor.volcano":"火山","app.setting.themecolor.sunset":"日暮","app.setting.themecolor.cyan":"明青","app.setting.themecolor.green":"极光绿","app.setting.themecolor.geekblue":"极客蓝","app.setting.themecolor.purple":"酱紫"}},91771:function(e,t,n){"use strict";n.r(t);var a=n(76338),i=n(22492);t["default"]=(0,a.A)({},i["default"])},97178:function(e,t,n){"use strict";n.r(t),t["default"]={"form.basic-form.basic.title":"基础表单","form.basic-form.basic.description":"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。","form.basic-form.title.label":"标题","form.basic-form.title.placeholder":"给目标起个名字","form.basic-form.title.required":"请输入标题","form.basic-form.date.label":"起止日期","form.basic-form.placeholder.start":"开始日期","form.basic-form.placeholder.end":"结束日期","form.basic-form.date.required":"请选择起止日期","form.basic-form.goal.label":"目标描述","form.basic-form.goal.placeholder":"请输入你的阶段性工作目标","form.basic-form.goal.required":"请输入目标描述","form.basic-form.standard.label":"衡量标准","form.basic-form.standard.placeholder":"请输入衡量标准","form.basic-form.standard.required":"请输入衡量标准","form.basic-form.client.label":"客户","form.basic-form.client.required":"请描述你服务的客户","form.basic-form.label.tooltip":"目标的服务对象","form.basic-form.client.placeholder":"请描述你服务的客户，内部客户直接 @姓名／工号","form.basic-form.invites.label":"邀评人","form.basic-form.invites.placeholder":"请直接 @姓名／工号，最多可邀请 5 人","form.basic-form.weight.label":"权重","form.basic-form.weight.placeholder":"请输入","form.basic-form.public.label":"目标公开","form.basic-form.label.help":"客户、邀评人默认被分享","form.basic-form.radio.public":"公开","form.basic-form.radio.partially-public":"部分公开","form.basic-form.radio.private":"不公开","form.basic-form.publicUsers.placeholder":"公开给","form.basic-form.option.A":"同事一","form.basic-form.option.B":"同事二","form.basic-form.option.C":"同事三","form.basic-form.email.required":"请输入邮箱地址！","form.basic-form.email.wrong-format":"邮箱地址格式错误！","form.basic-form.userName.required":"请输入用户名!","form.basic-form.password.required":"请输入密码！","form.basic-form.password.twice":"两次输入的密码不匹配!","form.basic-form.strength.msg":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","form.basic-form.strength.strong":"强度：强","form.basic-form.strength.medium":"强度：中","form.basic-form.strength.short":"强度：太短","form.basic-form.confirm-password.required":"请确认密码！","form.basic-form.phone-number.required":"请输入手机号！","form.basic-form.phone-number.wrong-format":"手机号格式错误！","form.basic-form.verification-code.required":"请输入验证码！","form.basic-form.form.get-captcha":"获取验证码","form.basic-form.captcha.second":"秒","form.basic-form.form.optional":"（选填）","form.basic-form.form.submit":"提交","form.basic-form.form.save":"保存","form.basic-form.email.placeholder":"邮箱","form.basic-form.password.placeholder":"至少6位密码，区分大小写","form.basic-form.confirm-password.placeholder":"确认密码","form.basic-form.phone-number.placeholder":"手机号","form.basic-form.verification-code.placeholder":"验证码"}},98490:function(e,t,n){var a=n(71332)["default"],i=n(88498)["default"],s=["class","staticClass","style","staticStyle","attrs"];n(28706),e.exports={functional:!0,render:function(e,t){var n=t._c,o=(t._v,t.data),r=t.children,c=void 0===r?[]:r,d=o.class,l=o.staticClass,u=o.style,m=o.staticStyle,f=o.attrs,p=void 0===f?{}:f,h=i(o,s);return n("svg",a({class:["bx-analyse_svg__icon",d,l],style:[u,m],attrs:Object.assign({viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg",width:"200",height:"200"},p)},h),c.concat([n("defs"),n("path",{attrs:{d:"M85.333 512h85.334a340.736 340.736 0 0199.712-241.621 337.493 337.493 0 01108.458-72.96 346.453 346.453 0 01261.547-1.75 106.155 106.155 0 00106.283 102.998c59.136 0 106.666-47.531 106.666-106.667S805.803 85.333 746.667 85.333c-29.398 0-55.979 11.776-75.222 30.934-103.722-41.515-222.848-40.875-325.76 2.517a423.595 423.595 0 00-135.68 91.264A423.253 423.253 0 00118.7 345.685 426.88 426.88 0 0085.333 512zm741.248 133.205c-17.109 40.619-41.685 77.142-72.96 108.416s-67.797 55.851-108.458 72.96a346.453 346.453 0 01-261.547 1.75 106.155 106.155 0 00-106.283-102.998c-59.136 0-106.666 47.531-106.666 106.667s47.53 106.667 106.666 106.667c29.398 0 55.979-11.776 75.222-30.934A425.173 425.173 0 00512 938.667a425.941 425.941 0 00393.259-260.352A426.325 426.325 0 00938.667 512h-85.334a341.035 341.035 0 01-26.752 133.205z"}}),n("path",{attrs:{d:"M512 318.379c-106.752 0-193.621 86.869-193.621 193.621S405.248 705.621 512 705.621 705.621 618.752 705.621 512 618.752 318.379 512 318.379zm0 301.909c-59.69 0-108.288-48.597-108.288-108.288S452.309 403.712 512 403.712 620.288 452.309 620.288 512 571.691 620.288 512 620.288z"}})]))}}},99547:function(e,t,n){"use strict";n.d(t,{w:function(){return s}});var a=n(76338),i=n(95353),s={computed:(0,a.A)({},(0,i.aH)({isMobile:function(e){return e.app.isMobile}}))}}},t={};function n(a){var i=t[a];if(void 0!==i)return i.exports;var s=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(s.exports,s,s.exports,n),s.loaded=!0,s.exports}n.m=e,function(){var e=[];n.O=function(t,a,i,s){if(!a){var o=1/0;for(l=0;l<e.length;l++){a=e[l][0],i=e[l][1],s=e[l][2];for(var r=!0,c=0;c<a.length;c++)(!1&s||o>=s)&&Object.keys(n.O).every((function(e){return n.O[e](a[c])}))?a.splice(c--,1):(r=!1,s<o&&(o=s));if(r){e.splice(l--,1);var d=i();void 0!==d&&(t=d)}}return t}s=s||0;for(var l=e.length;l>0&&e[l-1][2]>s;l--)e[l]=e[l-1];e[l]=[a,i,s]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,a){return n.f[a](e,t),t}),[]))}}(),function(){n.u=function(e){return"js/"+({77:"lang-en-US-result-fail",1143:"fail",1980:"lang-en-US-account",2098:"lang-en-US-account-settings",2418:"lang-en-US-global",4606:"lang-en-US-user",4729:"lang-en-US-setting",5802:"lang-en-US-result-success",5924:"lang-en-US-result",6345:"lang-en-US-dashboard",6806:"user",7254:"lang-en-US-menu",7533:"lang-en-US-form",7644:"lang-zh-CN",8376:"lang-en-US-dashboard-analysis",8438:"lang-en-US-form-basicForm"}[e]||e)+"."+{77:"acbc0401",183:"bb8c5710",261:"cacb7dde",313:"bfead587",700:"95b9c132",753:"451d41f2",790:"5a6c99ee",1143:"1a820d27",1190:"deb3b41d",1251:"30a0016d",1573:"ba01dd25",1581:"9bfc5f6a",1841:"104a3db3",1924:"c87fff96",1966:"39f3d6de",1980:"76f66b89",2098:"7207ce79",2418:"89037c5f",2484:"3f7dafef",2617:"7d655f56",2623:"f7d87d56",2824:"c3ec57c5",2843:"d6cf0e2a",3037:"7b2430d9",3302:"188f9a86",3478:"fde7e9b0",4606:"8faf8f5d",4729:"04b85d31",4951:"634b7d63",5802:"836b5c96",5924:"e2ecb4e7",6345:"79aaf3e7",6806:"225fa05f",7254:"90d92895",7287:"041348f7",7456:"7fa85e2b",7533:"84747a3e",7644:"8031a2d2",7870:"57aa64d2",8029:"9b6a928d",8123:"cc4bb64d",8143:"7f760be5",8376:"9042eabf",8423:"1bd14d2c",8438:"0f0f96a5",8741:"c0a84bc0",8858:"3a2e8c41",9007:"fa7c17d5",9242:"a4e21dfd",9571:"a236b338",9640:"3c594c40",9724:"fed736c4",9989:"4e0cf93f"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"css/"+(6806===e?"user":e)+"."+{183:"e9fadd2f",261:"c9770efe",313:"ccb9e800",700:"069140ca",790:"d12fb53f",1190:"04de2ff5",1251:"75ca6d7c",1924:"499fcc54",2484:"9e3ae93b",2617:"c0a8846d",2824:"f87dbea0",2843:"e7563241",3037:"0ffae39d",3478:"7cb064ce",6806:"b94fe790",7456:"9ad0d7ba",7870:"0f76a824",8123:"839fa1b9",8143:"9b8c5ae3",8423:"b819716f",8741:"d1d32285",8858:"8104910c",9007:"47102b9a",9242:"366e033a",9640:"3b10793d",9724:"410ea25f",9989:"c17d5506"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="vue-antd-pro:";n.l=function(a,i,s,o){if(e[a])e[a].push(i);else{var r,c;if(void 0!==s)for(var d=document.getElementsByTagName("script"),l=0;l<d.length;l++){var u=d[l];if(u.getAttribute("src")==a||u.getAttribute("data-webpack")==t+s){r=u;break}}r||(c=!0,r=document.createElement("script"),r.charset="utf-8",r.timeout=120,n.nc&&r.setAttribute("nonce",n.nc),r.setAttribute("data-webpack",t+s),r.src=a),e[a]=[i];var m=function(t,n){r.onerror=r.onload=null,clearTimeout(f);var i=e[a];if(delete e[a],r.parentNode&&r.parentNode.removeChild(r),i&&i.forEach((function(e){return e(n)})),t)return t(n)},f=setTimeout(m.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=m.bind(null,r.onerror),r.onload=m.bind(null,r.onload),c&&document.head.appendChild(r)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){n.p=""}(),function(){if("undefined"!==typeof document){var e=function(e,t,a,i,s){var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",n.nc&&(o.nonce=n.nc);var r=function(n){if(o.onerror=o.onload=null,"load"===n.type)i();else{var a=n&&n.type,r=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+r+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=a,c.request=r,o.parentNode&&o.parentNode.removeChild(o),s(c)}};return o.onerror=o.onload=r,o.href=t,a?a.parentNode.insertBefore(o,a.nextSibling):document.head.appendChild(o),o},t=function(e,t){for(var n=document.getElementsByTagName("link"),a=0;a<n.length;a++){var i=n[a],s=i.getAttribute("data-href")||i.getAttribute("href");if("stylesheet"===i.rel&&(s===e||s===t))return i}var o=document.getElementsByTagName("style");for(a=0;a<o.length;a++){i=o[a],s=i.getAttribute("data-href");if(s===e||s===t)return i}},a=function(a){return new Promise((function(i,s){var o=n.miniCssF(a),r=n.p+o;if(t(o,r))return i();e(a,r,null,i,s)}))},i={3524:0};n.f.miniCss=function(e,t){var n={183:1,261:1,313:1,700:1,790:1,1190:1,1251:1,1924:1,2484:1,2617:1,2824:1,2843:1,3037:1,3478:1,6806:1,7456:1,7870:1,8123:1,8143:1,8423:1,8741:1,8858:1,9007:1,9242:1,9640:1,9724:1,9989:1};i[e]?t.push(i[e]):0!==i[e]&&n[e]&&t.push(i[e]=a(e).then((function(){i[e]=0}),(function(t){throw delete i[e],t})))}}}(),function(){var e={3524:0};n.f.j=function(t,a){var i=n.o(e,t)?e[t]:void 0;if(0!==i)if(i)a.push(i[2]);else{var s=new Promise((function(n,a){i=e[t]=[n,a]}));a.push(i[2]=s);var o=n.p+n.u(t),r=new Error,c=function(a){if(n.o(e,t)&&(i=e[t],0!==i&&(e[t]=void 0),i)){var s=a&&("load"===a.type?"missing":a.type),o=a&&a.target&&a.target.src;r.message="Loading chunk "+t+" failed.\n("+s+": "+o+")",r.name="ChunkLoadError",r.type=s,r.request=o,i[1](r)}};n.l(o,c,"chunk-"+t,t)}},n.O.j=function(t){return 0===e[t]};var t=function(t,a){var i,s,o=a[0],r=a[1],c=a[2],d=0;if(o.some((function(t){return 0!==e[t]}))){for(i in r)n.o(r,i)&&(n.m[i]=r[i]);if(c)var l=c(n)}for(t&&t(a);d<o.length;d++)s=o[d],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(l)},a=self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=n.O(void 0,[504],(function(){return n(21508)}));a=n.O(a)})();