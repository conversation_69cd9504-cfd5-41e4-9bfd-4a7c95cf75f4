"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[8741],{38741:function(e,t,a){a.r(t),a.d(t,{default:function(){return m}});var n=function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("a-card",{attrs:{bordered:!1}},[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"table-page-search-wrapper"},[t("a-form",{attrs:{layout:"inline"}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"用户Id"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"真实姓名"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入真实姓名"},model:{value:e.queryParam.realName,callback:function(t){e.$set(e.queryParam,"realName",t)},expression:"queryParam.realName"}})],1)],1),t("a-col",{attrs:{md:12,lg:6,sm:24}},[t("a-form-item",{attrs:{label:"手机号"}},[t("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入手机号"},model:{value:e.queryParam.phone,callback:function(t){e.$set(e.queryParam,"phone",t)},expression:"queryParam.phone"}})],1)],1),t("a-col",{attrs:{md:12,lg:8,sm:24}},[t("a-form-item",[t("span",{staticClass:"table-page-search-submitButtons"},[t("a-button",{attrs:{icon:"redo"},on:{click:e.resetQueryParam}},[e._v("重置")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(t){e.queryParam.pageNum=1,e.getList()}}},[e._v("查询")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(t){return e.openUpdateFundDialog()}}},[e._v("修改用户可提现金额")])],1)])],1)],1)],1)],1)]),t("div",{staticStyle:{"margin-bottom":"10px"}},[e._v("总资金："+e._s(e.withdrawableObj.totalAmount)+"，不可提现金额："+e._s(e.withdrawableObj.nonWithdrawableAmount)+"，可提现金额："+e._s(e.withdrawableObj.withdrawableAmount))]),t("a-table",{attrs:{bordered:"",loading:e.loading,pagination:e.pagination,columns:e.columns,"data-source":e.datalist,rowKey:"id"},scopedSlots:e._u([{key:"isUsed",fn:function(a,n){return t("span",{},[[t("a-tag",{attrs:{color:0==n.isUsed?"red":"green"}},[e._v(e._s(0==n.isUsed?"未全部使用":"已全部使用"))])]],2)}},{key:"sourceType",fn:function(a,n){return t("span",{},[["RECHARGE"==n.sourceType?t("div",[e._v("充值")]):e._e(),"POSITION_SALE"==n.sourceType?t("div",[e._v("卖出股票")]):e._e(),"WITHDRAW_REJECT"==n.sourceType?t("div",[e._v("提现驳回返还")]):e._e(),"WITHDRAW_CANCEL"==n.sourceType?t("div",[e._v("提现取消返还")]):e._e(),"ADMIN_OPERATION"==n.sourceType?t("div",[e._v("管理员操作")]):e._e(),"ADMIN_DEPOSIT"==n.sourceType?t("div",[e._v("人工上分")]):e._e(),"ADMIN_WITHDRAW"==n.sourceType?t("div",[e._v("人工下分")]):e._e()]],2)}},{key:"action",fn:function(a,n){return[t("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.viewConsumptionRecords(n)}}},[e._v("查看")])]}}])}),t("a-modal",{attrs:{title:"修改用户可提现金额",width:640,visible:e.updateFundDialog,confirmLoading:e.updateFundDialogLoading,maskClosable:!1},on:{ok:e.submitUpdateFund,cancel:e.cancelUpdateFund}},[t("a-form",{ref:"updateFundForm",attrs:{form:e.updateFundForm}},[t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:24,lg:24,sm:24}},[t("a-form-item",{attrs:{label:"用户ID",labelCol:{span:6},wrapperCol:{span:18}}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["userId",{rules:[{required:!0,message:"请输入用户ID"}]}],expression:"['userId', { rules: [{ required: true, message: '请输入用户ID' }] }]"}],staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户ID"}})],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:24,lg:24,sm:24}},[t("a-form-item",{attrs:{label:"操作类型",labelCol:{span:6},wrapperCol:{span:18}}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["operationType",{rules:[{required:!0,message:"请选择操作类型"}]}],expression:"['operationType', { rules: [{ required: true, message: '请选择操作类型' }] }]"}],attrs:{placeholder:"请选择操作类型"}},[t("a-select-option",{attrs:{value:1}},[e._v("增加可提现金额")]),t("a-select-option",{attrs:{value:2}},[e._v("减少可提现金额")])],1)],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:24,lg:24,sm:24}},[t("a-form-item",{attrs:{label:"操作金额",labelCol:{span:6},wrapperCol:{span:18}}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["amount",{rules:[{required:!0,message:"请输入操作金额"}]}],expression:"['amount', { rules: [{ required: true, message: '请输入操作金额' }] }]"}],staticStyle:{width:"100%"},attrs:{placeholder:"请输入操作金额",min:.01,precision:2}})],1)],1)],1),t("a-row",{attrs:{gutter:48}},[t("a-col",{attrs:{md:24,lg:24,sm:24}},[t("a-form-item",{attrs:{label:"备注信息",labelCol:{span:6},wrapperCol:{span:18}}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["remark"],expression:"['remark']"}],attrs:{placeholder:"请输入备注信息（可选）",rows:3}})],1)],1)],1)],1)],1),t("a-modal",{attrs:{title:"资金消费记录",width:1200,visible:e.consumptionDialog,maskClosable:!1,footer:null},on:{cancel:e.closeConsumptionDialog}},[t("a-table",{attrs:{bordered:"",loading:e.consumptionLoading,columns:e.consumptionColumns,"data-source":e.consumptionList,rowKey:"id",pagination:!1},scopedSlots:e._u([{key:"consumptionType",fn:function(a,n){return t("span",{},[["POSITION_BUY"===n.consumptionType?t("div",[e._v("购买股票")]):"WITHDRAW"===n.consumptionType?t("div",[e._v("提现")]):"OTHER"===n.consumptionType?t("div",[e._v("其他")]):t("div",[e._v(e._s(n.consumptionType))])]],2)}},{key:"sourceType",fn:function(a,n){return t("span",{},[["RECHARGE"==n.sourceType?t("div",[e._v("充值")]):e._e(),"POSITION_SALE"==n.sourceType?t("div",[e._v("卖出股票")]):e._e(),"WITHDRAW_REJECT"==n.sourceType?t("div",[e._v("提现驳回返还")]):e._e(),"WITHDRAW_CANCEL"==n.sourceType?t("div",[e._v("提现取消返还")]):e._e(),"ADMIN_OPERATION"==n.sourceType?t("div",[e._v("管理员操作")]):e._e(),"ADMIN_DEPOSIT"==n.sourceType?t("div",[e._v("人工上分")]):e._e(),"ADMIN_WITHDRAW"==n.sourceType?t("div",[e._v("人工下分")]):e._e()]],2)}},{key:"consumptionAmount",fn:function(a){return t("span",{},[[t("span",{staticStyle:{color:"#f5222d"}},[e._v(e._s(a))])]],2)}}])})],1)],1)],1)},r=[],o=(a(28706),a(60804)),i=a(95093),s=a.n(i),u={name:"FundTransferrecord",data:function(){var e=this;return{columns:[{title:"用户名称（ID）",dataIndex:"realName",align:"center",width:180,customRender:function(e,t,a){return"".concat(t.realName,"（").concat(t.userId,"）")}},{title:"资金来源类型",dataIndex:"sourceType",align:"center",scopedSlots:{customRender:"sourceType"}},{title:"原始金额",dataIndex:"amount",align:"center"},{title:"剩余金额",dataIndex:"remainingAmount",align:"center"},{title:"关联单号",dataIndex:"relatedOrderSn",align:"center"},{title:"全部使用",dataIndex:"isUsed",align:"center",scopedSlots:{customRender:"isUsed"}},{title:"备注",dataIndex:"remark",align:"center"},{title:"创建时间",dataIndex:"createTime",align:"center",width:180,customRender:function(e,t,a){return e?s()(e).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",width:100,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(t,a){return e.onSizeChange(t,a)},onChange:function(t,a){return e.onPageChange(t,a)},showTotal:function(e){return"共有 ".concat(e," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,userId:"",realName:"",phone:""},datalist:[],withdrawableObj:{},updateFundDialog:!1,updateFundDialogLoading:!1,updateFundForm:this.$form.createForm(this),consumptionDialog:!1,consumptionLoading:!1,consumptionColumns:[{title:"消费类型",dataIndex:"consumptionType",align:"center",scopedSlots:{customRender:"consumptionType"}},{title:"资金来源类型",dataIndex:"sourceType",align:"center",scopedSlots:{customRender:"sourceType"}},{title:"消费金额",dataIndex:"consumptionAmount",align:"center",scopedSlots:{customRender:"consumptionAmount"}},{title:"备注",dataIndex:"remark",align:"center"},{title:"创建时间",dataIndex:"createTime",align:"center",width:180,customRender:function(e,t,a){return e?s()(e).format("YYYY-MM-DD HH:mm:ss"):""}}],consumptionList:[]}},created:function(){this.getList()},methods:{getWithdrawableAmountData:function(e){var t=this;(0,o.pM)(this.queryParam).then((function(a){t.withdrawableObj=a.data,e()}))},resetQueryParam:function(){this.queryParam={pageNum:1,pageSize:10,userId:"",realName:"",phone:""}},getList:function(){var e=this,t=this;this.loading=!0,this.getWithdrawableAmountData((function(){(0,o.q1)(e.queryParam).then((function(a){e.datalist=a.data.list,e.pagination.total=a.data.total,t.loading=!1}))}))},onPageChange:function(e,t){this.queryParam.pageNum=e,this.pagination.pageSize=t,this.getList()},onSizeChange:function(e,t){this.queryParam.pageNum=e,this.queryParam.pageSize=t,this.pagination.pageSize=t,this.getList()},openUpdateFundDialog:function(){this.updateFundDialog=!0},submitUpdateFund:function(e){var t=this;e.preventDefault(),this.updateFundForm.validateFields((function(e,a){e||(t.updateFundDialogLoading=!0,(0,o.ot)(a).then((function(e){0===e.status?(t.$message.success({content:e.msg,duration:2}),t.updateFundDialog=!1,t.updateFundForm.resetFields(),t.getList()):t.$message.error({content:e.msg}),t.updateFundDialogLoading=!1})).catch((function(e){t.$message.error({content:"修改用户可提现金额失败"}),t.updateFundDialogLoading=!1})))}))},cancelUpdateFund:function(){this.updateFundDialog=!1,this.updateFundForm.resetFields()},viewConsumptionRecords:function(e){var t=this;this.consumptionDialog=!0,this.consumptionLoading=!0,(0,o.dg)({fundSourceId:e.id}).then((function(e){t.consumptionList=e.data||[],t.consumptionLoading=!1})).catch((function(e){t.$message.error({content:"获取消费记录失败"}),t.consumptionLoading=!1}))},closeConsumptionDialog:function(){this.consumptionDialog=!1,this.consumptionLoading=!1}}},d=u,l=a(81656),c=(0,l.A)(d,n,r,!1,null,"12773d66",null),m=c.exports}}]);