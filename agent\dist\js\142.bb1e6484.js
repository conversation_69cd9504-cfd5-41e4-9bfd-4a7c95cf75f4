"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[142],{30142:function(t,e,a){a.r(e),a.d(e,{default:function(){return U}});var i=function(){var t=this,e=t._self._c;return e("page-header-wrapper",[e("a-card",[e("a-tabs",{attrs:{"default-active-key":"1"},on:{tabClick:t.gettabchange}},[e("a-tab-pane",{key:"1",attrs:{tab:"融资持仓单",forceRender:""}},[e("financingHold",{ref:"financingHold"})],1),e("a-tab-pane",{key:"2",attrs:{tab:"融资平仓单",forceRender:""}},[e("financingFlat",{ref:"financingFlats"})],1),e("a-tab-pane",{key:"3",attrs:{tab:"指数持仓单",forceRender:""}},[e("indexHold",{ref:"indexHold"})],1),e("a-tab-pane",{key:"4",attrs:{tab:"指数平仓单",forceRender:""}},[e("indexFlat",{ref:"indexFlat"})],1)],1)],1)],1)},n=[],o=(a(2892),function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"股票代码"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入股票代码"},model:{value:t.queryParam.stockCode,callback:function(e){t.$set(t.queryParam,"stockCode",e)},expression:"queryParam.stockCode"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"股票名称"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入股票名称"},model:{value:t.queryParam.stockName,callback:function(e){t.$set(t.queryParam,"stockName",e)},expression:"queryParam.stockName"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(e){t.queryParam.pageNum=1,t.getAllFinish(!1)}}},[t._v("选择项一键成交 ")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:function(e){t.queryParam.pageNum=1,t.getAllFinish(!0)}}},[t._v("当前页全部成交 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"selection",fn:function(a,i){return[e("a-checkbox",{attrs:{checked:t.chooseList.indexOf(i.id)>-1},on:{change:function(e){return t.onChange(i.id,e)}}})]}},{key:"stockName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.stockName))]),e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==i.stockPlate?"科创":i.stockPlate?i.stockPlate:"A股")+" ")]),e("p",[t._v("("+t._s(i.stockCode)+")")])],1)]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"now_price",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:Number(i.now_price)-i.buyOrderPrice<0?"greens":Number(i.now_price)-i.buyOrderPrice>0?"reds":""},[t._v(" "+t._s(i.now_price)+" ")])])]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[0==i.isLock?e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){t.Lockvisibledialog=!0,t.clickpositionId=i.id}},slot:"action"},[t._v("锁仓")]):e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getLockopen(i.id)}},slot:"action"},[t._v("解锁")]),e("a-divider",{attrs:{type:"vertical"}}),e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getCompulsoryclosing(i.positionSn)}},slot:"action"},[t._v("强制平仓")]),e("a-divider",{attrs:{type:"vertical"}}),1!=i.status?e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getBuyFinish(i)}},slot:"action"},[t._v("点击成交")]):t._e()]}}])}),e("a-modal",{attrs:{title:"锁仓",width:640,visible:t.Lockvisibledialog,confirmLoading:t.Lockvisibleloading},on:{ok:t.getDialogok,cancel:t.handleCancel}},[e("a-form",{ref:"createModal",attrs:{form:t.Lockvisibleform}},[e("a-form-item",[e("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["lockMsg",{rules:[{required:!0,message:"请输入锁仓原因！",whitespace:!0}]}],expression:"['lockMsg', { rules: [{ required: true, message: '请输入锁仓原因！', whitespace: true }] }]"}],attrs:{placeholder:"请输入锁仓原因！"}})],1)],1)],1),e("a-modal",{attrs:{title:"购买成交",width:640,visible:t.ConfirmTreadDialog,confirmLoading:t.ConfirmTreadDialogding},on:{ok:t.getTradeDialogok,cancel:t.handleTreadCancel}},[e("a-form",{ref:"createModal",attrs:{form:t.ConfirmTreadform}},[e("a-form-item",[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"成交比例"}},[e("a-select",{attrs:{placeholder:"请选择成交比例"},model:{value:t.buyRatio,callback:function(e){t.buyRatio=e},expression:"buyRatio"}},[e("a-select-option",{attrs:{value:5}},[t._v("5%")]),e("a-select-option",{attrs:{value:10}},[t._v("10%")]),e("a-select-option",{attrs:{value:15}},[t._v("15%")]),e("a-select-option",{attrs:{value:20}},[t._v("20%")]),e("a-select-option",{attrs:{value:25}},[t._v("25%")]),e("a-select-option",{attrs:{value:30}},[t._v("30%")]),e("a-select-option",{attrs:{value:35}},[t._v("35%")]),e("a-select-option",{attrs:{value:40}},[t._v("40%")]),e("a-select-option",{attrs:{value:45}},[t._v("45%")]),e("a-select-option",{attrs:{value:50}},[t._v("50%")]),e("a-select-option",{attrs:{value:55}},[t._v("55%")]),e("a-select-option",{attrs:{value:60}},[t._v("60%")]),e("a-select-option",{attrs:{value:65}},[t._v("65%")]),e("a-select-option",{attrs:{value:70}},[t._v("70%")]),e("a-select-option",{attrs:{value:75}},[t._v("75%")]),e("a-select-option",{attrs:{value:80}},[t._v("80%")]),e("a-select-option",{attrs:{value:85}},[t._v("85%")]),e("a-select-option",{attrs:{value:90}},[t._v("90%")]),e("a-select-option",{attrs:{value:95}},[t._v("95%")]),e("a-select-option",{attrs:{value:100}},[t._v("100%")])],1)],1),e("a-form-item",{attrs:{label:"审核状态"}},[e("a-select",{attrs:{placeholder:"请选择审核状态"},model:{value:t.oneTreadData.status,callback:function(e){t.$set(t.oneTreadData,"status",e)},expression:"oneTreadData.status"}},[e("a-select-option",{attrs:{value:1}},[t._v("成交")]),e("a-select-option",{attrs:{value:2}},[t._v("驳回")])],1)],1)],1)],1)],1)],1),e("a-modal",{attrs:{title:"购买成交",width:640,visible:t.ConfirmTreadDialogAll,confirmLoading:t.ConfirmTreadDialogdingAll},on:{ok:t.getTradeDialogokAll,cancel:t.handleTreadCancelAll}},[e("a-form",{ref:"createModal",attrs:{form:t.ConfirmTreadform}},[e("a-form-item",[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"成交比例"}},[e("a-select",{attrs:{placeholder:"请选择成交比例"},model:{value:t.buyRatio,callback:function(e){t.buyRatio=e},expression:"buyRatio"}},[e("a-select-option",{attrs:{value:5}},[t._v("5%")]),e("a-select-option",{attrs:{value:10}},[t._v("10%")]),e("a-select-option",{attrs:{value:15}},[t._v("15%")]),e("a-select-option",{attrs:{value:20}},[t._v("20%")]),e("a-select-option",{attrs:{value:25}},[t._v("25%")]),e("a-select-option",{attrs:{value:30}},[t._v("30%")]),e("a-select-option",{attrs:{value:35}},[t._v("35%")]),e("a-select-option",{attrs:{value:40}},[t._v("40%")]),e("a-select-option",{attrs:{value:45}},[t._v("45%")]),e("a-select-option",{attrs:{value:50}},[t._v("50%")]),e("a-select-option",{attrs:{value:55}},[t._v("55%")]),e("a-select-option",{attrs:{value:60}},[t._v("60%")]),e("a-select-option",{attrs:{value:65}},[t._v("65%")]),e("a-select-option",{attrs:{value:70}},[t._v("70%")]),e("a-select-option",{attrs:{value:75}},[t._v("75%")]),e("a-select-option",{attrs:{value:80}},[t._v("80%")]),e("a-select-option",{attrs:{value:85}},[t._v("85%")]),e("a-select-option",{attrs:{value:90}},[t._v("90%")]),e("a-select-option",{attrs:{value:95}},[t._v("95%")]),e("a-select-option",{attrs:{value:100}},[t._v("100%")])],1)],1)],1)],1)],1)],1)],1)}),r=[],s=(a(28706),a(74423),a(48598),a(62062),a(54554),a(9868),a(26099),a(38781),a(21699),a(47764),a(23500),a(62953),a(48408),a(75769)),l=a(55373),c=a.n(l),d={positionlist:"/agent/position/list.do",indexpositionlist:"/agent/index/position/list.do",futurespositionlist:"/agent/futures/position/list.do",positionsell:"/admin/position/sell.do",positionlock:"/admin/position/lock.do",buyOneTread:"/admin/position/updateStatus.do",buyAllTread:"admin/position/batchAudit.do",oneClickTransaction:"/admin/position/oneClickTransaction.do"};function u(t){return(0,s.Ay)({url:d.positionlist,method:"post",data:c().stringify(t)})}function m(t){return(0,s.Ay)({url:d.oneClickTransaction,method:"post",data:c().stringify(t)})}function p(t){return(0,s.Ay)({url:d.buyOneTread,method:"post",data:c().stringify(t)})}function g(t){return(0,s.Ay)({url:d.positionlock,method:"post",data:c().stringify(t)})}function f(t){return(0,s.Ay)({url:d.indexpositionlist,method:"post",data:c().stringify(t)})}function v(t){return(0,s.Ay)({url:d.positionsell,method:"post",data:c().stringify(t)})}var h=a(80157),y=a(95093),_=a.n(y),k={name:"financinghold",data:function(){var t=this;return{columns:[{title:"选择",dataIndex:"selection",align:"center",width:60,scopedSlots:{customRender:"selection"}},{title:"融资名称",dataIndex:"stockName",align:"center",width:180,scopedSlots:{customRender:"stockName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"nickName",align:"center",customRender:function(t,e,a){return"".concat(e.nickName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入价",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"现价",dataIndex:"now_price",align:"center",scopedSlots:{customRender:"now_price"}},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（股）",dataIndex:"orderNum",align:"center"},{title:"总市值",dataIndex:"orderTotalPrice",align:"center"},{title:"杠杆倍数",dataIndex:"orderLever",align:"center"},{title:"手续费",dataIndex:"orderFee",align:"center"},{title:"印花税",dataIndex:"orderSpread",align:"center"},{title:"留仓费",dataIndex:"orderStayFee",align:"center"},{title:"留仓天数",dataIndex:"orderStayDays",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?_()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:250,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0,stockCode:"",stockName:""},datalist:[],agentlist:[],agentloading:!1,agentqueryParam:{pageNum:1,pageSize:100},chooseList:[],ConfirmTreadform:this.$form.createForm(this),Lockvisibleform:this.$form.createForm(this),Lockvisibledialog:!1,Lockvisibleloading:!1,buyRatio:100,oneTreadData:{},ConfirmTreadDialogding:!1,ConfirmTreadDialog:!1,ConfirmTreadDialogAll:!1,ConfirmTreadDialogdingAll:!1}},created:function(){this.getlist()},methods:{handleTreadCancelAll:function(){this.ConfirmTreadDialogAll=!1;var t=this.$refs.createModal.form;t.resetFields()},getTradeDialogokAll:function(){var t=this,e=this,a="",i="",n=[],o=[];this.$refs.createModal.form;this.isAll?(a=e.datalist.map((function(t){return t.id})).join(","),i=e.datalist.map((function(t){return t.agentId})).join(",")):(e.datalist.forEach((function(t){e.chooseList.includes(t.id)&&(n.push(t.id),o.push(t.agentId))})),a=n.join(","),i=o.join(","));var r=e.buyRatio,s=new URLSearchParams({ids:a,status:1,agentIds:i,buyRatio:r}).toString();console.log(s),m({ids:a}).then((function(e){0==e.status?(t.ConfirmTreadDialogAll=!1,t.$message.success({content:e.data,duration:2}),t.getlist()):(t.$message.success({content:e.data,duration:2}),t.ConfirmTreadDialogdingAll=!1),t.ConfirmTreadDialogdingAll=!1})).catch((function(e){t.$message.error({content:"购买成交失败",duration:2}),t.ConfirmTreadDialogdingAll=!1}))},getAllFinish:function(t){var e=this;t&&(this.chooseList=this.datalist.map((function(t){return t.id}))),this.chooseList.length<1?this.$message.error({content:"请先选择需要成交的条目"}):m({ids:this.chooseList.join(",")}).then((function(t){0==t.status?(e.ConfirmTreadDialogAll=!1,e.$message.success({content:t.data,duration:2}),e.getlist()):(e.$message.success({content:t.data,duration:2}),e.ConfirmTreadDialogdingAll=!1),e.chooseList=[]})).catch((function(t){e.$message.error({content:"购买成交失败",duration:2})}))},onChange:function(t,e){if(e.target.checked)this.chooseList.push(t);else{var a=this.chooseList.indexOf(t);a>-1&&this.chooseList.splice(a,1)}this.chooseList.forEach((function(t){console.log("Checked values:",t)}))},getTradeDialogok:function(){var t=this,e=this,a=this.$refs.createModal.form;a.validateFields((function(a,i){a||(t.oneTreadData.buyRatio=t.buyRatio,p(t.oneTreadData).then((function(a){0==a.status?(t.ConfirmTreadDialog=!1,e.$message.success({content:a.data,duration:2}),e.getlist()):e.$message.success({content:a.data,duration:2})})).catch((function(a){e.$message.success({content:"购买成交失败",duration:2}),t.ConfirmTreadDialogding=!1})))}))},handleTreadCancel:function(){this.ConfirmTreadDialog=!1;var t=this.$refs.createModal.form;t.resetFields()},getBuyFinish:function(t){this.oneTreadData={id:t.id,status:1,userId:t.userId},this.ConfirmTreadDialog=!0},getLockopen:function(t){var e=this;this.$confirm({title:"提示",content:"确认要解锁该持仓单?",onOk:function(){var a={state:0,positionId:t};g(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.error({content:t.msg})}))},onCancel:function(){console.log("Cancel")}})},getDialogok:function(){var t=this,e=this.$refs.createModal.form;e.validateFields((function(a,i){if(!a){t.Lockvisibleloading=!0;var n={state:1,lockMsg:i.lockMsg,positionId:t.clickpositionId};g(n).then((function(a){0==a.status?(t.Lockvisibledialog=!1,t.$message.success({content:a.msg,duration:2}),e.resetFields(),t.getlist()):t.$message.error({content:a.msg}),t.Lockvisibleloading=!1})).catch((function(t){reject(t)}))}}))},handleCancel:function(){this.Lockvisibledialog=!1;var t=this.$refs.createModal.form;t.resetFields()},getCompulsoryclosing:function(t){var e=this;this.$confirm({title:"提示",content:"确认要强制平仓吗?",onOk:function(){var a={positionSn:t};v(a).then((function(t){0==t.status?(e.$message.success({content:t.msg,duration:2}),e.getlist()):e.$message.success({content:t.msg,duration:2})})).catch((function(t){}))},onCancel:function(){console.log("Cancel"),e.$message.success({content:"平仓失败",duration:2})}})},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0},this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0}},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,h.LY)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,u(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},b=k,P=a(81656),I=(0,P.A)(b,o,r,!1,null,"1c61f0b4",null),x=I.exports,S=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",{attrs:{label:"卖出时间"}},[e("a-range-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss"},on:{change:t.onChangeRangeDate},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"stockName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.stockName))]),e("a-tag",{attrs:{color:"科创"==i.stockPlate?"blue":i.stockPlate?"创业"==i.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==i.stockPlate?"科创":i.stockPlate?i.stockPlate:"A股")+" ")]),e("p",[t._v("("+t._s(i.stockCode)+")")])],1)]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getFlatdetails(i)}},slot:"action"},[t._v("查看详情")])]}}])}),e("a-modal",{attrs:{title:"融资详情",width:1e3,visible:t.finacingDialog,footer:!1},on:{cancel:function(e){t.finacingDialog=!1}}},[e("a-descriptions",{attrs:{bordered:"",title:"".concat(t.clickitem.stockName,"(").concat(t.clickitem.stockCode,")")}},[e("a-descriptions-item",{attrs:{label:"用户名称（ID）"}},[e("span",[t._v(t._s(t.clickitem.nickName)+"（"+t._s(t.clickitem.userId)+"）")])]),e("a-descriptions-item",{attrs:{label:"股票类型"}},[e("a-tag",{attrs:{color:"科创"==t.clickitem.stockPlate?"blue":t.clickitem.stockPlate?"创业"==t.clickitem.stockPlate?"pink":"purple":"orange"}},[t._v(" "+t._s("科创"==t.clickitem.stockPlate?"科创":t.clickitem.stockPlate?t.clickitem.stockPlate:"A股")+" ")])],1),e("a-descriptions-item",{attrs:{label:"账户类型"}},[e("a-tag",{attrs:{color:1==t.clickitem.positionType?"blue":"green"}},[t._v(" "+t._s(1==t.clickitem.positionType?"模拟持仓":"正式持仓")+" ")])],1),e("a-descriptions-item",{attrs:{label:"持仓ID"}},[e("span",[t._v(" "+t._s(t.clickitem.id)+" ")])]),e("a-descriptions-item",{attrs:{label:"浮动盈亏"}},[e("span",{class:t.clickitem.profitAndLose>0?"reds":t.clickitem.profitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.profitAndLose)+" ")])]),e("a-descriptions-item",{attrs:{label:"总盈亏"}},[e("span",{class:t.clickitem.allProfitAndLose>0?"reds":t.clickitem.allProfitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.allProfitAndLose)+" ")])]),t.clickitem.now_price?e("a-descriptions-item",{attrs:{label:"当前价格"}},[e("span",{class:t.clickitem.now_price-t.clickitem.buyOrderPrice>0?"reds":t.clickitem.now_price-t.clickitem.buyOrderPrice<0?"greens":""},[t._v(" "+t._s(t.clickitem.now_price)+" ")])]):t._e(),e("a-descriptions-item",{attrs:{label:"卖出价格"}},[e("span",[t._v(" "+t._s(t.clickitem.sellOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入价格"}},[e("span",[t._v(" "+t._s(t.clickitem.buyOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入数量"}},[e("span",[t._v(" "+t._s(t.clickitem.orderNum)+" ")])]),e("a-descriptions-item",{attrs:{label:"买卖方向"}},[e("a-tag",{attrs:{color:"买涨"==t.clickitem.orderDirection?"red":"green"}},[t._v(" "+t._s(t.clickitem.orderDirection)+" ")])],1),e("a-descriptions-item",{attrs:{label:"杠杆倍数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderLever)+" ")])]),e("a-descriptions-item",{attrs:{label:"总市值"}},[e("span",[t._v(" "+t._s(t.clickitem.orderTotalPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓天数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayDays)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"手续费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"印花税"}},[e("span",[t._v(" "+t._s(t.clickitem.orderSpread)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.buyOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"卖出时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.sellOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"买入订单号"}},[t._v(" "+t._s(t.clickitem.buyOrderId)+" ")]),e("a-descriptions-item",{attrs:{label:"卖出订单号"}},[t._v(" "+t._s(t.clickitem.sellOrderId)+" ")]),e("a-descriptions-item",{attrs:{label:"持仓订单号"}},[t._v(" "+t._s(t.clickitem.positionSn)+" ")]),e("a-descriptions-item",{attrs:{label:"锁仓原因"}},[t._v(" "+t._s(t.clickitem.lockMsg)+" ")])],1)],1)],1)},T=[],q={name:"financingflat",data:function(){var t=this;return{columns:[{title:"融资名称",dataIndex:"stockName",align:"center",width:180,scopedSlots:{customRender:"stockName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"nickName",align:"center",customRender:function(t,e,a){return"".concat(e.nickName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入价",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"卖出价",dataIndex:"sellOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（股）",dataIndex:"orderNum",align:"center"},{title:"总市值",dataIndex:"orderTotalPrice",align:"center"},{title:"杠杆倍数",dataIndex:"orderLever",align:"center"},{title:"手续费",dataIndex:"orderFee",align:"center"},{title:"印花税",dataIndex:"orderSpread",align:"center"},{title:"留仓费",dataIndex:"orderStayFee",align:"center"},{title:"留仓天数",dataIndex:"orderStayDays",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?_()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"卖出时间",dataIndex:"sellOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?_()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},datalist:[],agentlist:[],agentloading:!1,times:[],finacingDialog:!1,clickitem:{},agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getFlatdetails:function(t){this.clickitem=t,this.finacingDialog=!0},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},this.times=[],this.getlist()},onChangeRangeDate:function(t,e){this.queryParam.beginTime=e[0],this.queryParam.endTime=e[1]},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},this.times=[]},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,h.LY)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this,e=this;this.loading=!0,u(this.queryParam).then((function(a){t.datalist=a.data.list,t.pagination.total=a.data.total,setTimeout((function(){e.loading=!1}),500)}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},D=q,C=(0,P.A)(D,S,T,!1,null,"1cd41fd8",null),w=C.exports,N=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"indexName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.indexName))]),e("p",[t._v("("+t._s(i.indexCode)+")")])])]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"now_price",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:Number(i.now_price)-i.buyOrderPrice<0?"greens":Number(i.now_price)-i.buyOrderPrice>0?"reds":""},[t._v(" "+t._s(Number(i.now_price).toFixed(2))+" ")])])]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}}])})],1)},L=[],A={name:"index-hold",data:function(){var t=this;return{columns:[{title:"指数名称",dataIndex:"indexName",align:"center",width:180,scopedSlots:{customRender:"indexName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"realName",align:"center",customRender:function(t,e,a){return"".concat(e.realName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入点数",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"最新点数",dataIndex:"now_price",align:"center",scopedSlots:{customRender:"now_price"}},{title:"保证金",dataIndex:"allDepositAmt",align:"center"},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（手）",dataIndex:"orderNum",align:"center"},{title:"点浮动价",dataIndex:"eachPoint",align:"center"},{title:"双边手续费",dataIndex:"orderFee",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?_()(t).format("YYYY-MM-DD HH:mm:ss"):""}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0},datalist:[],agentlist:[],agentloading:!1,Lockvisibledialig:!1,Lockvisibleloading:!1,Lockvisibleform:this.$form.createForm(this),clickpositionId:"",agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0},this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:0}},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,h.LY)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,f(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},R=A,$=(0,P.A)(R,N,L,!1,null,"243c4eb8",null),z=$.exports,O=function(){var t=this,e=t._self._c;return e("div",[e("a-card",{attrs:{bordered:!1}},[e("div",{staticClass:"table-page-search-wrapper"},[e("a-form",{attrs:{layout:"inline"}},[e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓类型"}},[e("a-select",{attrs:{placeholder:"请选择持仓类型"},model:{value:t.queryParam.positionType,callback:function(e){t.$set(t.queryParam,"positionType",e)},expression:"queryParam.positionType"}},[e("a-select-option",{attrs:{value:""}},[t._v("全部")]),e("a-select-option",{attrs:{value:0}},[t._v("正式持仓")]),e("a-select-option",{attrs:{value:1}},[t._v("模拟持仓")])],1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"下级代理"}},[e("a-select",{attrs:{placeholder:"请选择下级代理",loading:t.agentloading},on:{focus:t.getagentlist},model:{value:t.queryParam.agentId,callback:function(e){t.$set(t.queryParam,"agentId",e)},expression:"queryParam.agentId"}},t._l(t.agentlist,(function(a,i){return e("a-select-option",{key:i,attrs:{value:a.id}},[t._v(" "+t._s(a.agentName)+" ")])})),1)],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"用户Id"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入用户Id"},model:{value:t.queryParam.userId,callback:function(e){t.$set(t.queryParam,"userId",e)},expression:"queryParam.userId"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",{attrs:{label:"持仓订单号"}},[e("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入持仓订单号"},model:{value:t.queryParam.positionSn,callback:function(e){t.$set(t.queryParam,"positionSn",e)},expression:"queryParam.positionSn"}})],1)],1)],1),e("a-row",{attrs:{gutter:48}},[e("a-col",{attrs:{md:12,lg:8,sm:24}},[e("a-form-item",{attrs:{label:"卖出时间"}},[e("a-range-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss"},on:{change:t.onChangeRangeDate},model:{value:t.times,callback:function(e){t.times=e},expression:"times"}})],1)],1),e("a-col",{attrs:{md:12,lg:6,sm:24}},[e("a-form-item",[e("span",{staticClass:"table-page-search-submitButtons"},[e("a-button",{attrs:{icon:"redo"},on:{click:t.getqueryParam}},[t._v(" 重置")]),e("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"search"},on:{click:function(e){t.queryParam.pageNum=1,t.getlist()}}},[t._v("查询 ")])],1)])],1)],1)],1)],1)]),e("a-table",{attrs:{bordered:"",loading:t.loading,pagination:t.pagination,columns:t.columns,"data-source":t.datalist,rowKey:"id",scroll:{x:2800}},scopedSlots:t._u([{key:"indexName",fn:function(a,i){return e("span",{},[[e("div",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(i.indexName))]),e("p",[t._v("("+t._s(i.indexCode)+")")])])]],2)}},{key:"positionType",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:1==i.positionType?"blue":"green"}},[t._v(" "+t._s(1==i.positionType?"模拟持仓":"正式持仓")+" ")])],1)]],2)}},{key:"orderDirection",fn:function(a,i){return e("span",{},[[e("div",[e("a-tag",{attrs:{color:"买涨"==i.orderDirection?"red":"green"}},[t._v(" "+t._s(i.orderDirection)+" ")])],1)]],2)}},{key:"now_price",fn:function(a,i){return e("span",{},[[e("div",[e("p",{class:Number(i.now_price)-i.buyOrderPrice<0?"greens":Number(i.now_price)-i.buyOrderPrice>0?"reds":""},[t._v(" "+t._s(Number(i.now_price).toFixed(2))+" ")])])]],2)}},{key:"profitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"allProfitAndLose",fn:function(a){return e("span",{},[[e("div",[e("p",{class:a<0?"greens":a>0?"reds":""},[t._v(" "+t._s(a)+" ")])])]],2)}},{key:"action",fn:function(a,i){return[e("a",{attrs:{slot:"action",href:"javascript:;"},on:{click:function(e){return t.getIndexdetails(i)}},slot:"action"},[t._v("查看详情")])]}}])}),e("a-modal",{attrs:{title:"指数详情",width:1e3,visible:t.indexDialog,footer:!1},on:{cancel:function(e){t.indexDialog=!1}}},[e("a-descriptions",{attrs:{bordered:"",title:"".concat(t.clickitem.indexName,"(").concat(t.clickitem.indexCode,")")}},[e("a-descriptions-item",{attrs:{label:"用户名称（ID）"}},[e("span",[t._v(t._s(t.clickitem.realName)+"（"+t._s(t.clickitem.userId)+"）")])]),e("a-descriptions-item",{attrs:{label:"账户类型"}},[e("a-tag",{attrs:{color:1==t.clickitem.positionType?"blue":"green"}},[t._v(" "+t._s(1==t.clickitem.positionType?"模拟持仓":"正式持仓")+" ")])],1),e("a-descriptions-item",{attrs:{label:"持仓ID"}},[e("span",[t._v(" "+t._s(t.clickitem.id)+" ")])]),e("a-descriptions-item",{attrs:{label:"浮动盈亏"}},[e("span",{class:t.clickitem.profitAndLose>0?"reds":t.clickitem.profitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.profitAndLose)+" ")])]),e("a-descriptions-item",{attrs:{label:"总盈亏"}},[e("span",{class:t.clickitem.allProfitAndLose>0?"reds":t.clickitem.allProfitAndLose<0?"greens":""},[t._v(" "+t._s(t.clickitem.allProfitAndLose)+" ")])]),t.clickitem.now_price?e("a-descriptions-item",{attrs:{label:"当前价格"}},[e("span",{class:t.clickitem.now_price-t.clickitem.buyOrderPrice>0?"reds":t.clickitem.now_price-t.clickitem.buyOrderPrice<0?"greens":""},[t._v(" "+t._s(t.clickitem.now_price)+" ")])]):t._e(),e("a-descriptions-item",{attrs:{label:"卖出点数"}},[e("span",[t._v(" "+t._s(t.clickitem.sellOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入点数"}},[e("span",[t._v(" "+t._s(t.clickitem.buyOrderPrice)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入数量（手）"}},[e("span",[t._v(" "+t._s(t.clickitem.orderNum)+" ")])]),e("a-descriptions-item",{attrs:{label:"买卖方向"}},[e("a-tag",{attrs:{color:"买涨"==t.clickitem.orderDirection?"red":"green"}},[t._v(" "+t._s(t.clickitem.orderDirection)+" ")])],1),e("a-descriptions-item",{attrs:{label:"保证金"}},[e("span",[t._v(" "+t._s(t.clickitem.allDepositAmt)+" ")])]),e("a-descriptions-item",{attrs:{label:"点浮动价"}},[e("span",[t._v(" "+t._s(t.clickitem.eachPoint)+" ")])]),e("a-descriptions-item",{attrs:{label:"留仓天数"}},[e("span",[t._v(" "+t._s(t.clickitem.orderStayDays)+" ")])]),e("a-descriptions-item",{attrs:{label:"手续费"}},[e("span",[t._v(" "+t._s(t.clickitem.orderFee)+" ")])]),e("a-descriptions-item",{attrs:{label:"买入时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.buyOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"卖出时间"}},[e("span",[t._v(" "+t._s(t._f("moment")(t.clickitem.sellOrderTime))+" ")])]),e("a-descriptions-item",{attrs:{label:"持仓订单号"}},[t._v(" "+t._s(t.clickitem.positionSn)+" ")]),e("a-descriptions-item",{attrs:{label:"锁仓原因"}},[t._v(" "+t._s(t.clickitem.lockMsg)+" ")])],1)],1)],1)},F=[],Y={name:"index-flat",data:function(){var t=this;return{columns:[{title:"指数名称",dataIndex:"indexName",align:"center",width:180,scopedSlots:{customRender:"indexName"}},{title:"账户类型",dataIndex:"positionType",align:"center",scopedSlots:{customRender:"positionType"}},{title:"用户名称（ID）",dataIndex:"realName",align:"center",customRender:function(t,e,a){return"".concat(e.realName,"（").concat(e.userId,"）")}},{title:"持仓订单号（ID）",dataIndex:"positionSn",align:"center",customRender:function(t,e,a){return"".concat(e.positionSn,"（").concat(e.id,"）")}},{title:"买卖方向",dataIndex:"orderDirection",align:"center",scopedSlots:{customRender:"orderDirection"}},{title:"买入点数",dataIndex:"buyOrderPrice",align:"center",customRender:function(t,e,a){return t.toFixed(2)}},{title:"卖出点数",dataIndex:"sellOrderPrice",align:"center",scopedSlots:{customRender:"sellOrderPrice"}},{title:"保证金",dataIndex:"allDepositAmt",align:"center"},{title:"浮动盈亏",dataIndex:"profitAndLose",align:"center",scopedSlots:{customRender:"profitAndLose"}},{title:"总盈亏",dataIndex:"allProfitAndLose",align:"center",scopedSlots:{customRender:"allProfitAndLose"}},{title:"数量（手）",dataIndex:"orderNum",align:"center"},{title:"点浮动价",dataIndex:"eachPoint",align:"center"},{title:"手续费",dataIndex:"orderFee",align:"center"},{title:"锁定原因",dataIndex:"lockMsg",align:"center"},{title:"买入时间",dataIndex:"buyOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?_()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"卖出时间",dataIndex:"sellOrderTime",align:"center",width:180,customRender:function(t,e,a){return t?_()(t).format("YYYY-MM-DD HH:mm:ss"):""}},{title:"操作",key:"action",align:"center",fixed:"right",width:150,scopedSlots:{customRender:"action"}}],pagination:{total:0,pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50","100"],onShowSizeChange:function(e,a){return t.onSizeChange(e,a)},onChange:function(e,a){return t.onPageChange(e,a)},showTotal:function(t){return"共有 ".concat(t," 条数据")}},loading:!1,queryParam:{pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},datalist:[],agentlist:[],agentloading:!1,times:[],indexDialog:!1,clickitem:{},agentqueryParam:{pageNum:1,pageSize:100}}},created:function(){},methods:{getIndexdetails:function(t){this.clickitem=t,this.indexDialog=!0},getinit:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1,beginTime:"",endTime:""},this.times=[],this.getlist()},getqueryParam:function(){this.queryParam={pageNum:1,pageSize:10,positionType:"",agentId:void 0,userId:"",positionSn:"",state:1},this.times=[]},onChangeRangeDate:function(t,e){this.queryParam.beginTime=e[0],this.queryParam.endTime=e[1]},getagentlist:function(){var t=this,e=this;this.agentloading=!0,(0,h.LY)(this.agentqueryParam).then((function(a){t.agentlist=a.data.list,setTimeout((function(){e.agentloading=!1}),500)}))},getlist:function(){var t=this;this.loading=!0,f(this.queryParam).then((function(e){t.datalist=e.data.list,t.pagination.total=e.data.total,t.loading=!1}))},onPageChange:function(t,e){this.queryParam.pageNum=t,this.getlist()},onSizeChange:function(t,e){this.queryParam.pageNum=t,this.queryParam.pageSize=e,this.getlist()}}},M=Y,H=(0,P.A)(M,O,F,!1,null,"7b8fd6cc",null),j=H.exports,B={name:"financing",components:{financingHold:x,financingFlat:w,indexHold:z,indexFlat:j},data:function(){return{}},methods:{gettabchange:function(t){1==t?this.$refs.financingHold.getinit():2==t?this.$refs.financingFlats.getinit():3==t?this.$refs.indexHold.getinit():4==t?this.$refs.indexFlat.getinit():5==t?this.$refs.futuresHold.getinit():6==t&&this.$refs.futuresFlat.getinit()}}},K=B,E=(0,P.A)(K,i,n,!1,null,"2cff8da5",null),U=E.exports}}]);