(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[1251],{11251:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return p}});var r=function(){var t=this,e=t._self._c;return e("page-header-wrapper",[e("a-form",{ref:"addUserform",staticClass:"form",attrs:{form:t.addUserform}},[e("a-card",{staticClass:"card",attrs:{title:"A股",bordered:!1,loading:t.loading}},[e("span",{attrs:{slot:"extra"},slot:"extra"},[t._v("状态为开启即表示用户可以进行该产品的交易")]),e("a-row",{staticClass:"form-row",attrs:{gutter:48}},[e("a-col",{attrs:{md:8,lg:8,sm:12}},[e("a-form-item",{attrs:{label:"融资融券交易",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-switch",{directives:[{name:"decorator",rawName:"v-decorator",value:["stockDisplay",{valuePropName:"checked"}],expression:"['stockDisplay', { valuePropName: 'checked' }]"}]},[e("a-icon",{attrs:{slot:"checkedChildren",type:"check"},slot:"checkedChildren"}),e("a-icon",{attrs:{slot:"unCheckedChildren",type:"close"},slot:"unCheckedChildren"})],1)],1)],1),e("a-col",{attrs:{md:8,lg:8,sm:12}},[e("a-form-item",{attrs:{label:"科创板交易",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-switch",{directives:[{name:"decorator",rawName:"v-decorator",value:["kcStockDisplay",{valuePropName:"checked"}],expression:"['kcStockDisplay', { valuePropName: 'checked' }]"}]},[e("a-icon",{attrs:{slot:"checkedChildren",type:"check"},slot:"checkedChildren"}),e("a-icon",{attrs:{slot:"unCheckedChildren",type:"close"},slot:"unCheckedChildren"})],1)],1)],1),e("a-col",{attrs:{md:8,lg:8,sm:12}},[e("a-form-item",{attrs:{label:"指数交易",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-switch",{directives:[{name:"decorator",rawName:"v-decorator",value:["indexDisplay",{valuePropName:"checked"}],expression:"['indexDisplay', { valuePropName: 'checked' }]"}]},[e("a-icon",{attrs:{slot:"checkedChildren",type:"check"},slot:"checkedChildren"}),e("a-icon",{attrs:{slot:"unCheckedChildren",type:"close"},slot:"unCheckedChildren"})],1)],1)],1)],1)],1),e("a-card",{staticClass:"card",attrs:{title:"用户实名认证",bordered:!1,loading:t.loading}},[e("span",{attrs:{slot:"extra"},slot:"extra"},[t._v("状态为开启即表示用户实名认证后可以进行交易，关闭则不需要实名认证就可以正常交易")]),e("a-row",{staticClass:"form-row",attrs:{gutter:48}},[e("a-col",{attrs:{md:8,lg:8,sm:12}},[e("a-form-item",{attrs:{label:"实名认证",labelCol:t.labelCol,wrapperCol:t.wrapperCol}},[e("a-switch",{directives:[{name:"decorator",rawName:"v-decorator",value:["realNameDisplay",{valuePropName:"checked"}],expression:"['realNameDisplay', { valuePropName: 'checked' }]"}]},[e("a-icon",{attrs:{slot:"checkedChildren",type:"check"},slot:"checkedChildren"}),e("a-icon",{attrs:{slot:"unCheckedChildren",type:"close"},slot:"unCheckedChildren"})],1)],1)],1)],1)],1)],1),e("div",{staticClass:"bottomfixed"},[e("div",{staticStyle:{float:"right"}},[e("a-button",{attrs:{type:"primary",loading:t.addUserDialogloading},on:{click:t.OkaddUserdialog}},[t._v(" 保存当前设置 ")])],1)])],1)},n=[],i=(a(26099),a(23500),a(49717)),o=a(91863),d=a.n(o),l={name:"productsetting",data:function(){return{addUserform:this.$form.createForm(this),loading:!1,fields:["stockDisplay","kcStockDisplay","indexDisplay","futuresDisplay","realNameDisplay","fundsDisplay","delayDisplay","expandDisplay","marginDisplay","endDisplay"],labelCol:{xs:{span:10},sm:{span:10},md:{span:10}},wrapperCol:{xs:{span:14},sm:{span:14},md:{span:14}},addUserDialogloading:!1,details:{}}},mounted:function(){this.getdetail()},methods:{OkaddUserdialog:function(){var t=this,e=this.$refs.addUserform.form;e.validateFields((function(e,a){e||(t.addUserDialogloading=!0,t.loading=!0,a.id=t.details.id,(0,i.xi)(a).then((function(e){0==e.status?(t.$message.success({content:e.msg,duration:2}),t.getdetail()):t.$message.error({content:e.msg}),t.addUserDialogloading=!1})))}))},getdetail:function(){var t=this,e=this;this.loading=!0,(0,i.sE)().then((function(a){t.details=a.data,t.fields.forEach((function(e){return t.addUserform.getFieldDecorator(e)})),t.addUserform.setFieldsValue(d()(a.data,t.fields)),setTimeout((function(){e.loading=!1}),500)}))}}},s=l,c=a(81656),u=(0,c.A)(s,r,n,!1,null,"dfbe079e",null),p=u.exports},49717:function(t,e,a){"use strict";a.d(e,{FJ:function(){return p},IU:function(){return c},J5:function(){return m},Up:function(){return u},WU:function(){return s},WY:function(){return f},sE:function(){return d},xi:function(){return l},zX:function(){return h}});var r=a(75769),n=a(55373),i=a.n(n),o={getProductSetting:"/api/admin/getProductSetting.do",productupdate:"/admin/product/update.do",admingetSetting:"/api/admin/getSetting.do",setupdate:"/admin/set/update.do",admingetIndexSetting:"/api/admin/getIndexSetting.do",siteindexupdate:"/admin/site/index/update.do",admingetFuturesSetting:"/api/admin/getFuturesSetting.do",sitefuturesupdate:"/admin/site/futures/update.do",admingetSiteSpreadList:"/api/admin/getSiteSpreadList.do",adminaddSiteSpread:"/api/admin/addSiteSpread.do",adminupdateSiteSpread:"/api/admin/updateSiteSpread.do"};function d(t){return(0,r.Ay)({url:o.getProductSetting,method:"post",data:i().stringify(t)})}function l(t){return(0,r.Ay)({url:o.productupdate,method:"post",data:i().stringify(t)})}function s(t){return(0,r.Ay)({url:o.admingetSetting,method:"post",data:i().stringify(t)})}function c(t){return(0,r.Ay)({url:o.setupdate,method:"post",data:i().stringify(t)})}function u(t){return(0,r.Ay)({url:o.admingetIndexSetting,method:"post",data:i().stringify(t)})}function p(t){return(0,r.Ay)({url:o.siteindexupdate,method:"post",data:i().stringify(t)})}function f(t){return(0,r.Ay)({url:o.admingetSiteSpreadList,method:"post",data:i().stringify(t)})}function m(t){return(0,r.Ay)({url:o.adminaddSiteSpread,method:"post",data:i().stringify(t)})}function h(t){return(0,r.Ay)({url:o.adminupdateSiteSpread,method:"post",data:i().stringify(t)})}},91863:function(t,e,a){var r=1/0,n=9007199254740991,i="[object Arguments]",o="[object Function]",d="[object GeneratorFunction]",l="[object Symbol]",s="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,c="object"==typeof self&&self&&self.Object===Object&&self,u=s||c||Function("return this")();function p(t,e,a){switch(a.length){case 0:return t.call(e);case 1:return t.call(e,a[0]);case 2:return t.call(e,a[0],a[1]);case 3:return t.call(e,a[0],a[1],a[2])}return t.apply(e,a)}function f(t,e){var a=-1,r=t?t.length:0,n=Array(r);while(++a<r)n[a]=e(t[a],a,t);return n}function m(t,e){var a=-1,r=e.length,n=t.length;while(++a<r)t[n+a]=e[a];return t}var h=Object.prototype,g=h.hasOwnProperty,y=h.toString,C=u.Symbol,v=h.propertyIsEnumerable,k=C?C.isConcatSpreadable:void 0,b=Math.max;function S(t,e,a,r,n){var i=-1,o=t.length;a||(a=A),n||(n=[]);while(++i<o){var d=t[i];e>0&&a(d)?e>1?S(d,e-1,a,r,n):m(n,d):r||(n[n.length]=d)}return n}function w(t,e){return t=Object(t),x(t,e,(function(e,a){return a in t}))}function x(t,e,a){var r=-1,n=e.length,i={};while(++r<n){var o=e[r],d=t[o];a(d,o)&&(i[o]=d)}return i}function D(t,e){return e=b(void 0===e?t.length-1:e,0),function(){var a=arguments,r=-1,n=b(a.length-e,0),i=Array(n);while(++r<n)i[r]=a[e+r];r=-1;var o=Array(e+1);while(++r<e)o[r]=a[r];return o[e]=i,p(t,this,o)}}function A(t){return j(t)||U(t)||!!(k&&t&&t[k])}function N(t){if("string"==typeof t||$(t))return t;var e=t+"";return"0"==e&&1/t==-r?"-0":e}function U(t){return F(t)&&g.call(t,"callee")&&(!v.call(t,"callee")||y.call(t)==i)}var j=Array.isArray;function P(t){return null!=t&&_(t.length)&&!O(t)}function F(t){return E(t)&&P(t)}function O(t){var e=I(t)?y.call(t):"";return e==o||e==d}function _(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=n}function I(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function E(t){return!!t&&"object"==typeof t}function $(t){return"symbol"==typeof t||E(t)&&y.call(t)==l}var L=D((function(t,e){return null==t?{}:w(t,f(S(e,1),N))}));t.exports=L}}]);